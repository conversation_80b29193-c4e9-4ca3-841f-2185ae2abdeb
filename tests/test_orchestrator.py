"""
Test suite for JobOrchestrator.

Tests job orchestration, agent coordination, and workflow management.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
import tempfile
from pathlib import Path

from src.core.orchestrator import JobOrchestrator
from src.models.job_manifest import JobManifest, DocumentMetadata, JobState, AgentState
from src.database.models import JobRepository


class MockAgent:
    """Mock agent for testing"""
    
    def __init__(self, name: str, should_fail: bool = False, processing_time: float = 0.1):
        self.name = name
        self.should_fail = should_fail
        self.processing_time = processing_time
        self.process_called = False
        self.validate_called = False
    
    async def process(self, job_manifest: JobManifest):
        """Mock process method"""
        self.process_called = True
        await asyncio.sleep(self.processing_time)
        
        if self.should_fail:
            raise Exception(f"Mock failure in {self.name}")
        
        return {"status": "success", "agent": self.name}
    
    def validate_inputs(self, job_manifest: JobManifest) -> bool:
        """Mock validation method"""
        self.validate_called = True
        return not self.should_fail
    
    def estimate_processing_time(self, job_manifest: JobManifest) -> float:
        """Mock time estimation"""
        return self.processing_time


class TestJobOrchestrator:
    """Test JobOrchestrator functionality"""
    
    @pytest.fixture
    async def orchestrator(self):
        """Create orchestrator for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = {
                "database": {"path": str(Path(temp_dir) / "test.db")},
                "system": {"data_directory": temp_dir}
            }
            
            orchestrator = JobOrchestrator(config)
            await orchestrator.initialize()
            
            yield orchestrator
            
            await orchestrator.shutdown()
    
    @pytest.fixture
    def sample_job(self):
        """Create sample job for testing"""
        documents = [
            DocumentMetadata(
                filename="test.pdf",
                file_path="/path/to/test.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="abc123"
            )
        ]
        
        return JobManifest.create_new(
            documents=documents,
            title="Test Job",
            priority=5
        )
    
    async def test_orchestrator_initialization(self, orchestrator):
        """Test orchestrator initialization"""
        assert orchestrator.job_repository is not None
        assert orchestrator.resource_manager is not None
        assert len(orchestrator.agents) == 9  # All 9 agents should be registered
        assert orchestrator.max_concurrent_jobs > 0
    
    async def test_submit_job(self, orchestrator, sample_job):
        """Test job submission"""
        # Submit job
        await orchestrator.submit_job(sample_job)
        
        # Verify job was stored
        stored_job = await orchestrator.job_repository.get_job(sample_job.job_id)
        assert stored_job is not None
        assert stored_job.job_id == sample_job.job_id
        assert stored_job.state == JobState.PENDING
    
    async def test_job_processing_workflow(self, orchestrator, sample_job):
        """Test complete job processing workflow"""
        # Mock all agents to succeed quickly
        mock_agents = {}
        for agent_name in orchestrator.agents.keys():
            mock_agent = MockAgent(agent_name, should_fail=False, processing_time=0.01)
            mock_agents[agent_name] = mock_agent
            orchestrator.agents[agent_name] = mock_agent
        
        # Submit and process job
        await orchestrator.submit_job(sample_job)
        await orchestrator.process_job(sample_job.job_id)
        
        # Verify job completed successfully
        completed_job = await orchestrator.job_repository.get_job(sample_job.job_id)
        assert completed_job.state == JobState.COMPLETED
        assert completed_job.progress_percentage == 100.0
        
        # Verify all agents were called
        for mock_agent in mock_agents.values():
            assert mock_agent.process_called
            assert mock_agent.validate_called
    
    async def test_job_failure_handling(self, orchestrator, sample_job):
        """Test job failure handling"""
        # Mock agents - make one fail
        mock_agents = {}
        for i, agent_name in enumerate(orchestrator.agents.keys()):
            should_fail = (i == 2)  # Fail the third agent
            mock_agent = MockAgent(agent_name, should_fail=should_fail, processing_time=0.01)
            mock_agents[agent_name] = mock_agent
            orchestrator.agents[agent_name] = mock_agent
        
        # Submit and process job
        await orchestrator.submit_job(sample_job)
        
        # Job should fail during processing
        with pytest.raises(Exception):
            await orchestrator.process_job(sample_job.job_id)
        
        # Verify job failed
        failed_job = await orchestrator.job_repository.get_job(sample_job.job_id)
        assert failed_job.state == JobState.FAILED
        assert len(failed_job.error_log) > 0
    
    async def test_job_cancellation(self, orchestrator, sample_job):
        """Test job cancellation"""
        # Submit job
        await orchestrator.submit_job(sample_job)
        
        # Cancel job
        success = await orchestrator.cancel_job(sample_job.job_id)
        assert success
        
        # Verify job was cancelled
        cancelled_job = await orchestrator.job_repository.get_job(sample_job.job_id)
        assert cancelled_job.state == JobState.CANCELLED
    
    async def test_concurrent_job_processing(self, orchestrator):
        """Test concurrent job processing"""
        # Create multiple jobs
        jobs = []
        for i in range(3):
            documents = [
                DocumentMetadata(
                    filename=f"test{i}.pdf",
                    file_path=f"/path/to/test{i}.pdf",
                    file_size=1024,
                    mime_type="application/pdf",
                    checksum=f"abc{i}"
                )
            ]
            
            job = JobManifest.create_new(
                documents=documents,
                title=f"Test Job {i}",
                priority=5
            )
            jobs.append(job)
        
        # Mock agents to succeed quickly
        for agent_name in orchestrator.agents.keys():
            mock_agent = MockAgent(agent_name, should_fail=False, processing_time=0.01)
            orchestrator.agents[agent_name] = mock_agent
        
        # Submit all jobs
        for job in jobs:
            await orchestrator.submit_job(job)
        
        # Process jobs concurrently
        tasks = [orchestrator.process_job(job.job_id) for job in jobs]
        await asyncio.gather(*tasks)
        
        # Verify all jobs completed
        for job in jobs:
            completed_job = await orchestrator.job_repository.get_job(job.job_id)
            assert completed_job.state == JobState.COMPLETED
    
    async def test_job_priority_handling(self, orchestrator):
        """Test job priority handling"""
        # Create jobs with different priorities
        high_priority_job = JobManifest.create_new(
            documents=[DocumentMetadata(
                filename="high.pdf",
                file_path="/path/to/high.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="high"
            )],
            title="High Priority Job",
            priority=1  # High priority
        )
        
        low_priority_job = JobManifest.create_new(
            documents=[DocumentMetadata(
                filename="low.pdf",
                file_path="/path/to/low.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="low"
            )],
            title="Low Priority Job",
            priority=10  # Low priority
        )
        
        # Submit jobs (low priority first)
        await orchestrator.submit_job(low_priority_job)
        await orchestrator.submit_job(high_priority_job)
        
        # Get job queue (should be ordered by priority)
        pending_jobs = await orchestrator.job_repository.get_pending_jobs()
        
        # High priority job should come first
        assert pending_jobs[0].job_id == high_priority_job.job_id
        assert pending_jobs[1].job_id == low_priority_job.job_id
    
    async def test_resource_management_integration(self, orchestrator, sample_job):
        """Test integration with resource management"""
        # Mock resource manager to limit resources
        with patch.object(orchestrator.resource_manager, 'allocate_resources') as mock_allocate:
            # Mock successful resource allocation
            mock_context = AsyncMock()
            mock_allocate.return_value.__aenter__ = AsyncMock(return_value=mock_context)
            mock_allocate.return_value.__aexit__ = AsyncMock(return_value=None)
            
            # Mock agents
            for agent_name in orchestrator.agents.keys():
                mock_agent = MockAgent(agent_name, should_fail=False, processing_time=0.01)
                orchestrator.agents[agent_name] = mock_agent
            
            # Submit and process job
            await orchestrator.submit_job(sample_job)
            await orchestrator.process_job(sample_job.job_id)
            
            # Verify resource allocation was called
            assert mock_allocate.called
    
    async def test_job_state_persistence(self, orchestrator, sample_job):
        """Test job state persistence during processing"""
        # Mock agents with longer processing time
        for agent_name in orchestrator.agents.keys():
            mock_agent = MockAgent(agent_name, should_fail=False, processing_time=0.05)
            orchestrator.agents[agent_name] = mock_agent
        
        # Submit job
        await orchestrator.submit_job(sample_job)
        
        # Start processing in background
        process_task = asyncio.create_task(orchestrator.process_job(sample_job.job_id))
        
        # Wait a bit and check intermediate state
        await asyncio.sleep(0.02)
        
        # Job should be running
        running_job = await orchestrator.job_repository.get_job(sample_job.job_id)
        assert running_job.state == JobState.RUNNING
        
        # Wait for completion
        await process_task
        
        # Job should be completed
        completed_job = await orchestrator.job_repository.get_job(sample_job.job_id)
        assert completed_job.state == JobState.COMPLETED
    
    async def test_error_recovery(self, orchestrator, sample_job):
        """Test error recovery mechanisms"""
        # Mock agents - make one fail initially, then succeed on retry
        class RetryableAgent(MockAgent):
            def __init__(self, name):
                super().__init__(name, should_fail=True)
                self.attempt_count = 0
            
            async def process(self, job_manifest):
                self.attempt_count += 1
                if self.attempt_count == 1:
                    raise Exception("First attempt fails")
                else:
                    # Succeed on retry
                    self.should_fail = False
                    return await super().process(job_manifest)
        
        # Replace one agent with retryable version
        orchestrator.agents["content_extraction"] = RetryableAgent("content_extraction")
        
        # Mock other agents to succeed
        for agent_name in orchestrator.agents.keys():
            if agent_name != "content_extraction":
                orchestrator.agents[agent_name] = MockAgent(agent_name, should_fail=False)
        
        # Enable retry logic in orchestrator
        orchestrator.max_retries = 2
        
        # Submit and process job
        await orchestrator.submit_job(sample_job)
        
        # Job should eventually succeed after retry
        await orchestrator.process_job(sample_job.job_id)
        
        completed_job = await orchestrator.job_repository.get_job(sample_job.job_id)
        assert completed_job.state == JobState.COMPLETED
    
    async def test_orchestrator_shutdown(self, orchestrator):
        """Test orchestrator shutdown"""
        # Submit a job
        sample_job = JobManifest.create_new(
            documents=[DocumentMetadata(
                filename="test.pdf",
                file_path="/path/to/test.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="abc123"
            )],
            title="Shutdown Test Job"
        )
        
        await orchestrator.submit_job(sample_job)
        
        # Shutdown orchestrator
        await orchestrator.shutdown()
        
        # Verify shutdown completed without errors
        assert True  # If we get here, shutdown was successful


class TestJobOrchestratorEdgeCases:
    """Test edge cases and error conditions"""
    
    async def test_invalid_job_submission(self):
        """Test handling of invalid job submissions"""
        orchestrator = JobOrchestrator()
        
        # Try to submit None job
        with pytest.raises(ValueError):
            await orchestrator.submit_job(None)
    
    async def test_nonexistent_job_processing(self, orchestrator):
        """Test processing nonexistent job"""
        # Try to process job that doesn't exist
        with pytest.raises(ValueError):
            await orchestrator.process_job("nonexistent-job-id")
    
    async def test_duplicate_job_submission(self, orchestrator, sample_job):
        """Test duplicate job submission"""
        # Submit job twice
        await orchestrator.submit_job(sample_job)
        
        # Second submission should be handled gracefully
        await orchestrator.submit_job(sample_job)
        
        # Should still have only one job
        stored_job = await orchestrator.job_repository.get_job(sample_job.job_id)
        assert stored_job is not None


if __name__ == "__main__":
    pytest.main([__file__])
