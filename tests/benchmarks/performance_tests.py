"""
Performance Benchmarks for Study Guide Generator

Comprehensive performance testing suite to measure system performance
across different hardware configurations and workloads.
"""

import asyncio
import time
import statistics
import psutil
import json
from pathlib import Path
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import tempfile
import pytest

from src.models.job_manifest import JobManifest, DocumentMetadata
from src.core.orchestrator import JobOrchestrator
from src.config.system_config import get_config, initialize_config
from src.core.resource_manager import get_resource_manager, initialize_resource_manager


@dataclass
class BenchmarkResult:
    """Benchmark result data structure"""
    test_name: str
    duration_seconds: float
    memory_peak_mb: float
    cpu_avg_percent: float
    throughput_docs_per_second: float
    success_rate: float
    error_count: int
    additional_metrics: Dict[str, Any]


@dataclass
class SystemSnapshot:
    """System resource snapshot"""
    timestamp: float
    memory_used_mb: float
    memory_percent: float
    cpu_percent: float
    disk_io_read_mb: float
    disk_io_write_mb: float


class PerformanceBenchmark:
    """
    Performance benchmark suite for the study guide generator system.
    
    Tests various scenarios:
    - Single document processing
    - Batch document processing
    - Concurrent job processing
    - Memory usage patterns
    - CPU utilization
    - I/O performance
    """
    
    def __init__(self, output_dir: Path = None):
        self.output_dir = output_dir or Path("benchmark_results")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # System monitoring
        self.system_snapshots: List[SystemSnapshot] = []
        self.monitoring_active = False
        self.monitoring_task = None
        
        # Test data
        self.test_documents = self._create_test_documents()
        
        # Results storage
        self.benchmark_results: List[BenchmarkResult] = []
    
    def _create_test_documents(self) -> List[DocumentMetadata]:
        """Create test documents of various sizes"""
        documents = []
        
        # Small documents (1-10 KB)
        for i in range(5):
            documents.append(DocumentMetadata(
                filename=f"small_doc_{i}.txt",
                file_path=f"/test/small_doc_{i}.txt",
                file_size=1024 * (i + 1),  # 1-5 KB
                mime_type="text/plain",
                checksum=f"small_{i}"
            ))
        
        # Medium documents (100KB - 1MB)
        for i in range(5):
            documents.append(DocumentMetadata(
                filename=f"medium_doc_{i}.pdf",
                file_path=f"/test/medium_doc_{i}.pdf",
                file_size=1024 * 100 * (i + 1),  # 100KB - 500KB
                mime_type="application/pdf",
                checksum=f"medium_{i}"
            ))
        
        # Large documents (1-10 MB)
        for i in range(3):
            documents.append(DocumentMetadata(
                filename=f"large_doc_{i}.pdf",
                file_path=f"/test/large_doc_{i}.pdf",
                file_size=1024 * 1024 * (i + 1),  # 1-3 MB
                mime_type="application/pdf",
                checksum=f"large_{i}"
            ))
        
        return documents
    
    async def start_monitoring(self):
        """Start system resource monitoring"""
        self.monitoring_active = True
        self.system_snapshots.clear()
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
    
    async def stop_monitoring(self):
        """Stop system resource monitoring"""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
    
    async def _monitoring_loop(self):
        """System monitoring loop"""
        disk_io_start = psutil.disk_io_counters()
        
        while self.monitoring_active:
            try:
                # Get current system metrics
                memory = psutil.virtual_memory()
                cpu_percent = psutil.cpu_percent(interval=0.1)
                disk_io = psutil.disk_io_counters()
                
                # Calculate disk I/O rates
                disk_read_mb = (disk_io.read_bytes - disk_io_start.read_bytes) / (1024 * 1024)
                disk_write_mb = (disk_io.write_bytes - disk_io_start.write_bytes) / (1024 * 1024)
                
                snapshot = SystemSnapshot(
                    timestamp=time.time(),
                    memory_used_mb=(memory.total - memory.available) / (1024 * 1024),
                    memory_percent=memory.percent,
                    cpu_percent=cpu_percent,
                    disk_io_read_mb=disk_read_mb,
                    disk_io_write_mb=disk_write_mb
                )
                
                self.system_snapshots.append(snapshot)
                
                await asyncio.sleep(1.0)  # Sample every second
                
            except Exception as e:
                print(f"Monitoring error: {e}")
                await asyncio.sleep(1.0)
    
    def _calculate_metrics(self, start_time: float, end_time: float, doc_count: int, error_count: int) -> Dict[str, Any]:
        """Calculate performance metrics from monitoring data"""
        duration = end_time - start_time
        
        # Filter snapshots to benchmark period
        relevant_snapshots = [
            s for s in self.system_snapshots 
            if start_time <= s.timestamp <= end_time
        ]
        
        if not relevant_snapshots:
            return {
                "duration_seconds": duration,
                "memory_peak_mb": 0.0,
                "cpu_avg_percent": 0.0,
                "throughput_docs_per_second": doc_count / duration if duration > 0 else 0.0,
                "success_rate": (doc_count - error_count) / doc_count if doc_count > 0 else 0.0
            }
        
        # Calculate statistics
        memory_values = [s.memory_used_mb for s in relevant_snapshots]
        cpu_values = [s.cpu_percent for s in relevant_snapshots]
        
        return {
            "duration_seconds": duration,
            "memory_peak_mb": max(memory_values),
            "memory_avg_mb": statistics.mean(memory_values),
            "memory_std_mb": statistics.stdev(memory_values) if len(memory_values) > 1 else 0.0,
            "cpu_avg_percent": statistics.mean(cpu_values),
            "cpu_peak_percent": max(cpu_values),
            "cpu_std_percent": statistics.stdev(cpu_values) if len(cpu_values) > 1 else 0.0,
            "throughput_docs_per_second": doc_count / duration if duration > 0 else 0.0,
            "success_rate": (doc_count - error_count) / doc_count if doc_count > 0 else 0.0,
            "samples_count": len(relevant_snapshots)
        }
    
    async def benchmark_single_document(self) -> BenchmarkResult:
        """Benchmark single document processing"""
        print("Running single document benchmark...")
        
        # Use a medium-sized document
        test_doc = self.test_documents[7]  # Medium document
        
        job = JobManifest.create_new(
            documents=[test_doc],
            title="Single Document Benchmark",
            priority=5
        )
        
        # Start monitoring
        await self.start_monitoring()
        
        start_time = time.time()
        error_count = 0
        
        try:
            # Create orchestrator and process job
            with tempfile.TemporaryDirectory() as temp_dir:
                config = {
                    "database": {"path": str(Path(temp_dir) / "benchmark.db")},
                    "system": {"data_directory": temp_dir}
                }
                
                orchestrator = JobOrchestrator(config)
                await orchestrator.initialize()
                
                # Mock agents for benchmark
                await self._setup_mock_agents(orchestrator)
                
                await orchestrator.submit_job(job)
                await orchestrator.process_job(job.job_id)
                
                await orchestrator.shutdown()
                
        except Exception as e:
            print(f"Benchmark error: {e}")
            error_count = 1
        
        end_time = time.time()
        
        # Stop monitoring
        await self.stop_monitoring()
        
        # Calculate metrics
        metrics = self._calculate_metrics(start_time, end_time, 1, error_count)
        
        result = BenchmarkResult(
            test_name="single_document",
            duration_seconds=metrics["duration_seconds"],
            memory_peak_mb=metrics["memory_peak_mb"],
            cpu_avg_percent=metrics["cpu_avg_percent"],
            throughput_docs_per_second=metrics["throughput_docs_per_second"],
            success_rate=metrics["success_rate"],
            error_count=error_count,
            additional_metrics=metrics
        )
        
        self.benchmark_results.append(result)
        return result
    
    async def benchmark_batch_processing(self, batch_size: int = 5) -> BenchmarkResult:
        """Benchmark batch document processing"""
        print(f"Running batch processing benchmark (batch_size={batch_size})...")
        
        # Use first N documents
        test_docs = self.test_documents[:batch_size]
        
        job = JobManifest.create_new(
            documents=test_docs,
            title=f"Batch Processing Benchmark ({batch_size} docs)",
            priority=5
        )
        
        # Start monitoring
        await self.start_monitoring()
        
        start_time = time.time()
        error_count = 0
        
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                config = {
                    "database": {"path": str(Path(temp_dir) / "benchmark.db")},
                    "system": {"data_directory": temp_dir}
                }
                
                orchestrator = JobOrchestrator(config)
                await orchestrator.initialize()
                
                await self._setup_mock_agents(orchestrator)
                
                await orchestrator.submit_job(job)
                await orchestrator.process_job(job.job_id)
                
                await orchestrator.shutdown()
                
        except Exception as e:
            print(f"Benchmark error: {e}")
            error_count = batch_size
        
        end_time = time.time()
        
        # Stop monitoring
        await self.stop_monitoring()
        
        # Calculate metrics
        metrics = self._calculate_metrics(start_time, end_time, batch_size, error_count)
        
        result = BenchmarkResult(
            test_name=f"batch_processing_{batch_size}",
            duration_seconds=metrics["duration_seconds"],
            memory_peak_mb=metrics["memory_peak_mb"],
            cpu_avg_percent=metrics["cpu_avg_percent"],
            throughput_docs_per_second=metrics["throughput_docs_per_second"],
            success_rate=metrics["success_rate"],
            error_count=error_count,
            additional_metrics=metrics
        )
        
        self.benchmark_results.append(result)
        return result
    
    async def benchmark_concurrent_jobs(self, job_count: int = 3) -> BenchmarkResult:
        """Benchmark concurrent job processing"""
        print(f"Running concurrent jobs benchmark (job_count={job_count})...")
        
        # Create multiple jobs
        jobs = []
        total_docs = 0
        
        for i in range(job_count):
            # Use 2-3 documents per job
            docs_per_job = 2 + (i % 2)
            start_idx = (i * 2) % len(self.test_documents)
            job_docs = self.test_documents[start_idx:start_idx + docs_per_job]
            
            job = JobManifest.create_new(
                documents=job_docs,
                title=f"Concurrent Job {i+1}",
                priority=5
            )
            jobs.append(job)
            total_docs += len(job_docs)
        
        # Start monitoring
        await self.start_monitoring()
        
        start_time = time.time()
        error_count = 0
        
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                config = {
                    "database": {"path": str(Path(temp_dir) / "benchmark.db")},
                    "system": {"data_directory": temp_dir}
                }
                
                orchestrator = JobOrchestrator(config)
                await orchestrator.initialize()
                
                await self._setup_mock_agents(orchestrator)
                
                # Submit all jobs
                for job in jobs:
                    await orchestrator.submit_job(job)
                
                # Process jobs concurrently
                tasks = [orchestrator.process_job(job.job_id) for job in jobs]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Count errors
                error_count = sum(1 for result in results if isinstance(result, Exception))
                
                await orchestrator.shutdown()
                
        except Exception as e:
            print(f"Benchmark error: {e}")
            error_count = job_count
        
        end_time = time.time()
        
        # Stop monitoring
        await self.stop_monitoring()
        
        # Calculate metrics
        metrics = self._calculate_metrics(start_time, end_time, total_docs, error_count)
        
        result = BenchmarkResult(
            test_name=f"concurrent_jobs_{job_count}",
            duration_seconds=metrics["duration_seconds"],
            memory_peak_mb=metrics["memory_peak_mb"],
            cpu_avg_percent=metrics["cpu_avg_percent"],
            throughput_docs_per_second=metrics["throughput_docs_per_second"],
            success_rate=metrics["success_rate"],
            error_count=error_count,
            additional_metrics=metrics
        )
        
        self.benchmark_results.append(result)
        return result
    
    async def _setup_mock_agents(self, orchestrator):
        """Setup mock agents for benchmarking"""
        from tests.test_orchestrator import MockAgent
        
        # Create mock agents with realistic processing times
        processing_times = {
            "ingestion_triage": 0.1,
            "content_extraction": 0.3,
            "semantic_chunking": 0.2,
            "vectorization_indexing": 0.4,
            "outline_generation": 0.2,
            "knowledge_synthesizer": 0.5,
            "visual_analogy": 0.3,
            "qa_generation": 0.4,
            "final_compiler": 0.2
        }
        
        for agent_name, processing_time in processing_times.items():
            mock_agent = MockAgent(agent_name, should_fail=False, processing_time=processing_time)
            orchestrator.agents[agent_name] = mock_agent
    
    async def run_all_benchmarks(self) -> List[BenchmarkResult]:
        """Run all benchmark tests"""
        print("Starting comprehensive performance benchmarks...")
        
        # Single document
        await self.benchmark_single_document()
        
        # Batch processing with different sizes
        for batch_size in [3, 5, 8]:
            await self.benchmark_batch_processing(batch_size)
        
        # Concurrent jobs
        for job_count in [2, 3, 4]:
            await self.benchmark_concurrent_jobs(job_count)
        
        return self.benchmark_results
    
    def save_results(self, filename: str = None):
        """Save benchmark results to file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"benchmark_results_{timestamp}.json"
        
        output_file = self.output_dir / filename
        
        # Prepare results for JSON serialization
        results_data = {
            "timestamp": datetime.now().isoformat(),
            "system_info": {
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": psutil.virtual_memory().total / (1024**3),
                "platform": psutil.os.name
            },
            "benchmark_results": [asdict(result) for result in self.benchmark_results]
        }
        
        with open(output_file, 'w') as f:
            json.dump(results_data, f, indent=2)
        
        print(f"Benchmark results saved to: {output_file}")
        return output_file
    
    def print_summary(self):
        """Print benchmark results summary"""
        if not self.benchmark_results:
            print("No benchmark results available")
            return
        
        print("\n" + "="*60)
        print("PERFORMANCE BENCHMARK SUMMARY")
        print("="*60)
        
        for result in self.benchmark_results:
            print(f"\nTest: {result.test_name}")
            print(f"  Duration: {result.duration_seconds:.2f}s")
            print(f"  Memory Peak: {result.memory_peak_mb:.1f} MB")
            print(f"  CPU Average: {result.cpu_avg_percent:.1f}%")
            print(f"  Throughput: {result.throughput_docs_per_second:.2f} docs/sec")
            print(f"  Success Rate: {result.success_rate:.1%}")
            print(f"  Errors: {result.error_count}")
        
        # Overall statistics
        durations = [r.duration_seconds for r in self.benchmark_results]
        throughputs = [r.throughput_docs_per_second for r in self.benchmark_results]
        memory_peaks = [r.memory_peak_mb for r in self.benchmark_results]
        
        print(f"\nOVERALL STATISTICS:")
        print(f"  Average Duration: {statistics.mean(durations):.2f}s")
        print(f"  Average Throughput: {statistics.mean(throughputs):.2f} docs/sec")
        print(f"  Peak Memory Usage: {max(memory_peaks):.1f} MB")
        print(f"  Total Tests: {len(self.benchmark_results)}")
        print(f"  Success Rate: {statistics.mean([r.success_rate for r in self.benchmark_results]):.1%}")


# Pytest integration
class TestPerformanceBenchmarks:
    """Pytest integration for performance benchmarks"""
    
    @pytest.mark.benchmark
    async def test_single_document_performance(self):
        """Test single document processing performance"""
        benchmark = PerformanceBenchmark()
        result = await benchmark.benchmark_single_document()
        
        # Performance assertions
        assert result.success_rate >= 0.9  # At least 90% success rate
        assert result.duration_seconds < 10.0  # Should complete within 10 seconds
        assert result.memory_peak_mb < 1000.0  # Should use less than 1GB memory
    
    @pytest.mark.benchmark
    async def test_batch_processing_performance(self):
        """Test batch processing performance"""
        benchmark = PerformanceBenchmark()
        result = await benchmark.benchmark_batch_processing(batch_size=3)
        
        # Performance assertions
        assert result.success_rate >= 0.9
        assert result.throughput_docs_per_second > 0.1  # At least 0.1 docs/sec
        assert result.memory_peak_mb < 2000.0  # Should use less than 2GB memory


async def main():
    """Main benchmark runner"""
    benchmark = PerformanceBenchmark()
    
    try:
        results = await benchmark.run_all_benchmarks()
        benchmark.print_summary()
        benchmark.save_results()
        
        return results
        
    except KeyboardInterrupt:
        print("\nBenchmark interrupted by user")
    except Exception as e:
        print(f"Benchmark failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
