"""
Test suite for JobManifest and related models.

Tests the core job management functionality including state transitions,
progress tracking, and data persistence.
"""

import pytest
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import json

from src.models.job_manifest import (
    JobManifest, DocumentMetadata, ProcessingStage, ErrorRecord,
    JobState, AgentState, Priority
)


class TestDocumentMetadata:
    """Test DocumentMetadata model"""
    
    def test_create_document_metadata(self):
        """Test creating document metadata"""
        doc = DocumentMetadata(
            filename="test.pdf",
            file_path="/path/to/test.pdf",
            file_size=1024,
            mime_type="application/pdf",
            checksum="abc123"
        )
        
        assert doc.filename == "test.pdf"
        assert doc.file_path == "/path/to/test.pdf"
        assert doc.file_size == 1024
        assert doc.mime_type == "application/pdf"
        assert doc.checksum == "abc123"
    
    def test_document_metadata_validation(self):
        """Test document metadata validation"""
        # Valid document
        doc = DocumentMetadata(
            filename="test.pdf",
            file_path="/path/to/test.pdf",
            file_size=1024,
            mime_type="application/pdf",
            checksum="abc123"
        )
        assert doc.filename == "test.pdf"
        
        # Test with minimum required fields
        doc_minimal = DocumentMetadata(
            filename="minimal.txt",
            file_path="/path/to/minimal.txt",
            file_size=0,
            mime_type="text/plain",
            checksum=""
        )
        assert doc_minimal.filename == "minimal.txt"


class TestProcessingStage:
    """Test ProcessingStage model"""
    
    def test_create_processing_stage(self):
        """Test creating processing stage"""
        stage = ProcessingStage(
            agent_name="test_agent",
            state=AgentState.PENDING
        )
        
        assert stage.agent_name == "test_agent"
        assert stage.state == AgentState.PENDING
        assert stage.started_at is None
        assert stage.completed_at is None
        assert stage.progress_percentage == 0.0
        assert stage.error_message is None
        assert stage.output_artifacts == []
    
    def test_processing_stage_state_transitions(self):
        """Test processing stage state transitions"""
        stage = ProcessingStage(
            agent_name="test_agent",
            state=AgentState.PENDING
        )
        
        # Start processing
        stage.start_processing()
        assert stage.state == AgentState.RUNNING
        assert stage.started_at is not None
        
        # Update progress
        stage.update_progress(50.0, "Halfway done")
        assert stage.progress_percentage == 50.0
        assert stage.status_message == "Halfway done"
        
        # Complete processing
        stage.complete_processing()
        assert stage.state == AgentState.COMPLETED
        assert stage.completed_at is not None
        assert stage.progress_percentage == 100.0
    
    def test_processing_stage_error_handling(self):
        """Test processing stage error handling"""
        stage = ProcessingStage(
            agent_name="test_agent",
            state=AgentState.RUNNING
        )
        
        # Fail processing
        stage.fail_processing("Test error", "Stack trace here")
        assert stage.state == AgentState.FAILED
        assert stage.error_message == "Test error"
        assert stage.stack_trace == "Stack trace here"
        assert stage.completed_at is not None


class TestErrorRecord:
    """Test ErrorRecord model"""
    
    def test_create_error_record(self):
        """Test creating error record"""
        error = ErrorRecord(
            agent_name="test_agent",
            error_message="Test error",
            stack_trace="Stack trace",
            timestamp=datetime.now()
        )
        
        assert error.agent_name == "test_agent"
        assert error.error_message == "Test error"
        assert error.stack_trace == "Stack trace"
        assert isinstance(error.timestamp, datetime)


class TestJobManifest:
    """Test JobManifest model"""
    
    def test_create_new_job(self):
        """Test creating a new job manifest"""
        documents = [
            DocumentMetadata(
                filename="test1.pdf",
                file_path="/path/to/test1.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="abc123"
            ),
            DocumentMetadata(
                filename="test2.docx",
                file_path="/path/to/test2.docx",
                file_size=2048,
                mime_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                checksum="def456"
            )
        ]
        
        job = JobManifest.create_new(
            documents=documents,
            title="Test Study Guide",
            priority=Priority.NORMAL
        )
        
        assert job.title == "Test Study Guide"
        assert job.priority == Priority.NORMAL
        assert job.state == JobState.PENDING
        assert len(job.documents) == 2
        assert job.progress_percentage == 0.0
        assert len(job.processing_stages) == 9  # All 9 agents
        
        # Check that all processing stages are created
        expected_agents = [
            "ingestion_triage", "content_extraction", "semantic_chunking",
            "vectorization_indexing", "outline_generation", "knowledge_synthesizer",
            "visual_analogy", "qa_generation", "final_compiler"
        ]
        
        for agent_name in expected_agents:
            assert agent_name in job.processing_stages
            assert job.processing_stages[agent_name].state == AgentState.PENDING
    
    def test_job_state_transitions(self):
        """Test job state transitions"""
        documents = [
            DocumentMetadata(
                filename="test.pdf",
                file_path="/path/to/test.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="abc123"
            )
        ]
        
        job = JobManifest.create_new(documents=documents, title="Test Job")
        
        # Start job
        job.start_processing()
        assert job.state == JobState.RUNNING
        
        # Complete job
        job.complete_processing()
        assert job.state == JobState.COMPLETED
        assert job.progress_percentage == 100.0
    
    def test_job_progress_calculation(self):
        """Test job progress calculation"""
        documents = [
            DocumentMetadata(
                filename="test.pdf",
                file_path="/path/to/test.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="abc123"
            )
        ]
        
        job = JobManifest.create_new(documents=documents, title="Test Job")
        
        # Initially 0% progress
        assert job.progress_percentage == 0.0
        
        # Complete first stage
        job.processing_stages["ingestion_triage"].complete_processing()
        job.update_progress()
        
        # Should be approximately 11.11% (1/9 stages)
        expected_progress = (1 / 9) * 100
        assert abs(job.progress_percentage - expected_progress) < 0.1
        
        # Complete half the stages
        stages_to_complete = ["content_extraction", "semantic_chunking", "vectorization_indexing"]
        for stage_name in stages_to_complete:
            job.processing_stages[stage_name].complete_processing()
        
        job.update_progress()
        
        # Should be approximately 44.44% (4/9 stages)
        expected_progress = (4 / 9) * 100
        assert abs(job.progress_percentage - expected_progress) < 0.1
    
    def test_job_error_handling(self):
        """Test job error handling"""
        documents = [
            DocumentMetadata(
                filename="test.pdf",
                file_path="/path/to/test.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="abc123"
            )
        ]
        
        job = JobManifest.create_new(documents=documents, title="Test Job")
        
        # Add error
        job.add_error("test_agent", "Test error message", "Stack trace")
        
        assert len(job.error_log) == 1
        error = job.error_log[0]
        assert error.agent_name == "test_agent"
        assert error.error_message == "Test error message"
        assert error.stack_trace == "Stack trace"
    
    def test_job_serialization(self):
        """Test job manifest serialization"""
        documents = [
            DocumentMetadata(
                filename="test.pdf",
                file_path="/path/to/test.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="abc123"
            )
        ]
        
        job = JobManifest.create_new(documents=documents, title="Test Job")
        
        # Test to_dict
        job_dict = job.to_dict()
        assert isinstance(job_dict, dict)
        assert job_dict["title"] == "Test Job"
        assert job_dict["state"] == "pending"
        assert len(job_dict["documents"]) == 1
        assert len(job_dict["processing_stages"]) == 9
        
        # Test from_dict
        job_restored = JobManifest.from_dict(job_dict)
        assert job_restored.job_id == job.job_id
        assert job_restored.title == job.title
        assert job_restored.state == job.state
        assert len(job_restored.documents) == len(job.documents)
        assert len(job_restored.processing_stages) == len(job.processing_stages)
    
    def test_job_validation(self):
        """Test job manifest validation"""
        documents = [
            DocumentMetadata(
                filename="test.pdf",
                file_path="/path/to/test.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="abc123"
            )
        ]
        
        job = JobManifest.create_new(documents=documents, title="Test Job")
        
        # Valid job should pass validation
        assert job.validate() == True
        
        # Test with invalid state transition
        job.state = JobState.COMPLETED
        job.processing_stages["ingestion_triage"].state = AgentState.PENDING
        
        # This should still be valid as we don't enforce strict state consistency in validation
        # In a real implementation, you might want stricter validation
        assert job.validate() == True
    
    def test_job_with_output_path(self):
        """Test job with final output path"""
        documents = [
            DocumentMetadata(
                filename="test.pdf",
                file_path="/path/to/test.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="abc123"
            )
        ]
        
        job = JobManifest.create_new(documents=documents, title="Test Job")
        
        # Set output path
        output_path = "/path/to/output/study_guide.pdf"
        job.final_output_path = output_path
        
        assert job.final_output_path == output_path
        
        # Test serialization with output path
        job_dict = job.to_dict()
        assert job_dict["final_output_path"] == output_path
        
        # Test deserialization
        job_restored = JobManifest.from_dict(job_dict)
        assert job_restored.final_output_path == output_path


class TestJobManifestIntegration:
    """Integration tests for JobManifest"""
    
    def test_complete_job_workflow(self):
        """Test a complete job workflow"""
        # Create job
        documents = [
            DocumentMetadata(
                filename="test.pdf",
                file_path="/path/to/test.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="abc123"
            )
        ]
        
        job = JobManifest.create_new(
            documents=documents,
            title="Integration Test Job",
            priority=Priority.HIGH
        )
        
        # Start job
        job.start_processing()
        assert job.state == JobState.RUNNING
        
        # Process through all stages
        agent_names = [
            "ingestion_triage", "content_extraction", "semantic_chunking",
            "vectorization_indexing", "outline_generation", "knowledge_synthesizer",
            "visual_analogy", "qa_generation", "final_compiler"
        ]
        
        for i, agent_name in enumerate(agent_names):
            stage = job.processing_stages[agent_name]
            
            # Start stage
            stage.start_processing()
            assert stage.state == AgentState.RUNNING
            
            # Update progress
            stage.update_progress(50.0, f"Processing {agent_name}")
            assert stage.progress_percentage == 50.0
            
            # Complete stage
            stage.complete_processing()
            assert stage.state == AgentState.COMPLETED
            assert stage.progress_percentage == 100.0
            
            # Update job progress
            job.update_progress()
            
            # Check overall progress
            expected_progress = ((i + 1) / len(agent_names)) * 100
            assert abs(job.progress_percentage - expected_progress) < 0.1
        
        # Complete job
        job.complete_processing()
        assert job.state == JobState.COMPLETED
        assert job.progress_percentage == 100.0
    
    def test_job_failure_workflow(self):
        """Test job failure workflow"""
        documents = [
            DocumentMetadata(
                filename="test.pdf",
                file_path="/path/to/test.pdf",
                file_size=1024,
                mime_type="application/pdf",
                checksum="abc123"
            )
        ]
        
        job = JobManifest.create_new(documents=documents, title="Failure Test Job")
        
        # Start job
        job.start_processing()
        
        # Process first few stages successfully
        for agent_name in ["ingestion_triage", "content_extraction"]:
            stage = job.processing_stages[agent_name]
            stage.start_processing()
            stage.complete_processing()
        
        # Fail on third stage
        failing_stage = job.processing_stages["semantic_chunking"]
        failing_stage.start_processing()
        failing_stage.fail_processing("Simulated failure", "Stack trace here")
        
        # Add error to job
        job.add_error("semantic_chunking", "Simulated failure", "Stack trace here")
        
        # Fail job
        job.fail_processing("Job failed due to semantic_chunking error")
        
        assert job.state == JobState.FAILED
        assert len(job.error_log) == 1
        assert job.error_log[0].agent_name == "semantic_chunking"


if __name__ == "__main__":
    pytest.main([__file__])
