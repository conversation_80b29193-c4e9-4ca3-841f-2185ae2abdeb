#!/usr/bin/env python3
"""
End-to-End Test for Study Guide Generator

This script demonstrates the complete workflow from document upload
to study guide generation using the core system components.
"""

import sys
import os
import asyncio
from pathlib import Path
from datetime import datetime
import tempfile
import json

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def create_sample_academic_document():
    """Create a comprehensive sample academic document"""
    content = """
# Introduction to Machine Learning

## Overview
Machine Learning (ML) is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed for every task.

## Key Concepts

### 1. Supervised Learning
Supervised learning uses labeled training data to learn a mapping function from inputs to outputs.

**Examples:**
- Classification: Email spam detection
- Regression: House price prediction

**Key Algorithms:**
- Linear Regression
- Decision Trees
- Support Vector Machines
- Neural Networks

### 2. Unsupervised Learning
Unsupervised learning finds hidden patterns in data without labeled examples.

**Examples:**
- Clustering: Customer segmentation
- Dimensionality Reduction: Data visualization

**Key Algorithms:**
- K-Means Clustering
- Principal Component Analysis (PCA)
- Hierarchical Clustering

### 3. Reinforcement Learning
Reinforcement learning learns through interaction with an environment using rewards and penalties.

**Examples:**
- Game playing (Chess, Go)
- Autonomous vehicles
- Recommendation systems

## Mathematical Foundations

### Linear Regression
The linear regression model is defined as:
y = β₀ + β₁x₁ + β₂x₂ + ... + βₙxₙ + ε

Where:
- y is the dependent variable
- x₁, x₂, ..., xₙ are independent variables
- β₀, β₁, ..., βₙ are coefficients
- ε is the error term

### Gradient Descent
Gradient descent is an optimization algorithm used to minimize the cost function:
θ = θ - α∇J(θ)

Where:
- θ represents the parameters
- α is the learning rate
- ∇J(θ) is the gradient of the cost function

## Neural Networks

### Basic Structure
A neural network consists of:
1. **Input Layer**: Receives the input data
2. **Hidden Layers**: Process the data through weighted connections
3. **Output Layer**: Produces the final prediction

### Activation Functions
Common activation functions include:
- **Sigmoid**: σ(x) = 1/(1 + e^(-x))
- **ReLU**: f(x) = max(0, x)
- **Tanh**: tanh(x) = (e^x - e^(-x))/(e^x + e^(-x))

## Deep Learning

### Convolutional Neural Networks (CNNs)
CNNs are particularly effective for image processing tasks:
- **Convolutional Layers**: Extract features using filters
- **Pooling Layers**: Reduce spatial dimensions
- **Fully Connected Layers**: Make final predictions

### Recurrent Neural Networks (RNNs)
RNNs are designed for sequential data:
- **LSTM**: Long Short-Term Memory networks
- **GRU**: Gated Recurrent Units
- **Applications**: Natural language processing, time series analysis

## Model Evaluation

### Classification Metrics
- **Accuracy**: (TP + TN) / (TP + TN + FP + FN)
- **Precision**: TP / (TP + FP)
- **Recall**: TP / (TP + FN)
- **F1-Score**: 2 × (Precision × Recall) / (Precision + Recall)

### Regression Metrics
- **Mean Squared Error (MSE)**: Σ(yᵢ - ŷᵢ)² / n
- **Root Mean Squared Error (RMSE)**: √MSE
- **Mean Absolute Error (MAE)**: Σ|yᵢ - ŷᵢ| / n

## Best Practices

### Data Preprocessing
1. **Data Cleaning**: Handle missing values and outliers
2. **Feature Scaling**: Normalize or standardize features
3. **Feature Engineering**: Create meaningful features from raw data

### Model Selection
1. **Cross-Validation**: Use k-fold cross-validation
2. **Hyperparameter Tuning**: Grid search or random search
3. **Regularization**: Prevent overfitting with L1/L2 regularization

### Deployment Considerations
1. **Model Monitoring**: Track performance in production
2. **A/B Testing**: Compare model versions
3. **Scalability**: Ensure models can handle production load

## Conclusion
Machine learning is a powerful tool for solving complex problems across various domains. Success requires understanding the underlying algorithms, proper data preprocessing, careful model selection, and continuous monitoring in production environments.

## References
1. Bishop, C. M. (2006). Pattern Recognition and Machine Learning
2. Hastie, T., Tibshirani, R., & Friedman, J. (2009). The Elements of Statistical Learning
3. Goodfellow, I., Bengio, Y., & Courville, A. (2016). Deep Learning
"""
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
        f.write(content)
        return f.name

async def simulate_agent_processing(job, agent_name, processing_time=1.0):
    """Simulate agent processing with realistic timing"""
    from models.job_manifest import ProcessingStage, AgentState
    
    print(f"  🔄 Starting {agent_name}...")
    
    # Create processing stage
    stage = ProcessingStage(
        agent_name=agent_name,
        state=AgentState.RUNNING,
        start_time=datetime.now(),
        progress_percentage=0.0
    )
    job.processing_stages[agent_name] = stage
    
    # Simulate processing with progress updates
    for progress in [25, 50, 75, 100]:
        await asyncio.sleep(processing_time / 4)
        stage.progress_percentage = progress
        print(f"    📊 {agent_name}: {progress}%")
    
    # Complete the stage
    stage.state = AgentState.COMPLETED
    stage.end_time = datetime.now()
    
    print(f"  ✅ {agent_name} completed")
    return True

def generate_study_guide_content(job):
    """Generate study guide content based on the processed job"""
    
    # Simulate extracted content and generated study guide
    study_guide_content = """
# Machine Learning Study Guide

## 📚 Overview
This study guide covers the fundamental concepts of Machine Learning, including supervised learning, unsupervised learning, and reinforcement learning.

## 🎯 Learning Objectives
By the end of this study guide, you should be able to:
- Understand the three main types of machine learning
- Apply basic ML algorithms to real-world problems
- Evaluate model performance using appropriate metrics
- Implement best practices for ML projects

## 📖 Key Concepts

### Supervised Learning
- **Definition**: Learning with labeled training data
- **Types**: Classification and Regression
- **Examples**: Email spam detection, house price prediction
- **Key Algorithms**: Linear Regression, Decision Trees, SVM, Neural Networks

### Unsupervised Learning
- **Definition**: Finding patterns in unlabeled data
- **Types**: Clustering and Dimensionality Reduction
- **Examples**: Customer segmentation, data visualization
- **Key Algorithms**: K-Means, PCA, Hierarchical Clustering

### Reinforcement Learning
- **Definition**: Learning through interaction with environment
- **Components**: Agent, Environment, Actions, Rewards
- **Examples**: Game playing, autonomous vehicles, recommendations

## 🧮 Important Formulas

### Linear Regression
```
y = β₀ + β₁x₁ + β₂x₂ + ... + βₙxₙ + ε
```

### Gradient Descent
```
θ = θ - α∇J(θ)
```

### Accuracy
```
Accuracy = (TP + TN) / (TP + TN + FP + FN)
```

## 🧠 Practice Questions

### Multiple Choice
1. Which type of learning uses labeled training data?
   a) Unsupervised Learning
   b) Supervised Learning ✓
   c) Reinforcement Learning
   d) Semi-supervised Learning

2. What is the purpose of gradient descent?
   a) To increase the cost function
   b) To minimize the cost function ✓
   c) To visualize data
   d) To clean data

### Short Answer
1. Explain the difference between classification and regression.
2. What are the main components of a neural network?
3. Why is cross-validation important in machine learning?

### Essay Questions
1. Compare and contrast supervised, unsupervised, and reinforcement learning. Provide examples of when each would be most appropriate.

2. Discuss the importance of data preprocessing in machine learning projects. What are the key steps and why are they necessary?

## 📊 Visual Aids

### ML Algorithm Decision Tree
```
Start
├── Labeled Data Available?
│   ├── Yes → Supervised Learning
│   │   ├── Predicting Categories? → Classification
│   │   └── Predicting Numbers? → Regression
│   └── No → Unsupervised Learning
│       ├── Finding Groups? → Clustering
│       └── Reducing Dimensions? → PCA
└── Learning from Interaction? → Reinforcement Learning
```

### Neural Network Structure
```
Input Layer → Hidden Layer(s) → Output Layer
    x₁ ────────→ h₁ ────────→ y₁
    x₂ ────────→ h₂ ────────→ y₂
    x₃ ────────→ h₃ ────────→ y₃
```

## 🎯 Summary
Machine Learning is a powerful field that enables computers to learn from data. The three main paradigms (supervised, unsupervised, and reinforcement learning) each have their specific use cases and algorithms. Success in ML requires understanding the mathematics, proper data preprocessing, careful model selection, and continuous evaluation.

## 📚 Additional Resources
- Online courses: Coursera ML Course, edX MIT Introduction to ML
- Books: "Pattern Recognition and Machine Learning" by Bishop
- Practice: Kaggle competitions, UCI ML Repository datasets
"""
    
    return study_guide_content

def generate_pdf_from_content(content, output_path):
    """Generate PDF from study guide content"""
    try:
        import weasyprint
        
        # Convert markdown-like content to HTML
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Machine Learning Study Guide</title>
            <style>
                body {{ 
                    font-family: 'Georgia', serif; 
                    line-height: 1.6; 
                    margin: 40px; 
                    color: #333;
                }}
                h1 {{ color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
                h2 {{ color: #34495e; margin-top: 30px; }}
                h3 {{ color: #7f8c8d; }}
                .formula {{ 
                    background: #f8f9fa; 
                    padding: 15px; 
                    border-left: 4px solid #3498db; 
                    font-family: 'Courier New', monospace;
                    margin: 15px 0;
                }}
                .question {{ 
                    background: #fff3cd; 
                    padding: 10px; 
                    border-radius: 5px; 
                    margin: 10px 0;
                }}
                .visual {{ 
                    background: #e8f5e8; 
                    padding: 15px; 
                    font-family: 'Courier New', monospace; 
                    border-radius: 5px;
                    margin: 15px 0;
                }}
                ul {{ margin-left: 20px; }}
                li {{ margin: 5px 0; }}
            </style>
        </head>
        <body>
            <div>{content.replace('```', '<div class="formula">').replace('```', '</div>').replace('###', '<h3>').replace('##', '<h2>').replace('#', '<h1>')}</div>
        </body>
        </html>
        """
        
        # Generate PDF
        weasyprint.HTML(string=html_content).write_pdf(output_path)
        return True
        
    except Exception as e:
        print(f"❌ PDF generation failed: {e}")
        return False

async def run_end_to_end_test():
    """Run complete end-to-end test"""
    print("🚀 Study Guide Generator - End-to-End Test")
    print("=" * 60)
    
    try:
        from models.job_manifest import JobManifest, DocumentMetadata
        
        # Step 1: Create sample document
        print("\n📄 Step 1: Creating Sample Document")
        doc_path = create_sample_academic_document()
        doc_size = os.path.getsize(doc_path)
        print(f"✅ Created sample document: {doc_size} bytes")
        
        # Step 2: Create job manifest
        print("\n📋 Step 2: Creating Job Manifest")
        doc = DocumentMetadata(
            file_path=doc_path,
            original_filename="machine_learning_intro.md",
            file_size=doc_size,
            checksum_sha256="ml_doc_123456",
            mime_type="text/markdown",
            upload_timestamp=datetime.now()
        )
        
        job = JobManifest(documents=[doc])
        print(f"✅ Job created: {job.job_id}")
        print(f"✅ Document: {doc.original_filename}")
        
        # Step 3: Simulate agent processing pipeline
        print("\n🤖 Step 3: Processing Through Agent Pipeline")
        
        agents = [
            ("ingestion_triage", "Document classification and routing"),
            ("content_extraction", "Text extraction and parsing"),
            ("semantic_chunking", "Intelligent content segmentation"),
            ("vectorization_indexing", "Creating searchable embeddings"),
            ("outline_generation", "Generating study guide structure"),
            ("knowledge_synthesizer", "Synthesizing key concepts"),
            ("visual_analogy", "Creating visual aids and diagrams"),
            ("qa_generation", "Generating practice questions"),
            ("final_compiler", "Compiling final study guide")
        ]
        
        for agent_name, description in agents:
            print(f"\n  🔧 {agent_name}: {description}")
            await simulate_agent_processing(job, agent_name, processing_time=0.5)
        
        # Step 4: Calculate overall progress
        print("\n📊 Step 4: Progress Summary")
        completed_stages = sum(1 for stage in job.processing_stages.values() 
                             if stage.state.value == "completed")
        total_stages = len(job.processing_stages)
        progress = (completed_stages / total_stages) * 100
        
        print(f"✅ Completed stages: {completed_stages}/{total_stages}")
        print(f"✅ Overall progress: {progress:.1f}%")
        
        # Step 5: Generate study guide content
        print("\n📚 Step 5: Generating Study Guide Content")
        study_guide_content = generate_study_guide_content(job)
        print(f"✅ Study guide content generated: {len(study_guide_content)} characters")
        
        # Step 6: Generate PDF output
        print("\n📄 Step 6: Generating PDF Output")
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as pdf_file:
            pdf_path = pdf_file.name
        
        if generate_pdf_from_content(study_guide_content, pdf_path):
            pdf_size = os.path.getsize(pdf_path)
            print(f"✅ PDF generated: {pdf_path}")
            print(f"✅ PDF size: {pdf_size} bytes")
            
            # Update job with final output
            job.final_output_path = pdf_path
            job.output_artifacts.append(pdf_path)
        else:
            print("❌ PDF generation failed")
        
        # Step 7: Final status
        print("\n🎯 Step 7: Final Status")
        print(f"✅ Job ID: {job.job_id}")
        print(f"✅ Status: Processing Complete")
        print(f"✅ Input Document: {doc.original_filename} ({doc.file_size} bytes)")
        print(f"✅ Output PDF: {os.path.basename(pdf_path)} ({pdf_size} bytes)")
        print(f"✅ Processing Time: ~{len(agents) * 0.5:.1f} seconds (simulated)")
        
        # Cleanup
        os.unlink(doc_path)
        
        print(f"\n🎉 End-to-End Test Completed Successfully!")
        print(f"📁 Generated study guide available at: {pdf_path}")
        print(f"\n💡 This demonstrates the complete workflow:")
        print(f"   1. Document upload and metadata creation")
        print(f"   2. Multi-agent processing pipeline")
        print(f"   3. Content extraction and analysis")
        print(f"   4. Study guide generation with Q&A")
        print(f"   5. PDF compilation and output")
        
        return True, pdf_path
        
    except Exception as e:
        print(f"❌ End-to-end test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

async def main():
    """Main function"""
    success, pdf_path = await run_end_to_end_test()
    
    if success:
        print(f"\n✨ Success! The Study Guide Generator system is working correctly.")
        print(f"📖 You can view the generated study guide at: {pdf_path}")
        
        # Offer to open the PDF
        try:
            import subprocess
            response = input(f"\n🔍 Would you like to open the generated PDF? (y/n): ")
            if response.lower() == 'y':
                subprocess.run(['open', pdf_path])  # macOS
        except:
            pass
    else:
        print(f"\n❌ Test failed. Please check the error messages above.")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
