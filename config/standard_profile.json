{"resource_limits": {"max_memory_gb": 12.0, "max_cpu_cores": 6, "max_gpu_memory_gb": 4.0, "max_concurrent_jobs": 2, "max_file_size_mb": 200, "max_total_storage_gb": 50.0, "processing_timeout_minutes": 60}, "models": {"llm_model": "llama3:8b-instruct", "llm_context_window": 8192, "llm_temperature": 0.3, "llm_max_tokens": 3000, "embedding_model": "bge-small-en-v1.5", "embedding_dimension": 384, "ocr_model": "paddleocr", "ocr_confidence_threshold": 0.8, "image_generation_model": "stabilityai/stable-diffusion-xl-base-1.0", "enable_image_generation": true, "vector_db_type": "chromadb", "vector_collection_size_limit": 50000}, "processing": {"mode": "production", "batch_size": 10, "enable_parallel_processing": true, "chunk_size": 1024, "chunk_overlap": 100}}