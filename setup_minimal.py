#!/usr/bin/env python3
"""
Minimal setup script for testing the Study Guide Generator system.
"""

from setuptools import setup, find_packages

# Minimal requirements for basic functionality testing
requirements = [
    # Core async and I/O
    "aiofiles>=23.0.0",
    "aiohttp>=3.8.0",
    "aiohttp-cors>=0.7.0",
    "aiosqlite>=0.19.0",
    
    # CLI and configuration management
    "click>=8.0.0",
    "pydantic>=2.0.0",
    "pyyaml>=6.0",
    "toml>=0.10.0",
    
    # Document processing (basic)
    "PyMuPDF>=1.23.0",
    "python-docx>=0.8.11",
    "Pillow>=10.0.0",
    
    # System monitoring and resource management
    "psutil>=5.9.0",
    
    # Web and networking
    "requests>=2.31.0",
    "httpx>=0.24.0",
    
    # Data processing and analysis
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    
    # PDF generation and document compilation
    "weasyprint>=59.0",
    
    # Logging and utilities
    "colorlog>=6.7.0",
    "tqdm>=4.65.0",
    "python-dateutil>=2.8.0",
    
    # Security and validation
    "cryptography>=41.0.0",
    
    # Development dependencies
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
]

setup(
    name="studyguide-generator",
    version="1.0.0",
    description="Multi-Agent Study Guide Generator - Privacy-First Academic Document Processing",
    author="Study Guide Generator Team",
    
    # Package configuration
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.9",
    
    # Dependencies
    install_requires=requirements,
    
    # Entry points
    entry_points={
        "console_scripts": [
            "studyguide=cli.main:cli",
        ],
    },
    
    # Package data
    package_data={
        "": [
            "*.json",
            "*.yaml",
            "*.yml",
            "*.sql",
            "*.md",
        ],
    },
    include_package_data=True,
    
    zip_safe=False,
)
