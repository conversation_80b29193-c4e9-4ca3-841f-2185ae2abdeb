# Study Guide Generator - Core Dependencies
# Multi-Agent Study Guide Generator System Requirements

# Core async and I/O
asyncio-mqtt>=0.11.0
aiofiles>=23.0.0
aiohttp>=3.8.0
aiohttp-cors>=0.7.0
aiosqlite>=0.19.0

# CLI and configuration management
click>=8.0.0
pydantic>=2.0.0
pyyaml>=6.0
toml>=0.10.0

# Document processing and OCR
PyMuPDF>=1.23.0          # PDF processing
python-docx>=0.8.11      # Word document processing
Pillow>=10.0.0           # Image processing
paddleocr>=2.7.0         # OCR engine

# Machine learning and NLP
sentence-transformers>=2.2.0  # Text embeddings
chromadb>=0.4.0              # Vector database
transformers>=4.30.0         # Hugging Face transformers
torch>=2.0.0                 # PyTorch for ML models
diffusers>=0.21.0            # Stable Diffusion for image generation

# System monitoring and resource management
psutil>=5.9.0            # System resource monitoring
GPUtil>=1.4.0            # GPU monitoring

# Web and networking
requests>=2.31.0         # HTTP requests
httpx>=0.24.0           # Async HTTP client

# Data processing and analysis
pandas>=2.0.0           # Data manipulation
numpy>=1.24.0           # Numerical computing

# PDF generation and document compilation
weasyprint>=59.0        # HTML to PDF conversion (alternative to Pandoc)

# Logging and utilities
colorlog>=6.7.0         # Colored logging
tqdm>=4.65.0           # Progress bars
python-dateutil>=2.8.0 # Date utilities

# Security and validation
cryptography>=41.0.0    # Cryptographic functions
python-magic>=0.4.27   # File type detection

# Optional: Development and testing dependencies
# Uncomment for development environment
# pytest>=7.0.0
# pytest-asyncio>=0.21.0
# pytest-cov>=4.0.0
# black>=23.0.0
# flake8>=6.0.0
# mypy>=1.0.0
# pre-commit>=3.0.0

# Optional: GPU acceleration (uncomment if you have CUDA GPU)
# torch[cuda]>=2.0.0
# diffusers[torch]>=0.21.0

# Optional: Cloud storage support (uncomment if needed)
# boto3>=1.28.0              # AWS S3
# google-cloud-storage>=2.10.0  # Google Cloud Storage
# azure-storage-blob>=12.17.0   # Azure Blob Storage

# Optional: Monitoring and observability (uncomment if needed)
# prometheus-client>=0.17.0
# grafana-api>=1.0.3

# System dependencies that need to be installed separately:
# - Pandoc (for PDF generation): https://pandoc.org/installing.html
# - LaTeX distribution (TeX Live, MiKTeX, or MacTeX)
# - Ollama (for local LLM serving): https://ollama.ai/
# - Git (for version control)

# Hardware-specific notes:
# - For GPU acceleration: Install CUDA toolkit and cuDNN
# - For optimal performance: SSD storage recommended
# - Memory requirements: Minimum 8GB RAM, 16GB+ recommended
# - CPU: Multi-core processor recommended for parallel processing
