#!/usr/bin/env python3
"""
Setup script for the Study Guide Generator system.

Installs dependencies, configures the system, and provides
deployment utilities for the multi-agent study guide generator.
"""

from setuptools import setup, find_packages
from pathlib import Path
import sys

# Read README for long description
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    requirements = requirements_file.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]
else:
    # Fallback requirements if file doesn't exist
    requirements = [
        # Core dependencies
        "asyncio-mqtt>=0.11.0",
        "aiofiles>=23.0.0",
        "aiohttp>=3.8.0",
        "aiohttp-cors>=0.7.0",
        "aiosqlite>=0.19.0",
        
        # CLI and configuration
        "click>=8.0.0",
        "pydantic>=2.0.0",
        "pyyaml>=6.0",
        "toml>=0.10.0",
        
        # Document processing
        "PyMuPDF>=1.23.0",
        "python-docx>=0.8.11",
        "Pillow>=10.0.0",
        "paddleocr>=2.7.0",
        
        # Machine learning and NLP
        "sentence-transformers>=2.2.0",
        "chromadb>=0.4.0",
        "transformers>=4.30.0",
        "torch>=2.0.0",
        "diffusers>=0.21.0",
        
        # System monitoring
        "psutil>=5.9.0",
        "GPUtil>=1.4.0",
        
        # Web and networking
        "requests>=2.31.0",
        "httpx>=0.24.0",
        
        # Data processing
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        
        # PDF generation
        "pandoc>=2.3",
        "weasyprint>=59.0",
        
        # Development and testing
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "black>=23.0.0",
        "flake8>=6.0.0",
        "mypy>=1.0.0",
    ]

# Optional dependencies for different features
extras_require = {
    "gpu": [
        "torch[cuda]>=2.0.0",
        "diffusers[torch]>=0.21.0",
    ],
    "dev": [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "black>=23.0.0",
        "flake8>=6.0.0",
        "mypy>=1.0.0",
        "pre-commit>=3.0.0",
    ],
    "monitoring": [
        "prometheus-client>=0.17.0",
        "grafana-api>=1.0.3",
    ],
    "cloud": [
        "boto3>=1.28.0",
        "google-cloud-storage>=2.10.0",
        "azure-storage-blob>=12.17.0",
    ]
}

# All optional dependencies
extras_require["all"] = list(set(sum(extras_require.values(), [])))

setup(
    name="studyguide-generator",
    version="1.0.0",
    description="Multi-Agent Study Guide Generator - Privacy-First Academic Document Processing",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Study Guide Generator Team",
    author_email="<EMAIL>",
    url="https://github.com/studyguide-generator/studyguide-generator",
    
    # Package configuration
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.9",
    
    # Dependencies
    install_requires=requirements,
    extras_require=extras_require,
    
    # Entry points
    entry_points={
        "console_scripts": [
            "studyguide=cli.main:cli",
            "studyguide-server=cli.server:main",
            "studyguide-dashboard=monitoring.dashboard:main",
        ],
    },
    
    # Package data
    package_data={
        "": [
            "*.json",
            "*.yaml",
            "*.yml",
            "*.sql",
            "*.md",
            "templates/*.tex",
            "templates/*.html",
            "config/*.json",
            "config/*.yaml",
        ],
    },
    include_package_data=True,
    
    # Metadata
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Education",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Education",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Text Processing :: Markup :: LaTeX",
        "Topic :: Utilities",
    ],
    keywords=[
        "study-guide",
        "document-processing",
        "ai",
        "machine-learning",
        "education",
        "pdf-generation",
        "privacy-first",
        "local-processing",
        "multi-agent",
        "academic",
    ],
    
    # Project URLs
    project_urls={
        "Documentation": "https://studyguide-generator.readthedocs.io/",
        "Source": "https://github.com/studyguide-generator/studyguide-generator",
        "Tracker": "https://github.com/studyguide-generator/studyguide-generator/issues",
        "Changelog": "https://github.com/studyguide-generator/studyguide-generator/blob/main/CHANGELOG.md",
    },
    
    # Additional metadata
    zip_safe=False,
    platforms=["any"],
    license="MIT",
)
