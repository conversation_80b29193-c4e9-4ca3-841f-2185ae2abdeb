"""
System Configuration Management

Centralized configuration system with hardware adaptation profiles,
resource management policies, and environment-specific settings.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum
import psutil
import platform


class HardwareProfile(Enum):
    """Hardware profile classifications"""
    MINIMAL = "minimal"      # 8GB RAM, 4 cores, no GPU
    STANDARD = "standard"    # 16GB RAM, 8 cores, optional GPU
    PERFORMANCE = "performance"  # 32GB RAM, 16+ cores, GPU required
    ENTERPRISE = "enterprise"    # 64GB+ RAM, 32+ cores, multiple GPUs


class ProcessingMode(Enum):
    """Processing mode configurations"""
    DEVELOPMENT = "development"  # Fast iteration, lower quality
    PRODUCTION = "production"    # High quality, full processing
    BATCH = "batch"             # Optimized for multiple documents
    INTERACTIVE = "interactive"  # Real-time processing with feedback


@dataclass
class ResourceLimits:
    """Resource allocation limits"""
    max_memory_gb: float
    max_cpu_cores: int
    max_gpu_memory_gb: float
    max_concurrent_jobs: int
    max_file_size_mb: int
    max_total_storage_gb: float
    processing_timeout_minutes: int


@dataclass
class ModelConfiguration:
    """LLM and ML model configuration"""
    # LLM settings
    llm_model: str
    llm_context_window: int
    llm_temperature: float
    llm_max_tokens: int
    
    # Embedding model settings
    embedding_model: str
    embedding_dimension: int
    
    # OCR settings
    ocr_model: str
    ocr_confidence_threshold: float
    
    # Image generation settings
    image_generation_model: str
    enable_image_generation: bool
    
    # Vector database settings
    vector_db_type: str
    vector_collection_size_limit: int


@dataclass
class AgentConfiguration:
    """Individual agent configuration"""
    enabled: bool
    priority: int
    resource_allocation: Dict[str, Any]
    specific_config: Dict[str, Any]


class SystemConfiguration:
    """
    Centralized system configuration manager with hardware adaptation
    and environment-specific settings.
    """
    
    def __init__(self, config_dir: Optional[Path] = None):
        self.config_dir = config_dir or Path("config")
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        
        # System information
        self.system_info = self._detect_system_info()
        self.hardware_profile = self._detect_hardware_profile()
        
        # Configuration files
        self.base_config_file = self.config_dir / "base_config.json"
        self.profile_config_file = self.config_dir / f"{self.hardware_profile.value}_profile.json"
        self.user_config_file = self.config_dir / "user_config.json"
        
        # Load configuration
        self.config = self._load_configuration()
        
        self.logger.info(f"System initialized with {self.hardware_profile.value} hardware profile")

    def _detect_system_info(self) -> Dict[str, Any]:
        """Detect system hardware and capabilities"""
        try:
            # CPU information
            cpu_count = psutil.cpu_count(logical=True)
            cpu_count_physical = psutil.cpu_count(logical=False)
            
            # Memory information
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            
            # Disk information
            disk = psutil.disk_usage('/')
            disk_gb = disk.total / (1024**3)
            
            # GPU information (if available)
            gpu_info = self._detect_gpu_info()
            
            return {
                "platform": platform.system(),
                "platform_version": platform.version(),
                "architecture": platform.machine(),
                "cpu_count_logical": cpu_count,
                "cpu_count_physical": cpu_count_physical,
                "memory_gb": memory_gb,
                "disk_gb": disk_gb,
                "gpu_info": gpu_info
            }
            
        except Exception as e:
            self.logger.error(f"Failed to detect system info: {e}")
            return {
                "platform": "unknown",
                "cpu_count_logical": 4,
                "memory_gb": 8.0,
                "disk_gb": 100.0,
                "gpu_info": None
            }

    def _detect_gpu_info(self) -> Optional[Dict[str, Any]]:
        """Detect GPU information if available"""
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            
            if gpus:
                gpu = gpus[0]  # Use first GPU
                return {
                    "name": gpu.name,
                    "memory_gb": gpu.memoryTotal / 1024,
                    "driver_version": gpu.driver,
                    "cuda_available": True
                }
        except ImportError:
            pass
        except Exception as e:
            self.logger.warning(f"GPU detection failed: {e}")
        
        # Check for CUDA availability
        try:
            import torch
            if torch.cuda.is_available():
                return {
                    "name": torch.cuda.get_device_name(0),
                    "memory_gb": torch.cuda.get_device_properties(0).total_memory / (1024**3),
                    "cuda_available": True
                }
        except ImportError:
            pass
        
        return None

    def _detect_hardware_profile(self) -> HardwareProfile:
        """Automatically detect appropriate hardware profile"""
        memory_gb = self.system_info.get("memory_gb", 8.0)
        cpu_count = self.system_info.get("cpu_count_logical", 4)
        has_gpu = self.system_info.get("gpu_info") is not None
        
        if memory_gb >= 64 and cpu_count >= 32:
            return HardwareProfile.ENTERPRISE
        elif memory_gb >= 32 and cpu_count >= 16 and has_gpu:
            return HardwareProfile.PERFORMANCE
        elif memory_gb >= 16 and cpu_count >= 8:
            return HardwareProfile.STANDARD
        else:
            return HardwareProfile.MINIMAL

    def _load_configuration(self) -> Dict[str, Any]:
        """Load configuration from multiple sources with precedence"""
        config = {}
        
        # 1. Load base configuration
        base_config = self._load_base_configuration()
        config.update(base_config)
        
        # 2. Load hardware profile configuration
        profile_config = self._load_profile_configuration()
        config.update(profile_config)
        
        # 3. Load user overrides
        user_config = self._load_user_configuration()
        config.update(user_config)
        
        # 4. Apply environment variable overrides
        env_config = self._load_environment_overrides()
        config.update(env_config)
        
        return config

    def _load_base_configuration(self) -> Dict[str, Any]:
        """Load base system configuration"""
        if self.base_config_file.exists():
            try:
                with open(self.base_config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Failed to load base config: {e}")
        
        # Create default base configuration
        base_config = self._create_default_base_config()
        self._save_config_file(self.base_config_file, base_config)
        return base_config

    def _create_default_base_config(self) -> Dict[str, Any]:
        """Create default base configuration"""
        return {
            "system": {
                "log_level": "INFO",
                "data_directory": "data",
                "temp_directory": "data/temp",
                "max_log_files": 10,
                "log_rotation_mb": 100
            },
            "database": {
                "type": "sqlite",
                "path": "data/jobs.db",
                "connection_pool_size": 10,
                "query_timeout_seconds": 30,
                "backup_interval_hours": 24
            },
            "security": {
                "enable_file_validation": True,
                "max_file_size_mb": 100,
                "allowed_file_types": [".pdf", ".docx", ".txt", ".png", ".jpg", ".jpeg"],
                "quarantine_suspicious_files": True,
                "scan_timeout_seconds": 30
            },
            "networking": {
                "ollama_base_url": "http://localhost:11434",
                "connection_timeout_seconds": 30,
                "request_timeout_seconds": 300,
                "max_retries": 3,
                "retry_delay_seconds": 5
            }
        }

    def _load_profile_configuration(self) -> Dict[str, Any]:
        """Load hardware profile-specific configuration"""
        if self.profile_config_file.exists():
            try:
                with open(self.profile_config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Failed to load profile config: {e}")
        
        # Create default profile configuration
        profile_config = self._create_profile_configuration()
        self._save_config_file(self.profile_config_file, profile_config)
        return profile_config

    def _create_profile_configuration(self) -> Dict[str, Any]:
        """Create hardware profile-specific configuration"""
        profile_configs = {
            HardwareProfile.MINIMAL: {
                "resource_limits": asdict(ResourceLimits(
                    max_memory_gb=6.0,
                    max_cpu_cores=3,
                    max_gpu_memory_gb=0.0,
                    max_concurrent_jobs=1,
                    max_file_size_mb=50,
                    max_total_storage_gb=10.0,
                    processing_timeout_minutes=30
                )),
                "models": asdict(ModelConfiguration(
                    llm_model="llama3:8b-instruct",
                    llm_context_window=4096,
                    llm_temperature=0.3,
                    llm_max_tokens=2000,
                    embedding_model="nomic-embed-text",
                    embedding_dimension=768,
                    ocr_model="paddleocr",
                    ocr_confidence_threshold=0.7,
                    image_generation_model="",
                    enable_image_generation=False,
                    vector_db_type="chromadb",
                    vector_collection_size_limit=10000
                )),
                "processing": {
                    "mode": ProcessingMode.DEVELOPMENT.value,
                    "batch_size": 5,
                    "enable_parallel_processing": False,
                    "chunk_size": 512,
                    "chunk_overlap": 50
                }
            },
            
            HardwareProfile.STANDARD: {
                "resource_limits": asdict(ResourceLimits(
                    max_memory_gb=12.0,
                    max_cpu_cores=6,
                    max_gpu_memory_gb=4.0,
                    max_concurrent_jobs=2,
                    max_file_size_mb=200,
                    max_total_storage_gb=50.0,
                    processing_timeout_minutes=60
                )),
                "models": asdict(ModelConfiguration(
                    llm_model="llama3:8b-instruct",
                    llm_context_window=8192,
                    llm_temperature=0.3,
                    llm_max_tokens=3000,
                    embedding_model="bge-small-en-v1.5",
                    embedding_dimension=384,
                    ocr_model="paddleocr",
                    ocr_confidence_threshold=0.8,
                    image_generation_model="stabilityai/stable-diffusion-xl-base-1.0",
                    enable_image_generation=True,
                    vector_db_type="chromadb",
                    vector_collection_size_limit=50000
                )),
                "processing": {
                    "mode": ProcessingMode.PRODUCTION.value,
                    "batch_size": 10,
                    "enable_parallel_processing": True,
                    "chunk_size": 1024,
                    "chunk_overlap": 100
                }
            },
            
            HardwareProfile.PERFORMANCE: {
                "resource_limits": asdict(ResourceLimits(
                    max_memory_gb=24.0,
                    max_cpu_cores=12,
                    max_gpu_memory_gb=12.0,
                    max_concurrent_jobs=4,
                    max_file_size_mb=500,
                    max_total_storage_gb=200.0,
                    processing_timeout_minutes=120
                )),
                "models": asdict(ModelConfiguration(
                    llm_model="llama3:70b-instruct",
                    llm_context_window=8192,
                    llm_temperature=0.3,
                    llm_max_tokens=4000,
                    embedding_model="bge-large-en-v1.5",
                    embedding_dimension=1024,
                    ocr_model="paddleocr",
                    ocr_confidence_threshold=0.85,
                    image_generation_model="stabilityai/stable-diffusion-xl-base-1.0",
                    enable_image_generation=True,
                    vector_db_type="chromadb",
                    vector_collection_size_limit=100000
                )),
                "processing": {
                    "mode": ProcessingMode.PRODUCTION.value,
                    "batch_size": 20,
                    "enable_parallel_processing": True,
                    "chunk_size": 2048,
                    "chunk_overlap": 200
                }
            },
            
            HardwareProfile.ENTERPRISE: {
                "resource_limits": asdict(ResourceLimits(
                    max_memory_gb=48.0,
                    max_cpu_cores=24,
                    max_gpu_memory_gb=24.0,
                    max_concurrent_jobs=8,
                    max_file_size_mb=1000,
                    max_total_storage_gb=1000.0,
                    processing_timeout_minutes=240
                )),
                "models": asdict(ModelConfiguration(
                    llm_model="llama3:70b-instruct",
                    llm_context_window=16384,
                    llm_temperature=0.3,
                    llm_max_tokens=6000,
                    embedding_model="bge-large-en-v1.5",
                    embedding_dimension=1024,
                    ocr_model="paddleocr",
                    ocr_confidence_threshold=0.9,
                    image_generation_model="stabilityai/stable-diffusion-xl-base-1.0",
                    enable_image_generation=True,
                    vector_db_type="chromadb",
                    vector_collection_size_limit=500000
                )),
                "processing": {
                    "mode": ProcessingMode.BATCH.value,
                    "batch_size": 50,
                    "enable_parallel_processing": True,
                    "chunk_size": 2048,
                    "chunk_overlap": 200
                }
            }
        }
        
        return profile_configs.get(self.hardware_profile, profile_configs[HardwareProfile.STANDARD])

    def _load_user_configuration(self) -> Dict[str, Any]:
        """Load user-specific configuration overrides"""
        if self.user_config_file.exists():
            try:
                with open(self.user_config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Failed to load user config: {e}")
        
        return {}

    def _load_environment_overrides(self) -> Dict[str, Any]:
        """Load configuration from environment variables"""
        env_config = {}
        
        # System overrides
        if os.getenv("STUDYGUIDE_LOG_LEVEL"):
            env_config.setdefault("system", {})["log_level"] = os.getenv("STUDYGUIDE_LOG_LEVEL")
        
        if os.getenv("STUDYGUIDE_DATA_DIR"):
            env_config.setdefault("system", {})["data_directory"] = os.getenv("STUDYGUIDE_DATA_DIR")
        
        # Model overrides
        if os.getenv("STUDYGUIDE_LLM_MODEL"):
            env_config.setdefault("models", {})["llm_model"] = os.getenv("STUDYGUIDE_LLM_MODEL")
        
        if os.getenv("STUDYGUIDE_OLLAMA_URL"):
            env_config.setdefault("networking", {})["ollama_base_url"] = os.getenv("STUDYGUIDE_OLLAMA_URL")
        
        # Resource overrides
        if os.getenv("STUDYGUIDE_MAX_MEMORY_GB"):
            try:
                max_memory = float(os.getenv("STUDYGUIDE_MAX_MEMORY_GB"))
                env_config.setdefault("resource_limits", {})["max_memory_gb"] = max_memory
            except ValueError:
                pass
        
        if os.getenv("STUDYGUIDE_MAX_CONCURRENT_JOBS"):
            try:
                max_jobs = int(os.getenv("STUDYGUIDE_MAX_CONCURRENT_JOBS"))
                env_config.setdefault("resource_limits", {})["max_concurrent_jobs"] = max_jobs
            except ValueError:
                pass
        
        return env_config

    def _save_config_file(self, file_path: Path, config: Dict[str, Any]):
        """Save configuration to file"""
        try:
            with open(file_path, 'w') as f:
                json.dump(config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save config file {file_path}: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value with dot notation support"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value

    def set(self, key: str, value: Any, persist: bool = False):
        """Set configuration value with dot notation support"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        
        if persist:
            self.save_user_config()

    def save_user_config(self):
        """Save current configuration as user overrides"""
        try:
            # Only save non-default values
            user_overrides = self._extract_user_overrides()
            self._save_config_file(self.user_config_file, user_overrides)
            self.logger.info("User configuration saved")
        except Exception as e:
            self.logger.error(f"Failed to save user config: {e}")

    def _extract_user_overrides(self) -> Dict[str, Any]:
        """Extract user-specific overrides from current config"""
        # This is a simplified implementation
        # In practice, you'd want to track which values were modified by the user
        return {}

    def get_agent_config(self, agent_name: str) -> Dict[str, Any]:
        """Get configuration for a specific agent"""
        agent_config = self.get(f"agents.{agent_name}", {})
        
        # Merge with global settings
        global_config = {
            "ollama_base_url": self.get("networking.ollama_base_url"),
            "llm_model": self.get("models.llm_model"),
            "embedding_model": self.get("models.embedding_model"),
            "batch_size": self.get("processing.batch_size"),
            "enable_parallel_processing": self.get("processing.enable_parallel_processing"),
            "chunk_size": self.get("processing.chunk_size"),
            "chunk_overlap": self.get("processing.chunk_overlap"),
            "max_memory_gb": self.get("resource_limits.max_memory_gb"),
            "processing_timeout_minutes": self.get("resource_limits.processing_timeout_minutes")
        }
        
        # Agent-specific config overrides global config
        global_config.update(agent_config)
        return global_config

    def get_resource_limits(self) -> ResourceLimits:
        """Get current resource limits"""
        limits_dict = self.get("resource_limits", {})
        return ResourceLimits(**limits_dict)

    def get_model_config(self) -> ModelConfiguration:
        """Get current model configuration"""
        model_dict = self.get("models", {})
        return ModelConfiguration(**model_dict)

    def validate_configuration(self) -> List[str]:
        """Validate current configuration and return any issues"""
        issues = []
        
        # Check resource limits against system capabilities
        limits = self.get_resource_limits()
        
        if limits.max_memory_gb > self.system_info["memory_gb"] * 0.9:
            issues.append(f"Memory limit ({limits.max_memory_gb}GB) exceeds 90% of system memory")
        
        if limits.max_cpu_cores > self.system_info["cpu_count_logical"]:
            issues.append(f"CPU core limit ({limits.max_cpu_cores}) exceeds system cores")
        
        # Check model availability
        model_config = self.get_model_config()
        if model_config.enable_image_generation and not self.system_info.get("gpu_info"):
            issues.append("Image generation enabled but no GPU detected")
        
        # Check disk space
        data_dir = Path(self.get("system.data_directory", "data"))
        if data_dir.exists():
            try:
                disk_usage = psutil.disk_usage(str(data_dir))
                available_gb = disk_usage.free / (1024**3)
                
                if limits.max_total_storage_gb > available_gb:
                    issues.append(f"Storage limit ({limits.max_total_storage_gb}GB) exceeds available space")
            except Exception:
                issues.append("Could not check disk space availability")
        
        return issues

    def get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        return self.system_info.copy()

    def get_hardware_profile(self) -> HardwareProfile:
        """Get detected hardware profile"""
        return self.hardware_profile

    def export_config(self, file_path: Path):
        """Export current configuration to file"""
        export_data = {
            "system_info": self.system_info,
            "hardware_profile": self.hardware_profile.value,
            "configuration": self.config,
            "validation_issues": self.validate_configuration()
        }
        
        self._save_config_file(file_path, export_data)

    def __str__(self) -> str:
        """String representation of configuration"""
        return (f"SystemConfiguration("
                f"profile={self.hardware_profile.value}, "
                f"memory={self.system_info['memory_gb']:.1f}GB, "
                f"cores={self.system_info['cpu_count_logical']}, "
                f"gpu={'yes' if self.system_info.get('gpu_info') else 'no'})")


# Global configuration instance
_config_instance: Optional[SystemConfiguration] = None


def get_config() -> SystemConfiguration:
    """Get global configuration instance"""
    global _config_instance
    if _config_instance is None:
        _config_instance = SystemConfiguration()
    return _config_instance


def initialize_config(config_dir: Optional[Path] = None) -> SystemConfiguration:
    """Initialize global configuration with custom directory"""
    global _config_instance
    _config_instance = SystemConfiguration(config_dir)
    return _config_instance
