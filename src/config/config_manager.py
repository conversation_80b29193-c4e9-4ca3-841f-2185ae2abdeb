"""
Configuration Management Utility

Provides centralized configuration management with validation,
environment adaptation, and runtime configuration updates.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import yaml
from datetime import datetime

from .system_config import SystemConfiguration, get_config, initialize_config
from .hardware_profiles import HardwareProfileManager, get_profile_manager, HardwareProfile
from ..core.resource_manager import get_resource_manager


class ConfigurationManager:
    """
    Centralized configuration management with validation and adaptation.
    
    Features:
    - Configuration validation and schema checking
    - Environment-specific configuration loading
    - Runtime configuration updates
    - Configuration backup and restore
    - Performance-based auto-tuning
    """
    
    def __init__(self, config_dir: Optional[Path] = None):
        self.config_dir = config_dir or Path("config")
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.system_config = get_config() if config_dir is None else initialize_config(config_dir)
        self.profile_manager = get_profile_manager()
        self.resource_manager = get_resource_manager()
        
        # Configuration schema for validation
        self.config_schema = self._load_config_schema()
        
        # Configuration history for rollback
        self.config_history: List[Dict[str, Any]] = []
        self.max_history_entries = 10

    def _load_config_schema(self) -> Dict[str, Any]:
        """Load configuration schema for validation"""
        schema_file = self.config_dir / "config_schema.json"
        
        if schema_file.exists():
            try:
                with open(schema_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Failed to load config schema: {e}")
        
        # Return default schema
        return self._create_default_schema()

    def _create_default_schema(self) -> Dict[str, Any]:
        """Create default configuration schema"""
        return {
            "type": "object",
            "properties": {
                "system": {
                    "type": "object",
                    "properties": {
                        "log_level": {"type": "string", "enum": ["DEBUG", "INFO", "WARNING", "ERROR"]},
                        "data_directory": {"type": "string"},
                        "temp_directory": {"type": "string"},
                        "max_log_files": {"type": "integer", "minimum": 1},
                        "log_rotation_mb": {"type": "integer", "minimum": 1}
                    },
                    "required": ["log_level", "data_directory"]
                },
                "resource_limits": {
                    "type": "object",
                    "properties": {
                        "max_memory_gb": {"type": "number", "minimum": 1},
                        "max_cpu_cores": {"type": "integer", "minimum": 1},
                        "max_gpu_memory_gb": {"type": "number", "minimum": 0},
                        "max_concurrent_jobs": {"type": "integer", "minimum": 1},
                        "max_file_size_mb": {"type": "integer", "minimum": 1},
                        "processing_timeout_minutes": {"type": "integer", "minimum": 1}
                    },
                    "required": ["max_memory_gb", "max_cpu_cores", "max_concurrent_jobs"]
                },
                "models": {
                    "type": "object",
                    "properties": {
                        "llm_model": {"type": "string"},
                        "llm_context_window": {"type": "integer", "minimum": 512},
                        "embedding_model": {"type": "string"},
                        "enable_image_generation": {"type": "boolean"}
                    },
                    "required": ["llm_model", "embedding_model"]
                }
            },
            "required": ["system", "resource_limits", "models"]
        }

    def validate_configuration(self, config: Optional[Dict[str, Any]] = None) -> Tuple[bool, List[str]]:
        """
        Validate configuration against schema and system constraints.
        
        Args:
            config: Configuration to validate (uses current config if None)
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        if config is None:
            config = self.system_config.config
        
        errors = []
        
        # Schema validation
        try:
            import jsonschema
            jsonschema.validate(config, self.config_schema)
        except ImportError:
            self.logger.warning("jsonschema not available, skipping schema validation")
        except Exception as e:
            errors.append(f"Schema validation failed: {e}")
        
        # System constraint validation
        system_errors = self._validate_system_constraints(config)
        errors.extend(system_errors)
        
        # Hardware compatibility validation
        hardware_errors = self._validate_hardware_compatibility(config)
        errors.extend(hardware_errors)
        
        # Model availability validation
        model_errors = self._validate_model_availability(config)
        errors.extend(model_errors)
        
        return len(errors) == 0, errors

    def _validate_system_constraints(self, config: Dict[str, Any]) -> List[str]:
        """Validate configuration against system constraints"""
        errors = []
        
        # Get system information
        system_info = self.system_config.get_system_info()
        
        # Memory validation
        max_memory = config.get("resource_limits", {}).get("max_memory_gb", 0)
        system_memory = system_info.get("memory_gb", 0)
        
        if max_memory > system_memory * 0.95:
            errors.append(f"Memory limit ({max_memory}GB) exceeds 95% of system memory ({system_memory}GB)")
        
        # CPU validation
        max_cpu = config.get("resource_limits", {}).get("max_cpu_cores", 0)
        system_cpu = system_info.get("cpu_count_logical", 0)
        
        if max_cpu > system_cpu:
            errors.append(f"CPU core limit ({max_cpu}) exceeds system cores ({system_cpu})")
        
        # GPU validation
        max_gpu_memory = config.get("resource_limits", {}).get("max_gpu_memory_gb", 0)
        gpu_info = system_info.get("gpu_info")
        
        if max_gpu_memory > 0 and not gpu_info:
            errors.append("GPU memory limit specified but no GPU detected")
        elif gpu_info and max_gpu_memory > gpu_info.get("memory_gb", 0):
            errors.append(f"GPU memory limit ({max_gpu_memory}GB) exceeds GPU memory ({gpu_info['memory_gb']}GB)")
        
        # Storage validation
        data_dir = Path(config.get("system", {}).get("data_directory", "data"))
        if data_dir.exists():
            try:
                import shutil
                _, _, free_bytes = shutil.disk_usage(data_dir)
                free_gb = free_bytes / (1024**3)
                
                max_storage = config.get("resource_limits", {}).get("max_total_storage_gb", 0)
                if max_storage > free_gb:
                    errors.append(f"Storage limit ({max_storage}GB) exceeds available space ({free_gb:.1f}GB)")
            except Exception:
                errors.append("Could not validate storage constraints")
        
        return errors

    def _validate_hardware_compatibility(self, config: Dict[str, Any]) -> List[str]:
        """Validate configuration against hardware profile compatibility"""
        errors = []
        
        # Get current hardware profile
        hardware_profile = self.system_config.get_hardware_profile()
        profile_config = self.profile_manager.get_profile_config(hardware_profile)
        
        # Check if configuration exceeds profile recommendations
        profile_limits = profile_config.get("resource_limits", {})
        config_limits = config.get("resource_limits", {})
        
        for limit_name, profile_value in profile_limits.items():
            config_value = config_limits.get(limit_name, 0)
            if isinstance(profile_value, (int, float)) and config_value > profile_value * 1.2:
                errors.append(f"{limit_name} ({config_value}) significantly exceeds {hardware_profile.value} profile recommendation ({profile_value})")
        
        return errors

    def _validate_model_availability(self, config: Dict[str, Any]) -> List[str]:
        """Validate that configured models are available"""
        errors = []
        
        models_config = config.get("models", {})
        
        # Check LLM model availability
        llm_model = models_config.get("llm_model", "")
        if llm_model and not self._check_ollama_model_available(llm_model):
            errors.append(f"LLM model '{llm_model}' not available in Ollama")
        
        # Check embedding model
        embedding_model = models_config.get("embedding_model", "")
        if embedding_model and not self._check_embedding_model_available(embedding_model):
            errors.append(f"Embedding model '{embedding_model}' not available")
        
        # Check image generation model
        if models_config.get("enable_image_generation", False):
            image_model = models_config.get("image_generation_model", "")
            if image_model and not self._check_diffusion_model_available(image_model):
                errors.append(f"Image generation model '{image_model}' not available")
        
        return errors

    def _check_ollama_model_available(self, model_name: str) -> bool:
        """Check if Ollama model is available"""
        try:
            import requests
            ollama_url = self.system_config.get("networking.ollama_base_url", "http://localhost:11434")
            response = requests.get(f"{ollama_url}/api/tags", timeout=5)
            
            if response.status_code == 200:
                models = response.json().get("models", [])
                return any(model.get("name", "").startswith(model_name) for model in models)
        except Exception:
            pass
        
        return False

    def _check_embedding_model_available(self, model_name: str) -> bool:
        """Check if embedding model is available"""
        try:
            from sentence_transformers import SentenceTransformer
            # Try to load the model (this will download if not available)
            SentenceTransformer(model_name)
            return True
        except Exception:
            pass
        
        return False

    def _check_diffusion_model_available(self, model_name: str) -> bool:
        """Check if diffusion model is available"""
        try:
            from diffusers import StableDiffusionXLPipeline
            # This is a simplified check - in practice you'd want to check model availability
            return "stable-diffusion" in model_name.lower()
        except ImportError:
            return False

    def apply_configuration(self, config: Dict[str, Any], validate: bool = True) -> bool:
        """
        Apply new configuration with validation and backup.
        
        Args:
            config: New configuration to apply
            validate: Whether to validate before applying
            
        Returns:
            True if configuration was applied successfully
        """
        if validate:
            is_valid, errors = self.validate_configuration(config)
            if not is_valid:
                self.logger.error(f"Configuration validation failed: {errors}")
                return False
        
        # Backup current configuration
        self._backup_current_config()
        
        try:
            # Apply new configuration
            self.system_config.config = config
            
            # Save to user config file
            self.system_config.save_user_config()
            
            self.logger.info("Configuration applied successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to apply configuration: {e}")
            # Restore from backup
            self._restore_from_backup()
            return False

    def _backup_current_config(self):
        """Backup current configuration"""
        backup_entry = {
            "timestamp": datetime.now().isoformat(),
            "config": self.system_config.config.copy()
        }
        
        self.config_history.append(backup_entry)
        
        # Keep only recent backups
        if len(self.config_history) > self.max_history_entries:
            self.config_history = self.config_history[-self.max_history_entries:]

    def _restore_from_backup(self, backup_index: int = -1):
        """Restore configuration from backup"""
        if not self.config_history:
            self.logger.error("No configuration backup available")
            return False
        
        try:
            backup_entry = self.config_history[backup_index]
            self.system_config.config = backup_entry["config"]
            self.logger.info(f"Configuration restored from backup: {backup_entry['timestamp']}")
            return True
        except (IndexError, KeyError) as e:
            self.logger.error(f"Failed to restore from backup: {e}")
            return False

    def optimize_for_performance(self, performance_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Optimize configuration based on performance metrics.
        
        Args:
            performance_metrics: Recent performance data
            
        Returns:
            Optimized configuration
        """
        current_profile = self.system_config.get_hardware_profile()
        
        # Get auto-tuned profile configuration
        optimized_config = self.profile_manager.auto_tune_profile(
            current_profile, 
            [performance_metrics]
        )
        
        # Merge with current system configuration
        merged_config = self.system_config.config.copy()
        merged_config.update(optimized_config)
        
        return merged_config

    def export_configuration(self, file_path: Path, format: str = "json"):
        """
        Export current configuration to file.
        
        Args:
            file_path: Output file path
            format: Export format ("json" or "yaml")
        """
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "system_info": self.system_config.get_system_info(),
            "hardware_profile": self.system_config.get_hardware_profile().value,
            "configuration": self.system_config.config,
            "validation_status": self.validate_configuration()
        }
        
        try:
            if format.lower() == "yaml":
                with open(file_path, 'w') as f:
                    yaml.dump(export_data, f, default_flow_style=False, indent=2)
            else:
                with open(file_path, 'w') as f:
                    json.dump(export_data, f, indent=2)
            
            self.logger.info(f"Configuration exported to {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to export configuration: {e}")

    def import_configuration(self, file_path: Path, apply: bool = False) -> Optional[Dict[str, Any]]:
        """
        Import configuration from file.
        
        Args:
            file_path: Input file path
            apply: Whether to apply the imported configuration
            
        Returns:
            Imported configuration or None if failed
        """
        try:
            with open(file_path, 'r') as f:
                if file_path.suffix.lower() in ['.yml', '.yaml']:
                    import_data = yaml.safe_load(f)
                else:
                    import_data = json.load(f)
            
            config = import_data.get("configuration", {})
            
            if apply:
                success = self.apply_configuration(config, validate=True)
                if success:
                    self.logger.info(f"Configuration imported and applied from {file_path}")
                else:
                    self.logger.error(f"Failed to apply imported configuration from {file_path}")
            else:
                self.logger.info(f"Configuration imported from {file_path} (not applied)")
            
            return config
            
        except Exception as e:
            self.logger.error(f"Failed to import configuration: {e}")
            return None

    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration"""
        is_valid, validation_errors = self.validate_configuration()
        
        return {
            "hardware_profile": self.system_config.get_hardware_profile().value,
            "system_info": self.system_config.get_system_info(),
            "resource_limits": self.system_config.get_resource_limits().__dict__,
            "model_config": self.system_config.get_model_config().__dict__,
            "validation_status": {
                "is_valid": is_valid,
                "errors": validation_errors
            },
            "backup_count": len(self.config_history),
            "last_modified": datetime.now().isoformat()
        }

    def reset_to_defaults(self, hardware_profile: Optional[HardwareProfile] = None):
        """
        Reset configuration to defaults for the specified hardware profile.
        
        Args:
            hardware_profile: Target hardware profile (uses detected if None)
        """
        if hardware_profile is None:
            hardware_profile = self.system_config.get_hardware_profile()
        
        # Backup current configuration
        self._backup_current_config()
        
        # Get default configuration for profile
        default_config = self.profile_manager.get_profile_config(hardware_profile)
        
        # Apply default configuration
        success = self.apply_configuration(default_config, validate=True)
        
        if success:
            self.logger.info(f"Configuration reset to {hardware_profile.value} defaults")
        else:
            self.logger.error("Failed to reset configuration to defaults")
            self._restore_from_backup()

    def get_performance_recommendations(self) -> List[str]:
        """Get performance recommendations based on current configuration"""
        recommendations = []
        
        # Get resource manager recommendations
        resource_recommendations = self.resource_manager.get_performance_recommendations()
        recommendations.extend(resource_recommendations)
        
        # Get hardware profile recommendations
        current_profile = self.system_config.get_hardware_profile()
        
        # Analyze current configuration against profile
        current_limits = self.system_config.get_resource_limits()
        profile_config = self.profile_manager.get_profile_config(current_profile)
        profile_limits = profile_config.get("resource_limits", {})
        
        # Memory recommendations
        if current_limits.max_memory_gb < profile_limits.get("max_memory_gb", 0) * 0.8:
            recommendations.append("Consider increasing memory allocation for better performance")
        
        # CPU recommendations
        if current_limits.max_cpu_cores < profile_limits.get("max_cpu_cores", 0) * 0.8:
            recommendations.append("Consider increasing CPU core allocation for better parallelism")
        
        # Model recommendations
        model_config = self.system_config.get_model_config()
        if not model_config.enable_image_generation and current_profile in [HardwareProfile.PERFORMANCE, HardwareProfile.ENTERPRISE]:
            recommendations.append("Consider enabling image generation for enhanced study guides")
        
        return recommendations


# Global configuration manager instance
_config_manager: Optional[ConfigurationManager] = None


def get_config_manager() -> ConfigurationManager:
    """Get global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigurationManager()
    return _config_manager


def initialize_config_manager(config_dir: Optional[Path] = None) -> ConfigurationManager:
    """Initialize global configuration manager with custom directory"""
    global _config_manager
    _config_manager = ConfigurationManager(config_dir)
    return _config_manager
