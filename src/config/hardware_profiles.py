"""
Hardware Adaptation Profiles

Defines hardware-specific configurations and optimization strategies
for different system capabilities and performance requirements.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import psutil

from .system_config import HardwareProfile, ProcessingMode, ResourceLimits, ModelConfiguration


@dataclass
class ProcessingStrategy:
    """Processing strategy configuration"""
    batch_processing: bool
    parallel_agents: bool
    memory_optimization: bool
    cpu_optimization: bool
    gpu_acceleration: bool
    streaming_processing: bool
    checkpoint_frequency: int  # minutes
    max_retries: int


@dataclass
class QualitySettings:
    """Quality vs performance trade-off settings"""
    ocr_quality: str  # "fast", "balanced", "high"
    llm_quality: str  # "fast", "balanced", "high"
    embedding_quality: str  # "fast", "balanced", "high"
    image_generation_quality: str  # "fast", "balanced", "high"
    enable_quality_checks: bool
    enable_content_validation: bool


@dataclass
class HardwareOptimization:
    """Hardware-specific optimization settings"""
    memory_management: Dict[str, Any]
    cpu_optimization: Dict[str, Any]
    gpu_optimization: Dict[str, Any]
    storage_optimization: Dict[str, Any]
    network_optimization: Dict[str, Any]


class HardwareProfileManager:
    """
    Manages hardware-specific configurations and optimizations.
    
    Provides adaptive configuration based on detected hardware capabilities
    and performance requirements.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.profiles = self._initialize_profiles()

    def _initialize_profiles(self) -> Dict[HardwareProfile, Dict[str, Any]]:
        """Initialize all hardware profiles with their configurations"""
        return {
            HardwareProfile.MINIMAL: self._create_minimal_profile(),
            HardwareProfile.STANDARD: self._create_standard_profile(),
            HardwareProfile.PERFORMANCE: self._create_performance_profile(),
            HardwareProfile.ENTERPRISE: self._create_enterprise_profile()
        }

    def _create_minimal_profile(self) -> Dict[str, Any]:
        """Create configuration for minimal hardware systems"""
        return {
            "description": "Optimized for systems with limited resources (8GB RAM, 4 cores, no GPU)",
            "resource_limits": asdict(ResourceLimits(
                max_memory_gb=6.0,
                max_cpu_cores=3,
                max_gpu_memory_gb=0.0,
                max_concurrent_jobs=1,
                max_file_size_mb=50,
                max_total_storage_gb=10.0,
                processing_timeout_minutes=30
            )),
            "models": asdict(ModelConfiguration(
                llm_model="llama3:8b-instruct",
                llm_context_window=2048,
                llm_temperature=0.3,
                llm_max_tokens=1000,
                embedding_model="nomic-embed-text",
                embedding_dimension=768,
                ocr_model="paddleocr",
                ocr_confidence_threshold=0.7,
                image_generation_model="",
                enable_image_generation=False,
                vector_db_type="chromadb",
                vector_collection_size_limit=5000
            )),
            "processing_strategy": asdict(ProcessingStrategy(
                batch_processing=True,
                parallel_agents=False,
                memory_optimization=True,
                cpu_optimization=True,
                gpu_acceleration=False,
                streaming_processing=True,
                checkpoint_frequency=5,
                max_retries=2
            )),
            "quality_settings": asdict(QualitySettings(
                ocr_quality="fast",
                llm_quality="fast",
                embedding_quality="fast",
                image_generation_quality="fast",
                enable_quality_checks=False,
                enable_content_validation=False
            )),
            "hardware_optimization": asdict(HardwareOptimization(
                memory_management={
                    "enable_memory_mapping": True,
                    "use_swap": True,
                    "garbage_collection_frequency": "high",
                    "memory_pool_size": "small",
                    "enable_compression": True
                },
                cpu_optimization={
                    "thread_pool_size": 2,
                    "enable_cpu_affinity": False,
                    "process_priority": "normal",
                    "enable_hyperthreading": True
                },
                gpu_optimization={
                    "enable_gpu": False,
                    "gpu_memory_fraction": 0.0,
                    "mixed_precision": False
                },
                storage_optimization={
                    "use_ssd_cache": False,
                    "compression_level": "high",
                    "temp_file_cleanup": "aggressive",
                    "enable_deduplication": True
                },
                network_optimization={
                    "connection_pooling": True,
                    "request_batching": True,
                    "timeout_seconds": 30
                }
            )),
            "agent_settings": {
                "ingestion_triage": {
                    "batch_size": 5,
                    "enable_parallel_validation": False,
                    "memory_limit_mb": 512
                },
                "content_extraction": {
                    "ocr_batch_size": 1,
                    "enable_table_detection": False,
                    "image_processing_quality": "low"
                },
                "semantic_chunking": {
                    "chunk_size": 256,
                    "chunk_overlap": 25,
                    "embedding_batch_size": 10
                },
                "vectorization_indexing": {
                    "batch_size": 20,
                    "enable_indexing_optimization": True,
                    "memory_efficient_mode": True
                },
                "outline_generation": {
                    "max_outline_depth": 2,
                    "enable_cross_references": False
                },
                "knowledge_synthesizer": {
                    "context_window": 2048,
                    "max_synthesis_length": 1000,
                    "enable_fact_checking": False
                },
                "visual_analogy": {
                    "enable_diagrams": True,
                    "enable_analogies": True,
                    "enable_image_generation": False,
                    "max_diagrams_per_section": 1
                },
                "qa_generation": {
                    "questions_per_section": 3,
                    "enable_answer_validation": False,
                    "question_complexity": "simple"
                },
                "final_compiler": {
                    "latex_engine": "pdflatex",
                    "enable_advanced_formatting": False,
                    "image_quality": "medium"
                }
            }
        }

    def _create_standard_profile(self) -> Dict[str, Any]:
        """Create configuration for standard hardware systems"""
        return {
            "description": "Balanced configuration for typical systems (16GB RAM, 8 cores, optional GPU)",
            "resource_limits": asdict(ResourceLimits(
                max_memory_gb=12.0,
                max_cpu_cores=6,
                max_gpu_memory_gb=4.0,
                max_concurrent_jobs=2,
                max_file_size_mb=200,
                max_total_storage_gb=50.0,
                processing_timeout_minutes=60
            )),
            "models": asdict(ModelConfiguration(
                llm_model="llama3:8b-instruct",
                llm_context_window=4096,
                llm_temperature=0.3,
                llm_max_tokens=2000,
                embedding_model="bge-small-en-v1.5",
                embedding_dimension=384,
                ocr_model="paddleocr",
                ocr_confidence_threshold=0.8,
                image_generation_model="stabilityai/stable-diffusion-xl-base-1.0",
                enable_image_generation=True,
                vector_db_type="chromadb",
                vector_collection_size_limit=25000
            )),
            "processing_strategy": asdict(ProcessingStrategy(
                batch_processing=True,
                parallel_agents=True,
                memory_optimization=True,
                cpu_optimization=True,
                gpu_acceleration=True,
                streaming_processing=False,
                checkpoint_frequency=10,
                max_retries=3
            )),
            "quality_settings": asdict(QualitySettings(
                ocr_quality="balanced",
                llm_quality="balanced",
                embedding_quality="balanced",
                image_generation_quality="balanced",
                enable_quality_checks=True,
                enable_content_validation=True
            )),
            "hardware_optimization": asdict(HardwareOptimization(
                memory_management={
                    "enable_memory_mapping": True,
                    "use_swap": False,
                    "garbage_collection_frequency": "medium",
                    "memory_pool_size": "medium",
                    "enable_compression": False
                },
                cpu_optimization={
                    "thread_pool_size": 4,
                    "enable_cpu_affinity": True,
                    "process_priority": "high",
                    "enable_hyperthreading": True
                },
                gpu_optimization={
                    "enable_gpu": True,
                    "gpu_memory_fraction": 0.8,
                    "mixed_precision": True
                },
                storage_optimization={
                    "use_ssd_cache": True,
                    "compression_level": "medium",
                    "temp_file_cleanup": "normal",
                    "enable_deduplication": True
                },
                network_optimization={
                    "connection_pooling": True,
                    "request_batching": True,
                    "timeout_seconds": 60
                }
            )),
            "agent_settings": {
                "ingestion_triage": {
                    "batch_size": 10,
                    "enable_parallel_validation": True,
                    "memory_limit_mb": 1024
                },
                "content_extraction": {
                    "ocr_batch_size": 3,
                    "enable_table_detection": True,
                    "image_processing_quality": "medium"
                },
                "semantic_chunking": {
                    "chunk_size": 512,
                    "chunk_overlap": 50,
                    "embedding_batch_size": 20
                },
                "vectorization_indexing": {
                    "batch_size": 50,
                    "enable_indexing_optimization": True,
                    "memory_efficient_mode": False
                },
                "outline_generation": {
                    "max_outline_depth": 3,
                    "enable_cross_references": True
                },
                "knowledge_synthesizer": {
                    "context_window": 4096,
                    "max_synthesis_length": 2000,
                    "enable_fact_checking": True
                },
                "visual_analogy": {
                    "enable_diagrams": True,
                    "enable_analogies": True,
                    "enable_image_generation": True,
                    "max_diagrams_per_section": 2
                },
                "qa_generation": {
                    "questions_per_section": 5,
                    "enable_answer_validation": True,
                    "question_complexity": "medium"
                },
                "final_compiler": {
                    "latex_engine": "xelatex",
                    "enable_advanced_formatting": True,
                    "image_quality": "high"
                }
            }
        }

    def _create_performance_profile(self) -> Dict[str, Any]:
        """Create configuration for high-performance systems"""
        return {
            "description": "High-performance configuration (32GB RAM, 16+ cores, GPU required)",
            "resource_limits": asdict(ResourceLimits(
                max_memory_gb=24.0,
                max_cpu_cores=12,
                max_gpu_memory_gb=12.0,
                max_concurrent_jobs=4,
                max_file_size_mb=500,
                max_total_storage_gb=200.0,
                processing_timeout_minutes=120
            )),
            "models": asdict(ModelConfiguration(
                llm_model="llama3:70b-instruct",
                llm_context_window=8192,
                llm_temperature=0.3,
                llm_max_tokens=4000,
                embedding_model="bge-large-en-v1.5",
                embedding_dimension=1024,
                ocr_model="paddleocr",
                ocr_confidence_threshold=0.85,
                image_generation_model="stabilityai/stable-diffusion-xl-base-1.0",
                enable_image_generation=True,
                vector_db_type="chromadb",
                vector_collection_size_limit=100000
            )),
            "processing_strategy": asdict(ProcessingStrategy(
                batch_processing=True,
                parallel_agents=True,
                memory_optimization=False,
                cpu_optimization=True,
                gpu_acceleration=True,
                streaming_processing=False,
                checkpoint_frequency=15,
                max_retries=3
            )),
            "quality_settings": asdict(QualitySettings(
                ocr_quality="high",
                llm_quality="high",
                embedding_quality="high",
                image_generation_quality="high",
                enable_quality_checks=True,
                enable_content_validation=True
            )),
            "hardware_optimization": asdict(HardwareOptimization(
                memory_management={
                    "enable_memory_mapping": False,
                    "use_swap": False,
                    "garbage_collection_frequency": "low",
                    "memory_pool_size": "large",
                    "enable_compression": False
                },
                cpu_optimization={
                    "thread_pool_size": 8,
                    "enable_cpu_affinity": True,
                    "process_priority": "high",
                    "enable_hyperthreading": True
                },
                gpu_optimization={
                    "enable_gpu": True,
                    "gpu_memory_fraction": 0.9,
                    "mixed_precision": True
                },
                storage_optimization={
                    "use_ssd_cache": True,
                    "compression_level": "low",
                    "temp_file_cleanup": "normal",
                    "enable_deduplication": False
                },
                network_optimization={
                    "connection_pooling": True,
                    "request_batching": True,
                    "timeout_seconds": 120
                }
            )),
            "agent_settings": {
                "ingestion_triage": {
                    "batch_size": 20,
                    "enable_parallel_validation": True,
                    "memory_limit_mb": 2048
                },
                "content_extraction": {
                    "ocr_batch_size": 5,
                    "enable_table_detection": True,
                    "image_processing_quality": "high"
                },
                "semantic_chunking": {
                    "chunk_size": 1024,
                    "chunk_overlap": 100,
                    "embedding_batch_size": 50
                },
                "vectorization_indexing": {
                    "batch_size": 100,
                    "enable_indexing_optimization": True,
                    "memory_efficient_mode": False
                },
                "outline_generation": {
                    "max_outline_depth": 4,
                    "enable_cross_references": True
                },
                "knowledge_synthesizer": {
                    "context_window": 8192,
                    "max_synthesis_length": 4000,
                    "enable_fact_checking": True
                },
                "visual_analogy": {
                    "enable_diagrams": True,
                    "enable_analogies": True,
                    "enable_image_generation": True,
                    "max_diagrams_per_section": 3
                },
                "qa_generation": {
                    "questions_per_section": 7,
                    "enable_answer_validation": True,
                    "question_complexity": "high"
                },
                "final_compiler": {
                    "latex_engine": "xelatex",
                    "enable_advanced_formatting": True,
                    "image_quality": "high"
                }
            }
        }

    def _create_enterprise_profile(self) -> Dict[str, Any]:
        """Create configuration for enterprise-grade systems"""
        return {
            "description": "Enterprise configuration (64GB+ RAM, 32+ cores, multiple GPUs)",
            "resource_limits": asdict(ResourceLimits(
                max_memory_gb=48.0,
                max_cpu_cores=24,
                max_gpu_memory_gb=24.0,
                max_concurrent_jobs=8,
                max_file_size_mb=1000,
                max_total_storage_gb=1000.0,
                processing_timeout_minutes=240
            )),
            "models": asdict(ModelConfiguration(
                llm_model="llama3:70b-instruct",
                llm_context_window=16384,
                llm_temperature=0.3,
                llm_max_tokens=6000,
                embedding_model="bge-large-en-v1.5",
                embedding_dimension=1024,
                ocr_model="paddleocr",
                ocr_confidence_threshold=0.9,
                image_generation_model="stabilityai/stable-diffusion-xl-base-1.0",
                enable_image_generation=True,
                vector_db_type="chromadb",
                vector_collection_size_limit=500000
            )),
            "processing_strategy": asdict(ProcessingStrategy(
                batch_processing=True,
                parallel_agents=True,
                memory_optimization=False,
                cpu_optimization=True,
                gpu_acceleration=True,
                streaming_processing=False,
                checkpoint_frequency=20,
                max_retries=5
            )),
            "quality_settings": asdict(QualitySettings(
                ocr_quality="high",
                llm_quality="high",
                embedding_quality="high",
                image_generation_quality="high",
                enable_quality_checks=True,
                enable_content_validation=True
            )),
            "hardware_optimization": asdict(HardwareOptimization(
                memory_management={
                    "enable_memory_mapping": False,
                    "use_swap": False,
                    "garbage_collection_frequency": "low",
                    "memory_pool_size": "xlarge",
                    "enable_compression": False
                },
                cpu_optimization={
                    "thread_pool_size": 16,
                    "enable_cpu_affinity": True,
                    "process_priority": "realtime",
                    "enable_hyperthreading": True
                },
                gpu_optimization={
                    "enable_gpu": True,
                    "gpu_memory_fraction": 0.95,
                    "mixed_precision": True
                },
                storage_optimization={
                    "use_ssd_cache": True,
                    "compression_level": "none",
                    "temp_file_cleanup": "lazy",
                    "enable_deduplication": False
                },
                network_optimization={
                    "connection_pooling": True,
                    "request_batching": True,
                    "timeout_seconds": 300
                }
            )),
            "agent_settings": {
                "ingestion_triage": {
                    "batch_size": 50,
                    "enable_parallel_validation": True,
                    "memory_limit_mb": 4096
                },
                "content_extraction": {
                    "ocr_batch_size": 10,
                    "enable_table_detection": True,
                    "image_processing_quality": "highest"
                },
                "semantic_chunking": {
                    "chunk_size": 2048,
                    "chunk_overlap": 200,
                    "embedding_batch_size": 100
                },
                "vectorization_indexing": {
                    "batch_size": 200,
                    "enable_indexing_optimization": True,
                    "memory_efficient_mode": False
                },
                "outline_generation": {
                    "max_outline_depth": 5,
                    "enable_cross_references": True
                },
                "knowledge_synthesizer": {
                    "context_window": 16384,
                    "max_synthesis_length": 6000,
                    "enable_fact_checking": True
                },
                "visual_analogy": {
                    "enable_diagrams": True,
                    "enable_analogies": True,
                    "enable_image_generation": True,
                    "max_diagrams_per_section": 4
                },
                "qa_generation": {
                    "questions_per_section": 10,
                    "enable_answer_validation": True,
                    "question_complexity": "expert"
                },
                "final_compiler": {
                    "latex_engine": "xelatex",
                    "enable_advanced_formatting": True,
                    "image_quality": "highest"
                }
            }
        }

    def get_profile_config(self, profile: HardwareProfile) -> Dict[str, Any]:
        """Get configuration for a specific hardware profile"""
        return self.profiles.get(profile, self.profiles[HardwareProfile.STANDARD])

    def get_agent_config(self, profile: HardwareProfile, agent_name: str) -> Dict[str, Any]:
        """Get agent-specific configuration for a hardware profile"""
        profile_config = self.get_profile_config(profile)
        return profile_config.get("agent_settings", {}).get(agent_name, {})

    def get_optimization_settings(self, profile: HardwareProfile) -> HardwareOptimization:
        """Get hardware optimization settings for a profile"""
        profile_config = self.get_profile_config(profile)
        optimization_dict = profile_config.get("hardware_optimization", {})
        return HardwareOptimization(**optimization_dict)

    def get_processing_strategy(self, profile: HardwareProfile) -> ProcessingStrategy:
        """Get processing strategy for a profile"""
        profile_config = self.get_profile_config(profile)
        strategy_dict = profile_config.get("processing_strategy", {})
        return ProcessingStrategy(**strategy_dict)

    def get_quality_settings(self, profile: HardwareProfile) -> QualitySettings:
        """Get quality settings for a profile"""
        profile_config = self.get_profile_config(profile)
        quality_dict = profile_config.get("quality_settings", {})
        return QualitySettings(**quality_dict)

    def recommend_profile_adjustments(
        self, 
        current_profile: HardwareProfile,
        performance_metrics: Dict[str, Any]
    ) -> List[str]:
        """Recommend profile adjustments based on performance metrics"""
        recommendations = []
        
        # Analyze performance metrics
        avg_memory_usage = performance_metrics.get("avg_memory_usage_percent", 0)
        avg_cpu_usage = performance_metrics.get("avg_cpu_usage_percent", 0)
        avg_processing_time = performance_metrics.get("avg_processing_time_minutes", 0)
        error_rate = performance_metrics.get("error_rate_percent", 0)
        
        # Memory-based recommendations
        if avg_memory_usage > 90:
            recommendations.append("Consider reducing batch sizes or enabling memory optimization")
        elif avg_memory_usage < 30 and current_profile != HardwareProfile.PERFORMANCE:
            recommendations.append("System has excess memory - consider upgrading to higher performance profile")
        
        # CPU-based recommendations
        if avg_cpu_usage > 95:
            recommendations.append("CPU is saturated - consider reducing concurrent jobs or parallel processing")
        elif avg_cpu_usage < 20:
            recommendations.append("CPU is underutilized - consider increasing parallelism or batch sizes")
        
        # Performance-based recommendations
        if avg_processing_time > 60 and current_profile == HardwareProfile.MINIMAL:
            recommendations.append("Processing times are high - consider upgrading hardware profile")
        
        # Error-based recommendations
        if error_rate > 5:
            recommendations.append("High error rate detected - consider enabling quality checks and validation")
        
        return recommendations

    def auto_tune_profile(
        self, 
        profile: HardwareProfile, 
        performance_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Automatically tune profile settings based on performance history"""
        if not performance_history:
            return self.get_profile_config(profile)
        
        # Calculate performance statistics
        avg_metrics = self._calculate_average_metrics(performance_history)
        
        # Get base profile configuration
        config = self.get_profile_config(profile).copy()
        
        # Adjust batch sizes based on memory usage
        if avg_metrics.get("memory_usage_percent", 0) > 85:
            # Reduce batch sizes
            for agent_name, agent_config in config.get("agent_settings", {}).items():
                if "batch_size" in agent_config:
                    agent_config["batch_size"] = max(1, int(agent_config["batch_size"] * 0.8))
        
        # Adjust parallelism based on CPU usage
        if avg_metrics.get("cpu_usage_percent", 0) < 30:
            # Increase parallelism
            config["processing_strategy"]["parallel_agents"] = True
            for agent_name, agent_config in config.get("agent_settings", {}).items():
                if "enable_parallel_validation" in agent_config:
                    agent_config["enable_parallel_validation"] = True
        
        # Adjust quality settings based on error rates
        if avg_metrics.get("error_rate_percent", 0) > 3:
            # Increase quality settings
            config["quality_settings"]["enable_quality_checks"] = True
            config["quality_settings"]["enable_content_validation"] = True
        
        return config

    def _calculate_average_metrics(self, performance_history: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate average performance metrics"""
        if not performance_history:
            return {}
        
        metrics = {}
        for key in ["memory_usage_percent", "cpu_usage_percent", "processing_time_minutes", "error_rate_percent"]:
            values = [entry.get(key, 0) for entry in performance_history if key in entry]
            if values:
                metrics[key] = sum(values) / len(values)
        
        return metrics


# Global hardware profile manager
_profile_manager: Optional[HardwareProfileManager] = None


def get_profile_manager() -> HardwareProfileManager:
    """Get global hardware profile manager instance"""
    global _profile_manager
    if _profile_manager is None:
        _profile_manager = HardwareProfileManager()
    return _profile_manager
