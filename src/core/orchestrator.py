"""
Job Orchestrator

Manages the lifecycle of multi-agent processing jobs, including state transitions,
agent coordination, failure recovery, and resource management.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Type, Any
from pathlib import Path

from ..models.job_manifest import JobManifest, JobState, AgentState
from ..database.models import JobRepository
from ..core.base_agent import BaseAgent, AgentError
from ..utils.logging import get_correlation_logger
from ..utils.backup import BackupManager
from ..utils.resource_manager import ResourceManager


class OrchestratorError(Exception):
    """Orchestrator-specific errors"""
    pass


class JobOrchestrator:
    """
    Orchestrates multi-agent job processing with state management,
    failure recovery, and resource coordination.
    """
    
    def __init__(
        self,
        job_repository: JobRepository,
        backup_manager: BackupManager,
        resource_manager: ResourceManager,
        config: Dict[str, Any]
    ):
        self.job_repository = job_repository
        self.backup_manager = backup_manager
        self.resource_manager = resource_manager
        self.config = config
        
        self.logger = logging.getLogger("orchestrator")
        
        # Agent registry and processing pipeline
        self.agents: Dict[str, BaseAgent] = {}
        self.processing_pipeline: List[str] = [
            "ingestion_triage",
            "content_extraction", 
            "semantic_chunking",
            "vectorization_indexing",
            "outline_generation",
            "knowledge_synthesizer",
            "visual_analogy",
            "qa_generation",
            "final_compiler"
        ]
        
        # Active job tracking
        self.active_jobs: Dict[str, asyncio.Task] = {}
        self.max_concurrent_jobs = config.get("max_concurrent_jobs", 3)
        
        # State transition rules
        self.state_transitions = {
            JobState.PENDING: [JobState.VALIDATING, JobState.CANCELLED],
            JobState.VALIDATING: [JobState.EXTRACTING, JobState.FAILED, JobState.PAUSED],
            JobState.EXTRACTING: [JobState.CHUNKING, JobState.FAILED, JobState.PAUSED],
            JobState.CHUNKING: [JobState.VECTORIZING, JobState.FAILED, JobState.PAUSED],
            JobState.VECTORIZING: [JobState.OUTLINING, JobState.FAILED, JobState.PAUSED],
            JobState.OUTLINING: [JobState.SYNTHESIZING, JobState.FAILED, JobState.PAUSED],
            JobState.SYNTHESIZING: [JobState.ENHANCING, JobState.FAILED, JobState.PAUSED],
            JobState.ENHANCING: [JobState.GENERATING_QA, JobState.FAILED, JobState.PAUSED],
            JobState.GENERATING_QA: [JobState.COMPILING, JobState.FAILED, JobState.PAUSED],
            JobState.COMPILING: [JobState.COMPLETED, JobState.FAILED, JobState.PAUSED],
            JobState.FAILED: [JobState.VALIDATING, JobState.CANCELLED],  # Retry from beginning
            JobState.PAUSED: [JobState.VALIDATING, JobState.CANCELLED],  # Resume from current
            JobState.COMPLETED: [JobState.CANCELLED],  # Allow cleanup
            JobState.CANCELLED: []  # Terminal state
        }

    def register_agent(self, agent_name: str, agent_class: Type[BaseAgent], config: Dict[str, Any]):
        """Register an agent for processing"""
        agent = agent_class(agent_name, config)
        self.agents[agent_name] = agent
        self.logger.info(f"Registered agent: {agent_name}")

    async def submit_job(self, job_manifest: JobManifest) -> str:
        """
        Submit a new job for processing.
        
        Args:
            job_manifest: Complete job specification
            
        Returns:
            Job ID for tracking
            
        Raises:
            OrchestratorError: If job cannot be submitted
        """
        # Validate job manifest
        if not job_manifest.documents:
            raise OrchestratorError("Job must contain at least one document")
        
        # Check concurrent job limits
        if len(self.active_jobs) >= self.max_concurrent_jobs:
            raise OrchestratorError(f"Maximum concurrent jobs ({self.max_concurrent_jobs}) reached")
        
        # Save job to database
        await self.job_repository.create_job(job_manifest)
        
        # Create backup
        await self.backup_manager.create_job_backup(job_manifest)
        
        # Start processing
        task = asyncio.create_task(self._process_job(job_manifest))
        self.active_jobs[job_manifest.job_id] = task
        
        self.logger.info(f"Submitted job {job_manifest.job_id} for processing")
        return job_manifest.job_id

    async def pause_job(self, job_id: str) -> bool:
        """Pause an active job"""
        if job_id not in self.active_jobs:
            return False
        
        job_manifest = await self.job_repository.get_job(job_id)
        if not job_manifest:
            return False
        
        # Update state
        await self._transition_job_state(job_manifest, JobState.PAUSED)
        
        # Cancel processing task
        task = self.active_jobs[job_id]
        task.cancel()
        
        try:
            await task
        except asyncio.CancelledError:
            pass
        
        del self.active_jobs[job_id]
        
        self.logger.info(f"Paused job {job_id}")
        return True

    async def resume_job(self, job_id: str) -> bool:
        """Resume a paused job"""
        job_manifest = await self.job_repository.get_job(job_id)
        if not job_manifest or job_manifest.current_state != JobState.PAUSED:
            return False
        
        # Check concurrent job limits
        if len(self.active_jobs) >= self.max_concurrent_jobs:
            raise OrchestratorError(f"Maximum concurrent jobs ({self.max_concurrent_jobs}) reached")
        
        # Resume from current state
        task = asyncio.create_task(self._process_job(job_manifest, resume=True))
        self.active_jobs[job_id] = task
        
        self.logger.info(f"Resumed job {job_id}")
        return True

    async def cancel_job(self, job_id: str) -> bool:
        """Cancel an active or paused job"""
        job_manifest = await self.job_repository.get_job(job_id)
        if not job_manifest:
            return False
        
        # Update state
        await self._transition_job_state(job_manifest, JobState.CANCELLED)
        
        # Cancel processing task if active
        if job_id in self.active_jobs:
            task = self.active_jobs[job_id]
            task.cancel()
            
            try:
                await task
            except asyncio.CancelledError:
                pass
            
            del self.active_jobs[job_id]
        
        self.logger.info(f"Cancelled job {job_id}")
        return True

    async def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive job status"""
        job_manifest = await self.job_repository.get_job(job_id)
        if not job_manifest:
            return None
        
        return {
            "job_id": job_id,
            "state": job_manifest.current_state,
            "progress": job_manifest.overall_progress,
            "created_at": job_manifest.created_at,
            "updated_at": job_manifest.updated_at,
            "estimated_completion": job_manifest.estimated_completion_time,
            "documents": len(job_manifest.documents),
            "stages": {
                name: {
                    "state": stage.state,
                    "progress": stage.progress_percentage,
                    "retry_count": stage.retry_count
                }
                for name, stage in job_manifest.processing_stages.items()
            },
            "errors": len(job_manifest.errors),
            "is_active": job_id in self.active_jobs
        }

    async def _process_job(self, job_manifest: JobManifest, resume: bool = False) -> None:
        """
        Main job processing pipeline.
        
        Args:
            job_manifest: Job to process
            resume: Whether this is resuming a paused job
        """
        correlation_logger = get_correlation_logger(self.logger, job_manifest.correlation_id)
        
        try:
            correlation_logger.info(f"Starting job processing (resume={resume})")
            
            # Determine starting point
            start_index = 0
            if resume:
                start_index = self._find_resume_point(job_manifest)
            
            # Process through pipeline
            for i in range(start_index, len(self.processing_pipeline)):
                agent_name = self.processing_pipeline[i]
                
                # Check if job was cancelled or paused
                current_job = await self.job_repository.get_job(job_manifest.job_id)
                if current_job.current_state in [JobState.CANCELLED, JobState.PAUSED]:
                    correlation_logger.info(f"Job {current_job.current_state}, stopping processing")
                    return
                
                # Update job state
                state_map = {
                    "ingestion_triage": JobState.VALIDATING,
                    "content_extraction": JobState.EXTRACTING,
                    "semantic_chunking": JobState.CHUNKING,
                    "vectorization_indexing": JobState.VECTORIZING,
                    "outline_generation": JobState.OUTLINING,
                    "knowledge_synthesizer": JobState.SYNTHESIZING,
                    "visual_analogy": JobState.ENHANCING,
                    "qa_generation": JobState.GENERATING_QA,
                    "final_compiler": JobState.COMPILING
                }
                
                new_state = state_map.get(agent_name, current_job.current_state)
                await self._transition_job_state(current_job, new_state, agent_name)
                
                # Execute agent
                agent = self.agents.get(agent_name)
                if not agent:
                    raise OrchestratorError(f"Agent {agent_name} not registered")
                
                correlation_logger.info(f"Executing agent: {agent_name}")
                
                # Create checkpoint before processing
                await self.backup_manager.create_checkpoint(current_job)
                
                try:
                    # Execute with resource management
                    async with self.resource_manager.allocate_resources(agent_name):
                        result = await agent.execute(current_job)
                        
                    # Update job manifest with results
                    if result.get("output_artifacts"):
                        current_job.output_artifacts.extend(result["output_artifacts"])
                    
                    # Save progress
                    await self.job_repository.update_job(current_job)
                    
                except AgentError as e:
                    if not e.recoverable:
                        # Unrecoverable error - fail the job
                        await self._transition_job_state(current_job, JobState.FAILED, agent_name)
                        raise
                    
                    # Recoverable error - already handled by agent retry logic
                    correlation_logger.warning(f"Agent {agent_name} failed but may retry: {e}")
                    raise
                
                # Update overall progress
                current_job.update_progress()
                await self.job_repository.update_job(current_job)
            
            # Job completed successfully
            await self._transition_job_state(job_manifest, JobState.COMPLETED)
            correlation_logger.info("Job processing completed successfully")
            
        except asyncio.CancelledError:
            correlation_logger.info("Job processing cancelled")
            raise
            
        except Exception as e:
            correlation_logger.error(f"Job processing failed: {e}")
            await self._transition_job_state(job_manifest, JobState.FAILED)
            raise
            
        finally:
            # Clean up active job tracking
            if job_manifest.job_id in self.active_jobs:
                del self.active_jobs[job_manifest.job_id]

    def _find_resume_point(self, job_manifest: JobManifest) -> int:
        """Find the appropriate point to resume processing"""
        for i, agent_name in enumerate(self.processing_pipeline):
            stage = job_manifest.processing_stages.get(agent_name)
            if not stage or stage.state != AgentState.COMPLETED:
                return i
        return len(self.processing_pipeline)  # All stages completed

    async def _transition_job_state(
        self, 
        job_manifest: JobManifest, 
        new_state: JobState, 
        agent_name: Optional[str] = None
    ) -> None:
        """
        Safely transition job state with validation.
        
        Args:
            job_manifest: Job to update
            new_state: Target state
            agent_name: Agent triggering the transition
        """
        current_state = job_manifest.current_state
        
        # Validate transition
        allowed_transitions = self.state_transitions.get(current_state, [])
        if new_state not in allowed_transitions:
            raise OrchestratorError(
                f"Invalid state transition: {current_state} -> {new_state}"
            )
        
        # Update manifest
        job_manifest.update_state(new_state, agent_name)
        
        # Estimate completion time
        if new_state in [JobState.VALIDATING, JobState.EXTRACTING]:
            estimated_duration = self._estimate_job_duration(job_manifest)
            job_manifest.estimated_completion_time = datetime.utcnow() + timedelta(seconds=estimated_duration)
        
        # Save to database
        await self.job_repository.update_job(job_manifest)
        
        self.logger.info(f"Job {job_manifest.job_id} transitioned: {current_state} -> {new_state}")

    def _estimate_job_duration(self, job_manifest: JobManifest) -> float:
        """Estimate total job processing time in seconds"""
        total_duration = 0.0
        
        for agent_name in self.processing_pipeline:
            agent = self.agents.get(agent_name)
            if agent:
                duration = agent.estimate_processing_time(job_manifest)
                total_duration += duration
        
        return total_duration

    async def cleanup_completed_jobs(self, retention_days: int = 30) -> int:
        """Clean up old completed jobs"""
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        completed_jobs = await self.job_repository.get_jobs_by_state(
            JobState.COMPLETED, 
            before_date=cutoff_date
        )
        
        cleaned_count = 0
        for job in completed_jobs:
            # Clean up artifacts
            for artifact_path in job.output_artifacts:
                try:
                    Path(artifact_path).unlink(missing_ok=True)
                except Exception as e:
                    self.logger.warning(f"Failed to delete artifact {artifact_path}: {e}")
            
            # Remove from database
            await self.job_repository.delete_job(job.job_id)
            cleaned_count += 1
        
        self.logger.info(f"Cleaned up {cleaned_count} completed jobs")
        return cleaned_count

    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        active_jobs = await self.job_repository.get_jobs_by_state(
            [JobState.VALIDATING, JobState.EXTRACTING, JobState.CHUNKING,
             JobState.VECTORIZING, JobState.OUTLINING, JobState.SYNTHESIZING,
             JobState.ENHANCING, JobState.GENERATING_QA, JobState.COMPILING]
        )
        
        return {
            "active_jobs": len(active_jobs),
            "max_concurrent_jobs": self.max_concurrent_jobs,
            "registered_agents": list(self.agents.keys()),
            "resource_usage": await self.resource_manager.get_current_usage(),
            "system_health": "healthy"  # TODO: Implement health checks
        }
