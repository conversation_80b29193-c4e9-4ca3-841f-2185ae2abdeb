"""
Resource Management System

Monitors and manages system resources including memory, CPU, GPU, and storage
with adaptive allocation and automatic scaling based on workload.
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import psutil
import threading
from contextlib import asynccontextmanager

from ..config.system_config import get_config, ResourceLimits


class ResourceType(Enum):
    """Types of system resources"""
    MEMORY = "memory"
    CPU = "cpu"
    GPU = "gpu"
    STORAGE = "storage"
    NETWORK = "network"


class ResourceStatus(Enum):
    """Resource availability status"""
    AVAILABLE = "available"
    LIMITED = "limited"
    CRITICAL = "critical"
    EXHAUSTED = "exhausted"


@dataclass
class ResourceMetrics:
    """Current resource usage metrics"""
    timestamp: float
    memory_used_gb: float
    memory_available_gb: float
    memory_percent: float
    cpu_percent: float
    cpu_count: int
    gpu_memory_used_gb: float
    gpu_memory_total_gb: float
    gpu_utilization_percent: float
    disk_used_gb: float
    disk_available_gb: float
    disk_percent: float
    active_jobs: int
    pending_jobs: int


@dataclass
class ResourceAllocation:
    """Resource allocation for a specific job or agent"""
    job_id: str
    agent_name: str
    memory_gb: float
    cpu_cores: int
    gpu_memory_gb: float
    priority: int
    allocated_at: float
    estimated_duration: float


class ResourceManager:
    """
    System resource manager with adaptive allocation and monitoring.
    
    Features:
    - Real-time resource monitoring
    - Adaptive resource allocation based on workload
    - Resource reservation and cleanup
    - Performance optimization recommendations
    - Automatic scaling and throttling
    """
    
    def __init__(self):
        self.config = get_config()
        self.logger = logging.getLogger(__name__)
        
        # Resource limits from configuration
        self.limits = self.config.get_resource_limits()
        
        # Current allocations
        self.allocations: Dict[str, ResourceAllocation] = {}
        self.allocation_lock = threading.Lock()
        
        # Monitoring
        self.metrics_history: List[ResourceMetrics] = []
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # Performance tracking
        self.performance_stats = {
            "jobs_completed": 0,
            "jobs_failed": 0,
            "average_processing_time": 0.0,
            "resource_efficiency": 0.0
        }
        
        # Initialize GPU monitoring if available
        self.gpu_available = self._check_gpu_availability()
        
        self.logger.info(f"Resource manager initialized with limits: {self.limits}")

    def _check_gpu_availability(self) -> bool:
        """Check if GPU monitoring is available"""
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            return len(gpus) > 0
        except ImportError:
            pass
        
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            pass
        
        return False

    async def start_monitoring(self, interval_seconds: float = 5.0):
        """Start continuous resource monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(
            self._monitoring_loop(interval_seconds)
        )
        self.logger.info("Resource monitoring started")

    async def stop_monitoring(self):
        """Stop resource monitoring"""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        self.logger.info("Resource monitoring stopped")

    async def _monitoring_loop(self, interval_seconds: float):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                metrics = await self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # Keep only recent metrics (last hour)
                max_metrics = int(3600 / interval_seconds)
                if len(self.metrics_history) > max_metrics:
                    self.metrics_history = self.metrics_history[-max_metrics:]
                
                # Check for resource issues
                await self._check_resource_health(metrics)
                
                await asyncio.sleep(interval_seconds)
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(interval_seconds)

    async def _collect_metrics(self) -> ResourceMetrics:
        """Collect current resource metrics"""
        def _collect():
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_used_gb = (memory.total - memory.available) / (1024**3)
            memory_available_gb = memory.available / (1024**3)
            
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count(logical=True)
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_used_gb = disk.used / (1024**3)
            disk_available_gb = disk.free / (1024**3)
            disk_percent = (disk.used / disk.total) * 100
            
            # GPU metrics
            gpu_memory_used_gb = 0.0
            gpu_memory_total_gb = 0.0
            gpu_utilization_percent = 0.0
            
            if self.gpu_available:
                try:
                    import GPUtil
                    gpus = GPUtil.getGPUs()
                    if gpus:
                        gpu = gpus[0]
                        gpu_memory_used_gb = gpu.memoryUsed / 1024
                        gpu_memory_total_gb = gpu.memoryTotal / 1024
                        gpu_utilization_percent = gpu.load * 100
                except Exception:
                    pass
            
            return ResourceMetrics(
                timestamp=time.time(),
                memory_used_gb=memory_used_gb,
                memory_available_gb=memory_available_gb,
                memory_percent=memory.percent,
                cpu_percent=cpu_percent,
                cpu_count=cpu_count,
                gpu_memory_used_gb=gpu_memory_used_gb,
                gpu_memory_total_gb=gpu_memory_total_gb,
                gpu_utilization_percent=gpu_utilization_percent,
                disk_used_gb=disk_used_gb,
                disk_available_gb=disk_available_gb,
                disk_percent=disk_percent,
                active_jobs=len(self.allocations),
                pending_jobs=0  # Would be populated from job queue
            )
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _collect)

    async def _check_resource_health(self, metrics: ResourceMetrics):
        """Check resource health and log warnings"""
        # Memory warnings
        if metrics.memory_percent > 90:
            self.logger.warning(f"High memory usage: {metrics.memory_percent:.1f}%")
        elif metrics.memory_percent > 80:
            self.logger.info(f"Elevated memory usage: {metrics.memory_percent:.1f}%")
        
        # CPU warnings
        if metrics.cpu_percent > 90:
            self.logger.warning(f"High CPU usage: {metrics.cpu_percent:.1f}%")
        
        # Disk warnings
        if metrics.disk_percent > 90:
            self.logger.warning(f"Low disk space: {metrics.disk_available_gb:.1f}GB available")
        
        # GPU warnings
        if self.gpu_available and metrics.gpu_utilization_percent > 95:
            self.logger.warning(f"High GPU utilization: {metrics.gpu_utilization_percent:.1f}%")

    @asynccontextmanager
    async def allocate_resources(
        self,
        job_id: str,
        agent_name: str,
        memory_gb: float,
        cpu_cores: int = 1,
        gpu_memory_gb: float = 0.0,
        priority: int = 1,
        estimated_duration: float = 300.0
    ):
        """
        Context manager for resource allocation with automatic cleanup.
        
        Args:
            job_id: Unique job identifier
            agent_name: Name of the requesting agent
            memory_gb: Required memory in GB
            cpu_cores: Required CPU cores
            gpu_memory_gb: Required GPU memory in GB
            priority: Job priority (1=highest, 10=lowest)
            estimated_duration: Estimated processing time in seconds
        """
        allocation_key = f"{job_id}_{agent_name}"
        
        # Check if resources are available
        if not await self._check_resource_availability(memory_gb, cpu_cores, gpu_memory_gb):
            raise ResourceError(f"Insufficient resources for {agent_name}")
        
        # Create allocation
        allocation = ResourceAllocation(
            job_id=job_id,
            agent_name=agent_name,
            memory_gb=memory_gb,
            cpu_cores=cpu_cores,
            gpu_memory_gb=gpu_memory_gb,
            priority=priority,
            allocated_at=time.time(),
            estimated_duration=estimated_duration
        )
        
        try:
            # Reserve resources
            with self.allocation_lock:
                self.allocations[allocation_key] = allocation
            
            self.logger.info(f"Resources allocated for {agent_name}: "
                           f"{memory_gb}GB RAM, {cpu_cores} cores, {gpu_memory_gb}GB GPU")
            
            yield allocation
            
        finally:
            # Clean up allocation
            with self.allocation_lock:
                if allocation_key in self.allocations:
                    del self.allocations[allocation_key]
            
            self.logger.info(f"Resources released for {agent_name}")

    async def _check_resource_availability(
        self,
        memory_gb: float,
        cpu_cores: int,
        gpu_memory_gb: float
    ) -> bool:
        """Check if requested resources are available"""
        
        # Get current metrics
        current_metrics = await self._collect_metrics()
        
        # Calculate currently allocated resources
        with self.allocation_lock:
            allocated_memory = sum(a.memory_gb for a in self.allocations.values())
            allocated_cpu = sum(a.cpu_cores for a in self.allocations.values())
            allocated_gpu = sum(a.gpu_memory_gb for a in self.allocations.values())
        
        # Check against limits and availability
        total_memory_needed = allocated_memory + memory_gb
        total_cpu_needed = allocated_cpu + cpu_cores
        total_gpu_needed = allocated_gpu + gpu_memory_gb
        
        # Memory check
        if total_memory_needed > self.limits.max_memory_gb:
            self.logger.warning(f"Memory limit exceeded: {total_memory_needed}GB > {self.limits.max_memory_gb}GB")
            return False
        
        if total_memory_needed > current_metrics.memory_available_gb * 0.9:
            self.logger.warning(f"Insufficient available memory: {total_memory_needed}GB needed, "
                              f"{current_metrics.memory_available_gb}GB available")
            return False
        
        # CPU check
        if total_cpu_needed > self.limits.max_cpu_cores:
            self.logger.warning(f"CPU limit exceeded: {total_cpu_needed} > {self.limits.max_cpu_cores}")
            return False
        
        # GPU check
        if gpu_memory_gb > 0:
            if not self.gpu_available:
                self.logger.warning("GPU memory requested but no GPU available")
                return False
            
            if total_gpu_needed > self.limits.max_gpu_memory_gb:
                self.logger.warning(f"GPU memory limit exceeded: {total_gpu_needed}GB > {self.limits.max_gpu_memory_gb}GB")
                return False
        
        return True

    def get_current_metrics(self) -> Optional[ResourceMetrics]:
        """Get the most recent resource metrics"""
        if self.metrics_history:
            return self.metrics_history[-1]
        return None

    def get_resource_status(self) -> Dict[ResourceType, ResourceStatus]:
        """Get current status of all resource types"""
        metrics = self.get_current_metrics()
        if not metrics:
            return {rt: ResourceStatus.AVAILABLE for rt in ResourceType}
        
        status = {}
        
        # Memory status
        if metrics.memory_percent > 95:
            status[ResourceType.MEMORY] = ResourceStatus.EXHAUSTED
        elif metrics.memory_percent > 85:
            status[ResourceType.MEMORY] = ResourceStatus.CRITICAL
        elif metrics.memory_percent > 70:
            status[ResourceType.MEMORY] = ResourceStatus.LIMITED
        else:
            status[ResourceType.MEMORY] = ResourceStatus.AVAILABLE
        
        # CPU status
        if metrics.cpu_percent > 95:
            status[ResourceType.CPU] = ResourceStatus.EXHAUSTED
        elif metrics.cpu_percent > 85:
            status[ResourceType.CPU] = ResourceStatus.CRITICAL
        elif metrics.cpu_percent > 70:
            status[ResourceType.CPU] = ResourceStatus.LIMITED
        else:
            status[ResourceType.CPU] = ResourceStatus.AVAILABLE
        
        # GPU status
        if self.gpu_available:
            if metrics.gpu_utilization_percent > 95:
                status[ResourceType.GPU] = ResourceStatus.EXHAUSTED
            elif metrics.gpu_utilization_percent > 85:
                status[ResourceType.GPU] = ResourceStatus.CRITICAL
            elif metrics.gpu_utilization_percent > 70:
                status[ResourceType.GPU] = ResourceStatus.LIMITED
            else:
                status[ResourceType.GPU] = ResourceStatus.AVAILABLE
        else:
            status[ResourceType.GPU] = ResourceStatus.EXHAUSTED
        
        # Storage status
        if metrics.disk_percent > 95:
            status[ResourceType.STORAGE] = ResourceStatus.EXHAUSTED
        elif metrics.disk_percent > 90:
            status[ResourceType.STORAGE] = ResourceStatus.CRITICAL
        elif metrics.disk_percent > 80:
            status[ResourceType.STORAGE] = ResourceStatus.LIMITED
        else:
            status[ResourceType.STORAGE] = ResourceStatus.AVAILABLE
        
        # Network status (simplified)
        status[ResourceType.NETWORK] = ResourceStatus.AVAILABLE
        
        return status

    def get_allocation_summary(self) -> Dict[str, Any]:
        """Get summary of current resource allocations"""
        with self.allocation_lock:
            allocations = list(self.allocations.values())
        
        if not allocations:
            return {
                "total_allocations": 0,
                "total_memory_gb": 0.0,
                "total_cpu_cores": 0,
                "total_gpu_memory_gb": 0.0,
                "allocations_by_agent": {}
            }
        
        total_memory = sum(a.memory_gb for a in allocations)
        total_cpu = sum(a.cpu_cores for a in allocations)
        total_gpu = sum(a.gpu_memory_gb for a in allocations)
        
        # Group by agent
        by_agent = {}
        for allocation in allocations:
            agent = allocation.agent_name
            if agent not in by_agent:
                by_agent[agent] = {
                    "count": 0,
                    "memory_gb": 0.0,
                    "cpu_cores": 0,
                    "gpu_memory_gb": 0.0
                }
            
            by_agent[agent]["count"] += 1
            by_agent[agent]["memory_gb"] += allocation.memory_gb
            by_agent[agent]["cpu_cores"] += allocation.cpu_cores
            by_agent[agent]["gpu_memory_gb"] += allocation.gpu_memory_gb
        
        return {
            "total_allocations": len(allocations),
            "total_memory_gb": total_memory,
            "total_cpu_cores": total_cpu,
            "total_gpu_memory_gb": total_gpu,
            "allocations_by_agent": by_agent
        }

    def get_performance_recommendations(self) -> List[str]:
        """Get performance optimization recommendations"""
        recommendations = []
        
        metrics = self.get_current_metrics()
        if not metrics:
            return recommendations
        
        # Memory recommendations
        if metrics.memory_percent > 80:
            recommendations.append("Consider reducing batch sizes or enabling memory-efficient processing")
        
        # CPU recommendations
        if metrics.cpu_percent < 30 and len(self.allocations) > 0:
            recommendations.append("CPU utilization is low - consider increasing parallelism")
        elif metrics.cpu_percent > 90:
            recommendations.append("High CPU usage detected - consider reducing concurrent jobs")
        
        # GPU recommendations
        if self.gpu_available:
            if metrics.gpu_utilization_percent < 20 and any(a.gpu_memory_gb > 0 for a in self.allocations.values()):
                recommendations.append("GPU is underutilized - consider optimizing GPU workloads")
            elif metrics.gpu_utilization_percent > 95:
                recommendations.append("GPU is at capacity - consider reducing GPU-intensive tasks")
        
        # Storage recommendations
        if metrics.disk_percent > 85:
            recommendations.append("Disk space is running low - consider cleaning up temporary files")
        
        return recommendations

    def export_metrics(self, file_path: Path, hours: int = 24):
        """Export resource metrics to file"""
        cutoff_time = time.time() - (hours * 3600)
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]
        
        export_data = {
            "export_timestamp": time.time(),
            "hours_covered": hours,
            "metrics_count": len(recent_metrics),
            "metrics": [asdict(m) for m in recent_metrics],
            "current_allocations": [asdict(a) for a in self.allocations.values()],
            "performance_stats": self.performance_stats,
            "resource_limits": asdict(self.limits)
        }
        
        import json
        with open(file_path, 'w') as f:
            json.dump(export_data, f, indent=2)
        
        self.logger.info(f"Resource metrics exported to {file_path}")


class ResourceError(Exception):
    """Exception raised when resource allocation fails"""
    pass


# Global resource manager instance
_resource_manager: Optional[ResourceManager] = None


def get_resource_manager() -> ResourceManager:
    """Get global resource manager instance"""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = ResourceManager()
    return _resource_manager


async def initialize_resource_manager() -> ResourceManager:
    """Initialize and start the global resource manager"""
    manager = get_resource_manager()
    await manager.start_monitoring()
    return manager
