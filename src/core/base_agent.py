"""
Base Agent Abstract Class

Defines the standard interface and common functionality for all processing agents
in the multi-agent study guide generation system.
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
from contextlib import asynccontextmanager

from ..models.job_manifest import JobManifest, ProcessingStage, AgentState, ErrorRecord
from ..utils.logging import get_correlation_logger
from ..utils.metrics import MetricsCollector
from ..utils.resource_monitor import ResourceMonitor


class AgentError(Exception):
    """Base exception for agent-related errors"""
    def __init__(self, message: str, error_type: str = "AgentError", recoverable: bool = True):
        super().__init__(message)
        self.error_type = error_type
        self.recoverable = recoverable


class AgentTimeoutError(AgentError):
    """Raised when agent processing exceeds timeout"""
    def __init__(self, message: str, timeout_seconds: int):
        super().__init__(message, "AgentTimeoutError", recoverable=True)
        self.timeout_seconds = timeout_seconds


class AgentResourceError(AgentError):
    """Raised when agent encounters resource constraints"""
    def __init__(self, message: str, resource_type: str):
        super().__init__(message, "AgentResourceError", recoverable=True)
        self.resource_type = resource_type


class BaseAgent(ABC):
    """
    Abstract base class for all processing agents.
    
    Provides common functionality for:
    - State management and progress tracking
    - Error handling and retry logic
    - Resource monitoring and limits
    - Logging with correlation IDs
    - Metrics collection
    """
    
    def __init__(
        self,
        agent_name: str,
        config: Dict[str, Any],
        max_retries: int = 3,
        timeout_seconds: int = 3600,
        resource_limits: Optional[Dict[str, Union[int, float]]] = None
    ):
        self.agent_name = agent_name
        self.config = config
        self.max_retries = max_retries
        self.timeout_seconds = timeout_seconds
        self.resource_limits = resource_limits or {}
        
        # Initialize components
        self.logger = logging.getLogger(f"agent.{agent_name}")
        self.metrics = MetricsCollector(agent_name)
        self.resource_monitor = ResourceMonitor(self.resource_limits)
        
        # State tracking
        self._current_job: Optional[JobManifest] = None
        self._processing_stage: Optional[ProcessingStage] = None
        self._start_time: Optional[datetime] = None
        self._correlation_logger: Optional[logging.Logger] = None

    @property
    def current_job(self) -> Optional[JobManifest]:
        """Get currently processing job"""
        return self._current_job

    @property
    def processing_stage(self) -> Optional[ProcessingStage]:
        """Get current processing stage"""
        return self._processing_stage

    @abstractmethod
    async def process(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """
        Main processing method - must be implemented by subclasses.
        
        Args:
            job_manifest: Complete job manifest with all context
            
        Returns:
            Dict containing processing results and output artifacts
            
        Raises:
            AgentError: For recoverable processing errors
            Exception: For unrecoverable errors
        """
        pass

    @abstractmethod
    def validate_inputs(self, job_manifest: JobManifest) -> bool:
        """
        Validate that inputs are suitable for processing.
        
        Args:
            job_manifest: Job manifest to validate
            
        Returns:
            True if inputs are valid, False otherwise
        """
        pass

    @abstractmethod
    def estimate_processing_time(self, job_manifest: JobManifest) -> float:
        """
        Estimate processing time in seconds.
        
        Args:
            job_manifest: Job manifest to analyze
            
        Returns:
            Estimated processing time in seconds
        """
        pass

    async def execute(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """
        Execute agent processing with full error handling and monitoring.
        
        Args:
            job_manifest: Job manifest to process
            
        Returns:
            Processing results dictionary
            
        Raises:
            AgentError: For recoverable errors after max retries
            Exception: For unrecoverable errors
        """
        self._current_job = job_manifest
        self._correlation_logger = get_correlation_logger(
            self.logger, job_manifest.correlation_id
        )
        
        # Initialize processing stage
        stage = ProcessingStage(
            agent_name=self.agent_name,
            state=AgentState.RUNNING,
            start_time=datetime.utcnow(),
            max_retries=self.max_retries
        )
        
        job_manifest.processing_stages[self.agent_name] = stage
        self._processing_stage = stage
        
        retry_count = 0
        last_error = None
        
        while retry_count <= self.max_retries:
            try:
                self._correlation_logger.info(
                    f"Starting {self.agent_name} processing (attempt {retry_count + 1})"
                )
                
                # Validate inputs
                if not self.validate_inputs(job_manifest):
                    raise AgentError(f"Input validation failed for {self.agent_name}")
                
                # Start resource monitoring
                async with self._monitor_resources():
                    # Execute with timeout
                    result = await asyncio.wait_for(
                        self._execute_with_progress(job_manifest),
                        timeout=self.timeout_seconds
                    )
                
                # Mark as completed
                stage.state = AgentState.COMPLETED
                stage.end_time = datetime.utcnow()
                stage.progress_percentage = 100.0
                
                self._correlation_logger.info(
                    f"Completed {self.agent_name} processing successfully"
                )
                
                return result
                
            except asyncio.TimeoutError:
                error = AgentTimeoutError(
                    f"{self.agent_name} processing timed out after {self.timeout_seconds}s",
                    self.timeout_seconds
                )
                last_error = error
                
            except AgentError as e:
                last_error = e
                
            except Exception as e:
                # Wrap unexpected errors
                last_error = AgentError(
                    f"Unexpected error in {self.agent_name}: {str(e)}",
                    error_type=type(e).__name__,
                    recoverable=False
                )
            
            # Handle error and retry logic
            retry_count += 1
            stage.retry_count = retry_count
            stage.error_message = str(last_error)
            
            if retry_count <= self.max_retries and getattr(last_error, 'recoverable', True):
                stage.state = AgentState.RETRYING
                
                # Exponential backoff
                wait_time = min(2 ** retry_count, 60)
                self._correlation_logger.warning(
                    f"{self.agent_name} failed (attempt {retry_count}), retrying in {wait_time}s: {last_error}"
                )
                
                await asyncio.sleep(wait_time)
            else:
                break
        
        # All retries exhausted or unrecoverable error
        stage.state = AgentState.FAILED
        stage.end_time = datetime.utcnow()
        
        # Add error to job manifest
        job_manifest.add_error(
            agent_name=self.agent_name,
            error_type=getattr(last_error, 'error_type', 'UnknownError'),
            error_message=str(last_error)
        )
        
        self._correlation_logger.error(
            f"{self.agent_name} processing failed after {retry_count} attempts: {last_error}"
        )
        
        raise last_error

    async def _execute_with_progress(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """Execute processing with progress updates"""
        self._start_time = datetime.utcnow()
        
        # Create progress update task
        progress_task = asyncio.create_task(self._update_progress_periodically())
        
        try:
            # Execute main processing
            result = await self.process(job_manifest)
            return result
        finally:
            # Cancel progress updates
            progress_task.cancel()
            try:
                await progress_task
            except asyncio.CancelledError:
                pass

    async def _update_progress_periodically(self):
        """Update progress every 5 seconds"""
        while True:
            try:
                await asyncio.sleep(5)
                if self._processing_stage and self._start_time:
                    elapsed = (datetime.utcnow() - self._start_time).total_seconds()
                    estimated_total = self.estimate_processing_time(self._current_job)
                    
                    if estimated_total > 0:
                        progress = min(95.0, (elapsed / estimated_total) * 100)
                        self._processing_stage.progress_percentage = progress
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.warning(f"Progress update failed: {e}")

    @asynccontextmanager
    async def _monitor_resources(self):
        """Context manager for resource monitoring"""
        monitor_task = None
        try:
            # Start resource monitoring
            monitor_task = asyncio.create_task(
                self.resource_monitor.monitor_continuously(
                    callback=self._on_resource_update
                )
            )
            
            yield
            
        finally:
            if monitor_task:
                monitor_task.cancel()
                try:
                    await monitor_task
                except asyncio.CancelledError:
                    pass

    def _on_resource_update(self, metrics: Dict[str, float]):
        """Handle resource usage updates"""
        if self._processing_stage:
            self._processing_stage.resource_usage.update(metrics)
            
        # Check resource limits
        for resource, limit in self.resource_limits.items():
            if resource in metrics and metrics[resource] > limit:
                raise AgentResourceError(
                    f"Resource limit exceeded: {resource} = {metrics[resource]} > {limit}",
                    resource
                )

    def update_progress(self, percentage: float, message: Optional[str] = None):
        """Manually update processing progress"""
        if self._processing_stage:
            self._processing_stage.progress_percentage = min(100.0, max(0.0, percentage))
            
        if message and self._correlation_logger:
            self._correlation_logger.info(f"Progress {percentage:.1f}%: {message}")

    def add_output_artifact(self, file_path: str, artifact_type: str = "file"):
        """Add output artifact to processing stage"""
        if self._processing_stage:
            self._processing_stage.output_artifacts.append(file_path)
            
        if self._correlation_logger:
            self._correlation_logger.info(f"Generated artifact: {file_path} ({artifact_type})")

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get configuration value with fallback"""
        return self.config.get(key, default)

    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(name='{self.agent_name}')"
