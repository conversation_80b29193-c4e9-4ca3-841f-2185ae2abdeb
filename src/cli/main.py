#!/usr/bin/env python3
"""
Study Guide Generator CLI

Command-line interface for the multi-agent study guide generator system.
Provides comprehensive job management, monitoring, and configuration tools.
"""

import asyncio
import sys
import logging
from pathlib import Path
from typing import List, Optional
import click
import json
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.system_config import get_config, initialize_config
from config.config_manager import get_config_manager, initialize_config_manager
from core.orchestrator import JobOrchestrator
from core.resource_manager import get_resource_manager, initialize_resource_manager
from models.job_manifest import JobManifest, DocumentMetadata, JobState
from database.models import JobRepository


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@click.group()
@click.option('--config-dir', type=click.Path(exists=True, file_okay=False, dir_okay=True),
              help='Configuration directory path')
@click.option('--log-level', type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']),
              default='INFO', help='Logging level')
@click.pass_context
def cli(ctx, config_dir, log_level):
    """
    Study Guide Generator - Multi-Agent Document Processing System
    
    A comprehensive system for generating study guides from academic documents
    using local AI models and privacy-first processing.
    """
    # Set logging level
    logging.getLogger().setLevel(getattr(logging, log_level))
    
    # Initialize configuration
    if config_dir:
        config_dir = Path(config_dir)
        initialize_config(config_dir)
        initialize_config_manager(config_dir)
    
    # Store context for subcommands
    ctx.ensure_object(dict)
    ctx.obj['config_dir'] = config_dir
    ctx.obj['log_level'] = log_level


@cli.group()
def job():
    """Job management commands"""
    pass


@job.command()
@click.argument('files', nargs=-1, type=click.Path(exists=True), required=True)
@click.option('--title', help='Study guide title')
@click.option('--priority', type=click.IntRange(1, 10), default=5, help='Job priority (1=highest, 10=lowest)')
@click.option('--output-dir', type=click.Path(file_okay=False, dir_okay=True), help='Output directory')
@click.option('--wait', is_flag=True, help='Wait for job completion')
@click.option('--watch', is_flag=True, help='Watch job progress in real-time')
def create(files, title, priority, output_dir, wait, watch):
    """Create a new study guide generation job"""
    
    async def _create_job():
        try:
            # Initialize components
            config = get_config()
            orchestrator = JobOrchestrator()
            
            # Prepare documents
            documents = []
            for file_path in files:
                file_path = Path(file_path)
                doc_metadata = DocumentMetadata(
                    filename=file_path.name,
                    file_path=str(file_path),
                    file_size=file_path.stat().st_size,
                    mime_type="application/octet-stream",  # Will be detected by ingestion agent
                    checksum=""  # Will be calculated by ingestion agent
                )
                documents.append(doc_metadata)
            
            # Create job manifest
            job_manifest = JobManifest.create_new(
                documents=documents,
                title=title or f"Study Guide - {datetime.now().strftime('%Y%m%d_%H%M%S')}",
                priority=priority,
                output_directory=output_dir
            )
            
            click.echo(f"Creating job: {job_manifest.job_id}")
            click.echo(f"Title: {job_manifest.title}")
            click.echo(f"Documents: {len(documents)}")
            
            # Submit job
            await orchestrator.submit_job(job_manifest)
            
            click.echo(f"✓ Job {job_manifest.job_id} created successfully")
            
            if wait or watch:
                if watch:
                    await _watch_job_progress(job_manifest.job_id)
                else:
                    await _wait_for_job_completion(job_manifest.job_id)
            else:
                click.echo(f"Use 'studyguide job status {job_manifest.job_id}' to check progress")
                
        except Exception as e:
            click.echo(f"✗ Failed to create job: {e}", err=True)
            sys.exit(1)
    
    asyncio.run(_create_job())


@job.command()
@click.argument('job_id', required=False)
@click.option('--all', is_flag=True, help='Show all jobs')
@click.option('--active', is_flag=True, help='Show only active jobs')
@click.option('--format', type=click.Choice(['table', 'json']), default='table', help='Output format')
def status(job_id, all, active, format):
    """Show job status"""
    
    async def _show_status():
        try:
            job_repo = JobRepository()
            
            if job_id:
                # Show specific job
                job_manifest = await job_repo.get_job(job_id)
                if not job_manifest:
                    click.echo(f"Job {job_id} not found", err=True)
                    return
                
                if format == 'json':
                    click.echo(json.dumps(job_manifest.to_dict(), indent=2))
                else:
                    _display_job_details(job_manifest)
            else:
                # Show job list
                if active:
                    jobs = await job_repo.get_active_jobs()
                else:
                    jobs = await job_repo.get_recent_jobs(limit=20 if not all else None)
                
                if format == 'json':
                    jobs_data = [job.to_dict() for job in jobs]
                    click.echo(json.dumps(jobs_data, indent=2))
                else:
                    _display_jobs_table(jobs)
                    
        except Exception as e:
            click.echo(f"✗ Failed to get job status: {e}", err=True)
            sys.exit(1)
    
    asyncio.run(_show_status())


@job.command()
@click.argument('job_id')
@click.option('--force', is_flag=True, help='Force cancellation')
def cancel(job_id, force):
    """Cancel a running job"""
    
    async def _cancel_job():
        try:
            orchestrator = JobOrchestrator()
            success = await orchestrator.cancel_job(job_id, force=force)
            
            if success:
                click.echo(f"✓ Job {job_id} cancelled successfully")
            else:
                click.echo(f"✗ Failed to cancel job {job_id}", err=True)
                sys.exit(1)
                
        except Exception as e:
            click.echo(f"✗ Failed to cancel job: {e}", err=True)
            sys.exit(1)
    
    asyncio.run(_cancel_job())


@job.command()
@click.argument('job_id')
def watch(job_id):
    """Watch job progress in real-time"""
    
    async def _watch():
        try:
            await _watch_job_progress(job_id)
        except KeyboardInterrupt:
            click.echo("\nStopped watching job progress")
        except Exception as e:
            click.echo(f"✗ Failed to watch job: {e}", err=True)
            sys.exit(1)
    
    asyncio.run(_watch())


@job.command()
@click.argument('job_id')
def logs(job_id):
    """Show job logs"""
    
    async def _show_logs():
        try:
            job_repo = JobRepository()
            job_manifest = await job_repo.get_job(job_id)
            
            if not job_manifest:
                click.echo(f"Job {job_id} not found", err=True)
                return
            
            # Get error logs
            error_logs = await job_repo.get_job_errors(job_id)
            
            if error_logs:
                click.echo("=== Error Logs ===")
                for error in error_logs:
                    click.echo(f"[{error.timestamp}] {error.agent_name}: {error.error_message}")
                    if error.stack_trace:
                        click.echo(f"  Stack trace: {error.stack_trace}")
            else:
                click.echo("No error logs found")
                
        except Exception as e:
            click.echo(f"✗ Failed to get job logs: {e}", err=True)
            sys.exit(1)
    
    asyncio.run(_show_logs())


@cli.group()
def config():
    """Configuration management commands"""
    pass


@config.command()
@click.option('--format', type=click.Choice(['table', 'json']), default='table', help='Output format')
def show(format):
    """Show current configuration"""
    try:
        config_manager = get_config_manager()
        summary = config_manager.get_configuration_summary()
        
        if format == 'json':
            click.echo(json.dumps(summary, indent=2))
        else:
            _display_config_summary(summary)
            
    except Exception as e:
        click.echo(f"✗ Failed to show configuration: {e}", err=True)
        sys.exit(1)


@config.command()
def validate():
    """Validate current configuration"""
    try:
        config_manager = get_config_manager()
        is_valid, errors = config_manager.validate_configuration()
        
        if is_valid:
            click.echo("✓ Configuration is valid")
        else:
            click.echo("✗ Configuration validation failed:")
            for error in errors:
                click.echo(f"  - {error}")
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"✗ Failed to validate configuration: {e}", err=True)
        sys.exit(1)


@config.command()
@click.argument('file_path', type=click.Path())
@click.option('--format', type=click.Choice(['json', 'yaml']), default='json', help='Export format')
def export(file_path, format):
    """Export configuration to file"""
    try:
        config_manager = get_config_manager()
        config_manager.export_configuration(Path(file_path), format)
        click.echo(f"✓ Configuration exported to {file_path}")
        
    except Exception as e:
        click.echo(f"✗ Failed to export configuration: {e}", err=True)
        sys.exit(1)


@config.command()
@click.argument('file_path', type=click.Path(exists=True))
@click.option('--apply', is_flag=True, help='Apply imported configuration')
def import_config(file_path, apply):
    """Import configuration from file"""
    try:
        config_manager = get_config_manager()
        config = config_manager.import_configuration(Path(file_path), apply=apply)
        
        if config:
            if apply:
                click.echo(f"✓ Configuration imported and applied from {file_path}")
            else:
                click.echo(f"✓ Configuration imported from {file_path} (not applied)")
                click.echo("Use --apply flag to apply the configuration")
        else:
            click.echo(f"✗ Failed to import configuration from {file_path}", err=True)
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"✗ Failed to import configuration: {e}", err=True)
        sys.exit(1)


@config.command()
def reset():
    """Reset configuration to defaults"""
    try:
        config_manager = get_config_manager()
        
        if click.confirm("This will reset all configuration to defaults. Continue?"):
            config_manager.reset_to_defaults()
            click.echo("✓ Configuration reset to defaults")
        else:
            click.echo("Configuration reset cancelled")
            
    except Exception as e:
        click.echo(f"✗ Failed to reset configuration: {e}", err=True)
        sys.exit(1)


@cli.group()
def system():
    """System monitoring and management commands"""
    pass


@system.command()
@click.option('--format', type=click.Choice(['table', 'json']), default='table', help='Output format')
def status(format):
    """Show system status"""
    
    async def _show_system_status():
        try:
            # Initialize resource manager
            resource_manager = await initialize_resource_manager()
            
            # Get system information
            config = get_config()
            system_info = config.get_system_info()
            hardware_profile = config.get_hardware_profile()
            
            # Get resource status
            resource_status = resource_manager.get_resource_status()
            current_metrics = resource_manager.get_current_metrics()
            allocation_summary = resource_manager.get_allocation_summary()
            
            if format == 'json':
                status_data = {
                    "system_info": system_info,
                    "hardware_profile": hardware_profile.value,
                    "resource_status": {k.value: v.value for k, v in resource_status.items()},
                    "current_metrics": current_metrics.__dict__ if current_metrics else None,
                    "allocation_summary": allocation_summary
                }
                click.echo(json.dumps(status_data, indent=2))
            else:
                _display_system_status(system_info, hardware_profile, resource_status, current_metrics, allocation_summary)
                
        except Exception as e:
            click.echo(f"✗ Failed to get system status: {e}", err=True)
            sys.exit(1)
    
    asyncio.run(_show_system_status())


@system.command()
@click.option('--interval', type=int, default=5, help='Update interval in seconds')
@click.option('--duration', type=int, help='Monitoring duration in seconds')
def monitor(interval, duration):
    """Monitor system resources in real-time"""
    
    async def _monitor_system():
        try:
            resource_manager = await initialize_resource_manager()
            
            click.echo("Starting system monitoring... (Press Ctrl+C to stop)")
            click.echo()
            
            start_time = asyncio.get_event_loop().time()
            
            while True:
                current_time = asyncio.get_event_loop().time()
                
                # Check duration limit
                if duration and (current_time - start_time) >= duration:
                    break
                
                # Get current metrics
                metrics = resource_manager.get_current_metrics()
                if metrics:
                    _display_resource_metrics(metrics)
                
                await asyncio.sleep(interval)
                
        except KeyboardInterrupt:
            click.echo("\nStopped monitoring")
        except Exception as e:
            click.echo(f"✗ Monitoring failed: {e}", err=True)
            sys.exit(1)
    
    asyncio.run(_monitor_system())


@system.command()
def recommendations():
    """Get performance recommendations"""
    try:
        config_manager = get_config_manager()
        recommendations = config_manager.get_performance_recommendations()
        
        if recommendations:
            click.echo("Performance Recommendations:")
            for i, rec in enumerate(recommendations, 1):
                click.echo(f"  {i}. {rec}")
        else:
            click.echo("No performance recommendations at this time")
            
    except Exception as e:
        click.echo(f"✗ Failed to get recommendations: {e}", err=True)
        sys.exit(1)


# Helper functions for display formatting

def _display_job_details(job_manifest: JobManifest):
    """Display detailed job information"""
    click.echo(f"Job ID: {job_manifest.job_id}")
    click.echo(f"Title: {job_manifest.title}")
    click.echo(f"State: {job_manifest.state.value}")
    click.echo(f"Priority: {job_manifest.priority}")
    click.echo(f"Created: {job_manifest.created_at}")
    click.echo(f"Updated: {job_manifest.updated_at}")
    click.echo(f"Progress: {job_manifest.progress_percentage:.1f}%")
    
    if job_manifest.documents:
        click.echo(f"\nDocuments ({len(job_manifest.documents)}):")
        for doc in job_manifest.documents:
            click.echo(f"  - {doc.filename} ({doc.file_size} bytes)")
    
    if job_manifest.processing_stages:
        click.echo(f"\nProcessing Stages:")
        for stage_name, stage in job_manifest.processing_stages.items():
            status_icon = "✓" if stage.state.value == "completed" else "⏳" if stage.state.value == "running" else "○"
            click.echo(f"  {status_icon} {stage_name}: {stage.state.value}")
    
    if job_manifest.final_output_path:
        click.echo(f"\nOutput: {job_manifest.final_output_path}")


def _display_jobs_table(jobs: List[JobManifest]):
    """Display jobs in table format"""
    if not jobs:
        click.echo("No jobs found")
        return
    
    # Table header
    click.echo(f"{'Job ID':<12} {'Title':<30} {'State':<12} {'Progress':<10} {'Created':<20}")
    click.echo("-" * 84)
    
    # Table rows
    for job in jobs:
        job_id = job.job_id[:10] + ".." if len(job.job_id) > 12 else job.job_id
        title = job.title[:28] + ".." if len(job.title) > 30 else job.title
        created = job.created_at.strftime("%Y-%m-%d %H:%M:%S")
        
        click.echo(f"{job_id:<12} {title:<30} {job.state.value:<12} {job.progress_percentage:>7.1f}% {created:<20}")


def _display_config_summary(summary: dict):
    """Display configuration summary"""
    click.echo(f"Hardware Profile: {summary['hardware_profile']}")
    click.echo(f"System: {summary['system_info']['platform']} {summary['system_info']['architecture']}")
    click.echo(f"Memory: {summary['system_info']['memory_gb']:.1f} GB")
    click.echo(f"CPU Cores: {summary['system_info']['cpu_count_logical']}")
    
    gpu_info = summary['system_info'].get('gpu_info')
    if gpu_info:
        click.echo(f"GPU: {gpu_info['name']} ({gpu_info['memory_gb']:.1f} GB)")
    else:
        click.echo("GPU: Not available")
    
    click.echo(f"\nResource Limits:")
    limits = summary['resource_limits']
    click.echo(f"  Max Memory: {limits['max_memory_gb']} GB")
    click.echo(f"  Max CPU Cores: {limits['max_cpu_cores']}")
    click.echo(f"  Max Concurrent Jobs: {limits['max_concurrent_jobs']}")
    
    click.echo(f"\nModel Configuration:")
    models = summary['model_config']
    click.echo(f"  LLM Model: {models['llm_model']}")
    click.echo(f"  Embedding Model: {models['embedding_model']}")
    click.echo(f"  Image Generation: {'Enabled' if models['enable_image_generation'] else 'Disabled'}")
    
    validation = summary['validation_status']
    if validation['is_valid']:
        click.echo(f"\nValidation: ✓ Valid")
    else:
        click.echo(f"\nValidation: ✗ Invalid ({len(validation['errors'])} errors)")


def _display_system_status(system_info, hardware_profile, resource_status, current_metrics, allocation_summary):
    """Display system status information"""
    click.echo(f"System Status - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    click.echo("=" * 50)
    
    click.echo(f"Hardware Profile: {hardware_profile.value}")
    click.echo(f"Platform: {system_info['platform']} {system_info['architecture']}")
    
    if current_metrics:
        click.echo(f"\nResource Usage:")
        click.echo(f"  Memory: {current_metrics.memory_percent:.1f}% ({current_metrics.memory_used_gb:.1f}/{current_metrics.memory_used_gb + current_metrics.memory_available_gb:.1f} GB)")
        click.echo(f"  CPU: {current_metrics.cpu_percent:.1f}%")
        
        if current_metrics.gpu_memory_total_gb > 0:
            gpu_percent = (current_metrics.gpu_memory_used_gb / current_metrics.gpu_memory_total_gb) * 100
            click.echo(f"  GPU: {current_metrics.gpu_utilization_percent:.1f}% utilization, {gpu_percent:.1f}% memory")
        
        click.echo(f"  Disk: {current_metrics.disk_percent:.1f}% ({current_metrics.disk_available_gb:.1f} GB available)")
    
    click.echo(f"\nResource Status:")
    for resource_type, status in resource_status.items():
        status_icon = "✓" if status.value == "available" else "⚠" if status.value in ["limited", "critical"] else "✗"
        click.echo(f"  {status_icon} {resource_type.value.title()}: {status.value}")
    
    click.echo(f"\nActive Allocations: {allocation_summary['total_allocations']}")
    if allocation_summary['total_allocations'] > 0:
        click.echo(f"  Memory: {allocation_summary['total_memory_gb']:.1f} GB")
        click.echo(f"  CPU Cores: {allocation_summary['total_cpu_cores']}")
        click.echo(f"  GPU Memory: {allocation_summary['total_gpu_memory_gb']:.1f} GB")


def _display_resource_metrics(metrics):
    """Display resource metrics for monitoring"""
    timestamp = datetime.fromtimestamp(metrics.timestamp).strftime("%H:%M:%S")
    
    # Clear screen and show metrics
    click.clear()
    click.echo(f"System Resources - {timestamp}")
    click.echo("=" * 40)
    
    # Memory bar
    memory_bar = _create_progress_bar(metrics.memory_percent, 40)
    click.echo(f"Memory: {memory_bar} {metrics.memory_percent:5.1f}%")
    
    # CPU bar
    cpu_bar = _create_progress_bar(metrics.cpu_percent, 40)
    click.echo(f"CPU:    {cpu_bar} {metrics.cpu_percent:5.1f}%")
    
    # GPU bar (if available)
    if metrics.gpu_memory_total_gb > 0:
        gpu_memory_percent = (metrics.gpu_memory_used_gb / metrics.gpu_memory_total_gb) * 100
        gpu_bar = _create_progress_bar(gpu_memory_percent, 40)
        click.echo(f"GPU:    {gpu_bar} {gpu_memory_percent:5.1f}%")
    
    # Disk bar
    disk_bar = _create_progress_bar(metrics.disk_percent, 40)
    click.echo(f"Disk:   {disk_bar} {metrics.disk_percent:5.1f}%")
    
    click.echo(f"\nActive Jobs: {metrics.active_jobs}")


def _create_progress_bar(percentage, width):
    """Create a text progress bar"""
    filled = int((percentage / 100) * width)
    bar = "█" * filled + "░" * (width - filled)
    return f"[{bar}]"


async def _watch_job_progress(job_id: str):
    """Watch job progress in real-time"""
    job_repo = JobRepository()
    
    click.echo(f"Watching job {job_id}... (Press Ctrl+C to stop)")
    
    last_progress = -1
    
    while True:
        job_manifest = await job_repo.get_job(job_id)
        
        if not job_manifest:
            click.echo(f"Job {job_id} not found")
            break
        
        # Show progress if changed
        if job_manifest.progress_percentage != last_progress:
            progress_bar = _create_progress_bar(job_manifest.progress_percentage, 30)
            click.echo(f"\r{progress_bar} {job_manifest.progress_percentage:5.1f}% - {job_manifest.state.value}", nl=False)
            last_progress = job_manifest.progress_percentage
        
        # Check if job is complete
        if job_manifest.state in [JobState.COMPLETED, JobState.FAILED, JobState.CANCELLED]:
            click.echo(f"\nJob {job_id} finished with state: {job_manifest.state.value}")
            if job_manifest.final_output_path:
                click.echo(f"Output: {job_manifest.final_output_path}")
            break
        
        await asyncio.sleep(2)


async def _wait_for_job_completion(job_id: str):
    """Wait for job completion without real-time updates"""
    job_repo = JobRepository()
    
    while True:
        job_manifest = await job_repo.get_job(job_id)
        
        if not job_manifest:
            click.echo(f"Job {job_id} not found")
            break
        
        if job_manifest.state in [JobState.COMPLETED, JobState.FAILED, JobState.CANCELLED]:
            click.echo(f"Job {job_id} finished with state: {job_manifest.state.value}")
            if job_manifest.final_output_path:
                click.echo(f"Output: {job_manifest.final_output_path}")
            break
        
        await asyncio.sleep(5)


if __name__ == '__main__':
    cli()
