"""
Outline Generation Agent

Creates hierarchical study guide outlines using RAG-based analysis
of document content and semantic clustering of topics.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import re

from ..core.base_agent import BaseAgent, AgentError
from ..models.job_manifest import JobManifest


class OutlineGenerationAgent(BaseAgent):
    """
    Agent responsible for:
    - RAG-based table of contents generation using llama3:8b-instruct
    - Hierarchical structure detection with depth limits
    - Topic clustering and organization with semantic similarity
    - Cross-reference generation between related sections
    - Adaptive outline complexity based on document length
    """
    
    def __init__(self, agent_name: str, config: Dict[str, Any]):
        super().__init__(agent_name, config)
        
        # LLM configuration
        self.llm_model = config.get("llm_model", "llama3:8b-instruct")
        self.ollama_base_url = config.get("ollama_base_url", "http://localhost:11434")
        
        # Outline configuration
        self.max_outline_depth = config.get("max_outline_depth", 4)
        self.min_sections_per_level = config.get("min_sections_per_level", 2)
        self.max_sections_per_level = config.get("max_sections_per_level", 8)
        self.topic_similarity_threshold = config.get("topic_similarity_threshold", 0.6)
        
        # RAG configuration
        self.rag_context_chunks = config.get("rag_context_chunks", 20)
        self.outline_generation_temperature = config.get("outline_generation_temperature", 0.3)
        
        # Output configuration
        self.outline_output_dir = Path(config.get("outline_output_dir", "data/outlines"))
        self.outline_output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.ollama_client = None
        self.vector_search = None
        self._init_ollama_client()

    def _init_ollama_client(self):
        """Initialize Ollama client for LLM inference"""
        try:
            import requests
            
            # Test connection to Ollama
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                self.ollama_client = requests.Session()
                self.logger.info(f"Ollama client initialized at {self.ollama_base_url}")
            else:
                raise AgentError(f"Ollama server not responding: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Ollama client: {e}")
            raise AgentError(f"Failed to initialize Ollama client: {e}")

    async def process(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """
        Generate hierarchical outline from vectorized content.
        
        Args:
            job_manifest: Job containing vectorization results
            
        Returns:
            Dict with outline generation results
        """
        self.update_progress(5.0, "Starting outline generation")
        
        # Load vectorization results and setup search
        collection_name = await self._setup_vector_search(job_manifest)
        
        if not collection_name:
            raise AgentError("No vector collection found for outline generation")
        
        self.update_progress(15.0, f"Using vector collection: {collection_name}")
        
        # Analyze document content to identify main topics
        main_topics = await self._identify_main_topics(collection_name)
        
        self.update_progress(30.0, f"Identified {len(main_topics)} main topics")
        
        # Generate hierarchical outline structure
        outline_structure = await self._generate_outline_structure(main_topics, collection_name)
        
        self.update_progress(60.0, "Generated outline structure")
        
        # Enhance outline with cross-references and details
        enhanced_outline = await self._enhance_outline(outline_structure, collection_name)
        
        self.update_progress(80.0, "Enhanced outline with cross-references")
        
        # Format and validate final outline
        final_outline = await self._format_final_outline(enhanced_outline, job_manifest)
        
        self.update_progress(95.0, "Finalizing outline")
        
        # Save outline
        outline_path = self.outline_output_dir / f"{job_manifest.job_id}_outline.json"
        with open(outline_path, 'w', encoding='utf-8') as f:
            json.dump(final_outline, f, indent=2, ensure_ascii=False)
        
        self.add_output_artifact(str(outline_path), "study_guide_outline")
        
        # Also save as markdown for readability
        markdown_path = self.outline_output_dir / f"{job_manifest.job_id}_outline.md"
        await self._save_outline_as_markdown(final_outline, markdown_path)
        self.add_output_artifact(str(markdown_path), "outline_markdown")
        
        self.update_progress(100.0, "Outline generation completed")
        
        return {
            "outline_summary": {
                "total_sections": self._count_sections(final_outline),
                "max_depth": self._calculate_max_depth(final_outline),
                "main_topics": len(main_topics),
                "cross_references": self._count_cross_references(final_outline)
            },
            "output_artifacts": [str(outline_path), str(markdown_path)],
            "outline_structure": final_outline
        }

    async def _setup_vector_search(self, job_manifest: JobManifest) -> Optional[str]:
        """Setup vector search from vectorization results"""
        vectorization_stage = job_manifest.processing_stages.get("vectorization_indexing")
        if not vectorization_stage or not vectorization_stage.output_artifacts:
            return None
        
        # Load vectorization report to get collection name
        for artifact_path in vectorization_stage.output_artifacts:
            if "vectorization_report.json" in artifact_path:
                try:
                    with open(artifact_path, 'r', encoding='utf-8') as f:
                        vectorization_report = json.load(f)
                    
                    collection_name = vectorization_report.get("collection_name")
                    if collection_name:
                        # Initialize vector search capability
                        from ..agents.vectorization_indexing import VectorizationIndexingAgent
                        self.vector_search = VectorizationIndexingAgent("vector_search", self.config)
                        return collection_name
                        
                except Exception as e:
                    self.logger.error(f"Failed to load vectorization report: {e}")
        
        return None

    async def _identify_main_topics(self, collection_name: str) -> List[Dict[str, Any]]:
        """Identify main topics using RAG and clustering"""
        # Search for diverse content samples
        topic_queries = [
            "main concepts and ideas",
            "key principles and theories",
            "important definitions and terms",
            "core topics and subjects",
            "fundamental concepts",
            "primary themes and ideas"
        ]
        
        all_chunks = []
        for query in topic_queries:
            chunks = await self.vector_search.search_similar_chunks(
                collection_name, query, n_results=10
            )
            all_chunks.extend(chunks)
        
        # Remove duplicates and get unique chunks
        unique_chunks = {}
        for chunk in all_chunks:
            chunk_id = chunk["chunk_id"]
            if chunk_id not in unique_chunks or chunk["similarity"] > unique_chunks[chunk_id]["similarity"]:
                unique_chunks[chunk_id] = chunk
        
        # Use LLM to identify main topics from diverse chunks
        context_text = "\n\n".join([
            f"Chunk {i+1}: {chunk['document'][:500]}..."
            for i, chunk in enumerate(list(unique_chunks.values())[:self.rag_context_chunks])
        ])
        
        topic_identification_prompt = f"""
Based on the following content chunks from academic documents, identify the main topics and themes that should be covered in a comprehensive study guide.

Content chunks:
{context_text}

Please identify 5-10 main topics that capture the essential themes and concepts. For each topic, provide:
1. Topic title (concise and descriptive)
2. Brief description (1-2 sentences)
3. Estimated importance (high/medium/low)
4. Related subtopics (2-4 items)

Format your response as a JSON array of topic objects.
"""
        
        response = await self._call_llm(topic_identification_prompt)
        
        try:
            # Parse LLM response
            topics = json.loads(response)
            if isinstance(topics, list):
                return topics[:10]  # Limit to 10 main topics
        except json.JSONDecodeError:
            self.logger.warning("Failed to parse LLM response as JSON, using fallback topic extraction")
        
        # Fallback: extract topics from chunk metadata
        return await self._extract_topics_fallback(list(unique_chunks.values()))

    async def _generate_outline_structure(
        self, 
        main_topics: List[Dict[str, Any]], 
        collection_name: str
    ) -> Dict[str, Any]:
        """Generate hierarchical outline structure"""
        
        outline_prompt = f"""
Create a comprehensive study guide outline based on the following main topics. The outline should be hierarchical with clear sections and subsections.

Main topics identified:
{json.dumps(main_topics, indent=2)}

Requirements:
1. Create a logical flow from basic concepts to advanced topics
2. Use hierarchical structure (max {self.max_outline_depth} levels deep)
3. Each section should have {self.min_sections_per_level}-{self.max_sections_per_level} subsections
4. Include learning objectives for each major section
5. Suggest cross-references between related sections

Format as JSON with this structure:
{{
  "title": "Study Guide Title",
  "overview": "Brief overview of the study guide",
  "sections": [
    {{
      "id": "section_1",
      "title": "Section Title",
      "learning_objectives": ["objective 1", "objective 2"],
      "subsections": [
        {{
          "id": "subsection_1_1",
          "title": "Subsection Title",
          "description": "Brief description",
          "cross_references": ["section_2", "subsection_3_1"]
        }}
      ]
    }}
  ]
}}
"""
        
        response = await self._call_llm(outline_prompt)
        
        try:
            outline = json.loads(response)
            return outline
        except json.JSONDecodeError:
            self.logger.warning("Failed to parse outline JSON, creating fallback structure")
            return await self._create_fallback_outline(main_topics)

    async def _enhance_outline(
        self, 
        outline_structure: Dict[str, Any], 
        collection_name: str
    ) -> Dict[str, Any]:
        """Enhance outline with detailed content and cross-references"""
        enhanced_outline = outline_structure.copy()
        
        # Enhance each section with relevant content
        for section in enhanced_outline.get("sections", []):
            section_title = section.get("title", "")
            
            # Search for relevant content
            relevant_chunks = await self.vector_search.search_similar_chunks(
                collection_name, section_title, n_results=5
            )
            
            # Add content preview and key points
            if relevant_chunks:
                section["content_preview"] = relevant_chunks[0]["document"][:200] + "..."
                section["key_points"] = await self._extract_key_points(
                    section_title, relevant_chunks
                )
            
            # Enhance subsections
            for subsection in section.get("subsections", []):
                subsection_title = subsection.get("title", "")
                
                # Search for subsection-specific content
                subsection_chunks = await self.vector_search.search_similar_chunks(
                    collection_name, f"{section_title} {subsection_title}", n_results=3
                )
                
                if subsection_chunks:
                    subsection["content_preview"] = subsection_chunks[0]["document"][:150] + "..."
        
        return enhanced_outline

    async def _extract_key_points(
        self, 
        section_title: str, 
        relevant_chunks: List[Dict[str, Any]]
    ) -> List[str]:
        """Extract key points for a section using LLM"""
        context = "\n\n".join([chunk["document"] for chunk in relevant_chunks[:3]])
        
        key_points_prompt = f"""
Based on the following content related to "{section_title}", extract 3-5 key points that should be highlighted in a study guide.

Content:
{context}

Provide key points as a JSON array of strings. Each point should be concise and informative.
"""
        
        response = await self._call_llm(key_points_prompt)
        
        try:
            key_points = json.loads(response)
            if isinstance(key_points, list):
                return key_points[:5]
        except json.JSONDecodeError:
            pass
        
        # Fallback: extract sentences that might be key points
        return self._extract_key_points_fallback(context)

    async def _format_final_outline(
        self, 
        enhanced_outline: Dict[str, Any], 
        job_manifest: JobManifest
    ) -> Dict[str, Any]:
        """Format and validate the final outline"""
        final_outline = {
            "job_id": job_manifest.job_id,
            "generated_at": job_manifest.updated_at.isoformat(),
            "title": enhanced_outline.get("title", "Study Guide"),
            "overview": enhanced_outline.get("overview", "Comprehensive study guide"),
            "metadata": {
                "total_sections": self._count_sections(enhanced_outline),
                "max_depth": self._calculate_max_depth(enhanced_outline),
                "generation_model": self.llm_model
            },
            "sections": enhanced_outline.get("sections", [])
        }
        
        # Validate structure
        self._validate_outline_structure(final_outline)
        
        return final_outline

    async def _save_outline_as_markdown(self, outline: Dict[str, Any], markdown_path: Path):
        """Save outline as markdown for readability"""
        def _generate_markdown():
            md_content = f"# {outline.get('title', 'Study Guide')}\n\n"
            md_content += f"{outline.get('overview', '')}\n\n"
            
            for section in outline.get("sections", []):
                md_content += f"## {section.get('title', '')}\n\n"
                
                if section.get("learning_objectives"):
                    md_content += "**Learning Objectives:**\n"
                    for obj in section["learning_objectives"]:
                        md_content += f"- {obj}\n"
                    md_content += "\n"
                
                if section.get("key_points"):
                    md_content += "**Key Points:**\n"
                    for point in section["key_points"]:
                        md_content += f"- {point}\n"
                    md_content += "\n"
                
                for subsection in section.get("subsections", []):
                    md_content += f"### {subsection.get('title', '')}\n\n"
                    if subsection.get("description"):
                        md_content += f"{subsection['description']}\n\n"
            
            return md_content
        
        loop = asyncio.get_event_loop()
        markdown_content = await loop.run_in_executor(None, _generate_markdown)
        
        with open(markdown_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

    async def _call_llm(self, prompt: str) -> str:
        """Call Ollama LLM with the given prompt"""
        def _make_request():
            payload = {
                "model": self.llm_model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": self.outline_generation_temperature,
                    "top_p": 0.9,
                    "max_tokens": 4000
                }
            }
            
            response = self.ollama_client.post(
                f"{self.ollama_base_url}/api/generate",
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                return response.json().get("response", "")
            else:
                raise AgentError(f"LLM request failed: {response.status_code}")
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _make_request)

    async def _extract_topics_fallback(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Fallback topic extraction from chunk content"""
        # Simple keyword-based topic extraction
        topics = []
        
        # Extract common terms and phrases
        all_text = " ".join([chunk["document"] for chunk in chunks[:10]])
        
        # Simple topic extraction (this could be enhanced with NLP libraries)
        common_terms = ["introduction", "overview", "principles", "methods", "analysis", 
                       "theory", "practice", "applications", "conclusion", "summary"]
        
        for i, term in enumerate(common_terms[:8]):
            topics.append({
                "title": term.title(),
                "description": f"Content related to {term}",
                "importance": "medium",
                "subtopics": [f"{term} basics", f"{term} applications"]
            })
        
        return topics

    async def _create_fallback_outline(self, main_topics: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create fallback outline structure"""
        return {
            "title": "Study Guide",
            "overview": "Comprehensive study guide based on document analysis",
            "sections": [
                {
                    "id": f"section_{i+1}",
                    "title": topic.get("title", f"Topic {i+1}"),
                    "learning_objectives": [f"Understand {topic.get('title', 'topic')}"],
                    "subsections": [
                        {
                            "id": f"subsection_{i+1}_1",
                            "title": f"Introduction to {topic.get('title', 'topic')}",
                            "description": topic.get("description", ""),
                            "cross_references": []
                        }
                    ]
                }
                for i, topic in enumerate(main_topics[:6])
            ]
        }

    def _extract_key_points_fallback(self, content: str) -> List[str]:
        """Fallback key point extraction"""
        sentences = re.split(r'[.!?]+', content)
        key_sentences = [s.strip() for s in sentences if len(s.strip()) > 20 and len(s.strip()) < 150]
        return key_sentences[:5]

    def _count_sections(self, outline: Dict[str, Any]) -> int:
        """Count total sections in outline"""
        count = len(outline.get("sections", []))
        for section in outline.get("sections", []):
            count += len(section.get("subsections", []))
        return count

    def _calculate_max_depth(self, outline: Dict[str, Any]) -> int:
        """Calculate maximum depth of outline"""
        max_depth = 1  # At least one level (sections)
        for section in outline.get("sections", []):
            if section.get("subsections"):
                max_depth = max(max_depth, 2)
        return max_depth

    def _count_cross_references(self, outline: Dict[str, Any]) -> int:
        """Count total cross-references in outline"""
        count = 0
        for section in outline.get("sections", []):
            for subsection in section.get("subsections", []):
                count += len(subsection.get("cross_references", []))
        return count

    def _validate_outline_structure(self, outline: Dict[str, Any]):
        """Validate outline structure"""
        if not outline.get("sections"):
            raise AgentError("Outline must have at least one section")
        
        for section in outline["sections"]:
            if not section.get("title"):
                raise AgentError("All sections must have titles")

    def validate_inputs(self, job_manifest: JobManifest) -> bool:
        """Validate that vectorization results are available"""
        vectorization_stage = job_manifest.processing_stages.get("vectorization_indexing")
        if not vectorization_stage:
            self.logger.error("Vectorization stage not found")
            return False
        
        if vectorization_stage.state.value != "completed":
            self.logger.error("Vectorization stage not completed")
            return False
        
        return True

    def estimate_processing_time(self, job_manifest: JobManifest) -> float:
        """Estimate processing time based on content complexity"""
        # Base time for outline generation
        base_time = 30.0  # seconds
        
        # Additional time based on document count and complexity
        doc_count = len(job_manifest.documents)
        complexity_factor = min(doc_count * 10.0, 120.0)  # Max 2 minutes additional
        
        return base_time + complexity_factor
