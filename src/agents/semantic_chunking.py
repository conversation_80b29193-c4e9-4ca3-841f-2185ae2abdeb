"""
Semantic Chunking Agent

Handles intelligent text segmentation using semantic similarity,
creating coherent chunks optimized for retrieval and processing.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import numpy as np

from ..core.base_agent import BaseAgent, AgentError
from ..models.job_manifest import JobManifest


class SemanticChunkingAgent(BaseAgent):
    """
    Agent responsible for:
    - Intelligent text segmentation using sliding window approach
    - Semantic similarity analysis with embeddings
    - Recursive merging with configurable thresholds
    - Chunk size optimization based on content type
    - Boundary detection for sections, paragraphs, and sentences
    """
    
    def __init__(self, agent_name: str, config: Dict[str, Any]):
        super().__init__(agent_name, config)
        
        # Chunking parameters
        self.min_chunk_size = config.get("min_chunk_size", 512)
        self.max_chunk_size = config.get("max_chunk_size", 2048)
        self.chunk_overlap = config.get("chunk_overlap", 128)
        self.similarity_threshold = config.get("similarity_threshold", 0.75)
        
        # Embedding model configuration
        self.embedding_model_name = config.get("embedding_model", "bge-small-en-v1.5")
        self.batch_size = config.get("embedding_batch_size", 32)
        
        # Output configuration
        self.chunks_output_dir = Path(config.get("chunks_output_dir", "data/chunks"))
        self.chunks_output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize embedding model
        self.embedding_model = None
        self.tokenizer = None
        self._init_embedding_model()

    def _init_embedding_model(self):
        """Initialize sentence transformer model for embeddings"""
        try:
            from sentence_transformers import SentenceTransformer
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            self.logger.info(f"Initialized embedding model: {self.embedding_model_name}")
        except ImportError:
            self.logger.error("sentence-transformers not available")
            raise AgentError("sentence-transformers package required for semantic chunking")
        except Exception as e:
            self.logger.error(f"Failed to initialize embedding model: {e}")
            raise AgentError(f"Failed to initialize embedding model: {e}")

    async def process(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """
        Process extracted text through semantic chunking pipeline.
        
        Args:
            job_manifest: Job containing extraction results
            
        Returns:
            Dict with chunking results and output artifacts
        """
        self.update_progress(5.0, "Starting semantic chunking")
        
        # Load extraction results
        extraction_results = await self._load_extraction_results(job_manifest)
        
        if not extraction_results:
            raise AgentError("No extraction results found for chunking")
        
        chunking_results = []
        total_docs = len(extraction_results)
        
        for i, doc_result in enumerate(extraction_results):
            try:
                self.update_progress(
                    10.0 + (i / total_docs) * 80.0,
                    f"Chunking document: {doc_result.get('filename', 'unknown')}"
                )
                
                # Process document text into semantic chunks
                doc_chunks = await self._chunk_document(doc_result, job_manifest.job_id)
                chunking_results.append(doc_chunks)
                
            except Exception as e:
                self.logger.error(f"Failed to chunk document {doc_result.get('filename')}: {e}")
                # Create error result
                error_result = {
                    "document_id": doc_result.get("document_id"),
                    "filename": doc_result.get("filename"),
                    "status": "failed",
                    "error": str(e),
                    "chunks": []
                }
                chunking_results.append(error_result)
        
        # Consolidate and save results
        total_chunks = sum(len(result.get("chunks", [])) for result in chunking_results)
        successful_docs = sum(1 for result in chunking_results if result.get("status") == "success")
        
        self.update_progress(95.0, "Finalizing chunking results")
        
        # Save consolidated chunking report
        chunking_report = {
            "job_id": job_manifest.job_id,
            "total_documents": total_docs,
            "successful_chunking": successful_docs,
            "failed_chunking": total_docs - successful_docs,
            "total_chunks": total_chunks,
            "chunking_config": {
                "min_chunk_size": self.min_chunk_size,
                "max_chunk_size": self.max_chunk_size,
                "chunk_overlap": self.chunk_overlap,
                "similarity_threshold": self.similarity_threshold,
                "embedding_model": self.embedding_model_name
            },
            "chunking_results": chunking_results
        }
        
        report_path = self.chunks_output_dir / f"{job_manifest.job_id}_chunking_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(chunking_report, f, indent=2, ensure_ascii=False)
        
        self.add_output_artifact(str(report_path), "chunking_report")
        
        self.update_progress(100.0, "Semantic chunking completed")
        
        return {
            "chunking_summary": {
                "total_documents": total_docs,
                "successful_chunking": successful_docs,
                "total_chunks": total_chunks,
                "average_chunks_per_doc": total_chunks / max(successful_docs, 1)
            },
            "output_artifacts": [str(report_path)],
            "chunking_results": chunking_results
        }

    async def _load_extraction_results(self, job_manifest: JobManifest) -> List[Dict[str, Any]]:
        """Load extraction results from previous stage"""
        # Look for extraction report in processing stages
        extraction_stage = job_manifest.processing_stages.get("content_extraction")
        if not extraction_stage or not extraction_stage.output_artifacts:
            return []
        
        # Load extraction report
        for artifact_path in extraction_stage.output_artifacts:
            if "extraction_report.json" in artifact_path:
                try:
                    with open(artifact_path, 'r', encoding='utf-8') as f:
                        extraction_report = json.load(f)
                    return extraction_report.get("extraction_details", [])
                except Exception as e:
                    self.logger.error(f"Failed to load extraction report: {e}")
        
        return []

    async def _chunk_document(self, doc_result: Dict[str, Any], job_id: str) -> Dict[str, Any]:
        """
        Chunk a single document using semantic similarity.
        
        Args:
            doc_result: Document extraction result
            job_id: Job identifier
            
        Returns:
            Dict with chunking results for the document
        """
        extracted_text = doc_result.get("extracted_text", "")
        if not extracted_text.strip():
            return {
                "document_id": doc_result.get("document_id"),
                "filename": doc_result.get("filename"),
                "status": "success",
                "chunks": [],
                "total_chunks": 0
            }
        
        # Step 1: Initial text segmentation
        initial_segments = await self._segment_text(extracted_text)
        
        # Step 2: Generate embeddings for segments
        embeddings = await self._generate_embeddings(initial_segments)
        
        # Step 3: Semantic chunking using similarity
        semantic_chunks = await self._create_semantic_chunks(initial_segments, embeddings)
        
        # Step 4: Optimize chunk sizes
        optimized_chunks = await self._optimize_chunk_sizes(semantic_chunks)
        
        # Step 5: Add metadata and save chunks
        final_chunks = []
        for i, chunk_text in enumerate(optimized_chunks):
            chunk_metadata = {
                "chunk_id": f"{doc_result.get('document_id', 'unknown')}_{i:04d}",
                "document_id": doc_result.get("document_id"),
                "filename": doc_result.get("filename"),
                "chunk_index": i,
                "chunk_size": len(chunk_text),
                "token_count": self._estimate_token_count(chunk_text),
                "text": chunk_text
            }
            final_chunks.append(chunk_metadata)
        
        # Save chunks to individual files
        chunks_file = self.chunks_output_dir / f"{job_id}_{doc_result.get('document_id', 'unknown')}_chunks.json"
        with open(chunks_file, 'w', encoding='utf-8') as f:
            json.dump(final_chunks, f, indent=2, ensure_ascii=False)
        
        return {
            "document_id": doc_result.get("document_id"),
            "filename": doc_result.get("filename"),
            "status": "success",
            "chunks": final_chunks,
            "total_chunks": len(final_chunks),
            "chunks_file": str(chunks_file)
        }

    async def _segment_text(self, text: str) -> List[str]:
        """Initial text segmentation into sentences and paragraphs"""
        def _segment():
            # Simple sentence segmentation
            import re
            
            # Split by paragraphs first
            paragraphs = text.split('\n\n')
            segments = []
            
            for paragraph in paragraphs:
                paragraph = paragraph.strip()
                if not paragraph:
                    continue
                
                # Split long paragraphs into sentences
                sentences = re.split(r'[.!?]+\s+', paragraph)
                
                current_segment = ""
                for sentence in sentences:
                    sentence = sentence.strip()
                    if not sentence:
                        continue
                    
                    # If adding this sentence would exceed max size, start new segment
                    if len(current_segment) + len(sentence) > self.max_chunk_size:
                        if current_segment:
                            segments.append(current_segment.strip())
                        current_segment = sentence
                    else:
                        if current_segment:
                            current_segment += ". " + sentence
                        else:
                            current_segment = sentence
                
                if current_segment:
                    segments.append(current_segment.strip())
            
            return segments
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _segment)

    async def _generate_embeddings(self, segments: List[str]) -> np.ndarray:
        """Generate embeddings for text segments"""
        if not segments:
            return np.array([])
        
        def _embed():
            # Process in batches to manage memory
            all_embeddings = []
            
            for i in range(0, len(segments), self.batch_size):
                batch = segments[i:i + self.batch_size]
                batch_embeddings = self.embedding_model.encode(
                    batch,
                    convert_to_numpy=True,
                    show_progress_bar=False
                )
                all_embeddings.append(batch_embeddings)
            
            return np.vstack(all_embeddings) if all_embeddings else np.array([])
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _embed)

    async def _create_semantic_chunks(self, segments: List[str], embeddings: np.ndarray) -> List[str]:
        """Create semantic chunks using similarity-based merging"""
        if len(segments) == 0:
            return []
        
        if len(segments) == 1:
            return segments
        
        def _compute_similarity(emb1: np.ndarray, emb2: np.ndarray) -> float:
            """Compute cosine similarity between embeddings"""
            return np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
        
        def _merge_segments():
            chunks = []
            current_chunk = segments[0]
            current_embedding = embeddings[0]
            
            for i in range(1, len(segments)):
                segment = segments[i]
                segment_embedding = embeddings[i]
                
                # Calculate similarity with current chunk
                similarity = _compute_similarity(current_embedding, segment_embedding)
                
                # Check if we should merge based on similarity and size constraints
                merged_size = len(current_chunk) + len(segment) + 1  # +1 for space
                
                if (similarity >= self.similarity_threshold and 
                    merged_size <= self.max_chunk_size):
                    # Merge segments
                    current_chunk += " " + segment
                    # Update embedding (simple average - could be improved)
                    current_embedding = (current_embedding + segment_embedding) / 2
                else:
                    # Start new chunk
                    chunks.append(current_chunk)
                    current_chunk = segment
                    current_embedding = segment_embedding
            
            # Add the last chunk
            if current_chunk:
                chunks.append(current_chunk)
            
            return chunks
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _merge_segments)

    async def _optimize_chunk_sizes(self, chunks: List[str]) -> List[str]:
        """Optimize chunk sizes to meet min/max constraints"""
        def _optimize():
            optimized = []
            
            for chunk in chunks:
                if len(chunk) < self.min_chunk_size:
                    # Try to merge with previous chunk if possible
                    if (optimized and 
                        len(optimized[-1]) + len(chunk) + 1 <= self.max_chunk_size):
                        optimized[-1] += " " + chunk
                    else:
                        optimized.append(chunk)
                        
                elif len(chunk) > self.max_chunk_size:
                    # Split large chunks
                    words = chunk.split()
                    current_chunk = ""
                    
                    for word in words:
                        if len(current_chunk) + len(word) + 1 <= self.max_chunk_size:
                            if current_chunk:
                                current_chunk += " " + word
                            else:
                                current_chunk = word
                        else:
                            if current_chunk:
                                optimized.append(current_chunk)
                            current_chunk = word
                    
                    if current_chunk:
                        optimized.append(current_chunk)
                else:
                    optimized.append(chunk)
            
            return optimized
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _optimize)

    def _estimate_token_count(self, text: str) -> int:
        """Estimate token count (rough approximation)"""
        # Simple approximation: ~4 characters per token on average
        return len(text) // 4

    def validate_inputs(self, job_manifest: JobManifest) -> bool:
        """Validate that extraction results are available"""
        extraction_stage = job_manifest.processing_stages.get("content_extraction")
        if not extraction_stage:
            self.logger.error("Content extraction stage not found")
            return False
        
        if extraction_stage.state.value != "completed":
            self.logger.error("Content extraction stage not completed")
            return False
        
        return True

    def estimate_processing_time(self, job_manifest: JobManifest) -> float:
        """Estimate processing time based on text volume"""
        # Base time for setup
        base_time = 10.0  # seconds
        
        # Estimate based on document count and sizes
        total_size_mb = sum(doc.file_size for doc in job_manifest.documents) / (1024 * 1024)
        
        # Chunking is relatively fast but embedding generation takes time
        # Estimate ~5 seconds per MB of text for embedding generation
        processing_time = total_size_mb * 5.0
        
        return base_time + processing_time
