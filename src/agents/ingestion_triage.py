"""
Ingestion & Triage Agent

Handles file validation, duplicate detection, security scanning,
and initial document preprocessing for the study guide generation pipeline.
"""

import hashlib
import mimetypes
from pathlib import Path
from typing import Dict, Any, List, Optional
import asyncio
import logging

from ..core.base_agent import BaseAgent, AgentError
from ..models.job_manifest import JobManifest, DocumentMetadata


class IngestionTriageAgent(BaseAgent):
    """
    Agent responsible for:
    - File validation using magic number detection
    - Duplicate detection using SHA256 hashing
    - File size and format compatibility checks
    - Security scanning and quarantine
    - Document metadata extraction
    """
    
    def __init__(self, agent_name: str, config: Dict[str, Any]):
        super().__init__(agent_name, config)
        
        self.max_file_size_mb = config.get("max_file_size_mb", 100)
        self.supported_formats = config.get("supported_formats", [
            "application/pdf",
            "image/jpeg", "image/png", "image/tiff",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
            "text/plain"
        ])
        
        self.quarantine_dir = Path(config.get("quarantine_dir", "data/quarantine"))
        self.quarantine_dir.mkdir(parents=True, exist_ok=True)
        
        # Duplicate detection cache
        self.known_checksums: Dict[str, str] = {}

    async def process(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """
        Process uploaded documents through validation pipeline.
        
        Args:
            job_manifest: Job containing documents to validate
            
        Returns:
            Dict with validation results and processed document metadata
        """
        self.update_progress(5.0, "Starting document validation")
        
        validated_documents = []
        quarantined_documents = []
        duplicate_documents = []
        
        total_docs = len(job_manifest.documents)
        
        for i, doc_metadata in enumerate(job_manifest.documents):
            try:
                self.update_progress(
                    10.0 + (i / total_docs) * 80.0,
                    f"Validating document {i+1}/{total_docs}: {doc_metadata.original_filename}"
                )
                
                # Validate document
                validation_result = await self._validate_document(doc_metadata)
                
                if validation_result["status"] == "valid":
                    validated_documents.append(validation_result["metadata"])
                elif validation_result["status"] == "duplicate":
                    duplicate_documents.append(validation_result["metadata"])
                else:
                    quarantined_documents.append(validation_result["metadata"])
                    
            except Exception as e:
                self.logger.error(f"Failed to validate document {doc_metadata.original_filename}: {e}")
                # Quarantine problematic documents
                quarantined_metadata = doc_metadata.model_copy()
                quarantined_metadata.validation_status = "quarantined"
                quarantined_documents.append(quarantined_metadata)
        
        # Update job manifest with validated documents
        job_manifest.documents = validated_documents
        
        self.update_progress(95.0, "Finalizing validation results")
        
        # Generate summary report
        validation_summary = {
            "total_documents": total_docs,
            "validated_documents": len(validated_documents),
            "duplicate_documents": len(duplicate_documents),
            "quarantined_documents": len(quarantined_documents),
            "validation_details": {
                "duplicates": [doc.original_filename for doc in duplicate_documents],
                "quarantined": [doc.original_filename for doc in quarantined_documents]
            }
        }
        
        self.update_progress(100.0, "Document validation completed")
        
        return {
            "validation_summary": validation_summary,
            "output_artifacts": [],
            "processed_documents": len(validated_documents)
        }

    async def _validate_document(self, doc_metadata: DocumentMetadata) -> Dict[str, Any]:
        """
        Comprehensive document validation.
        
        Args:
            doc_metadata: Document metadata to validate
            
        Returns:
            Dict with validation status and updated metadata
        """
        file_path = Path(doc_metadata.file_path)
        
        # Check if file exists
        if not file_path.exists():
            return {
                "status": "quarantined",
                "reason": "File not found",
                "metadata": self._update_metadata_status(doc_metadata, "quarantined")
            }
        
        # Verify file size
        actual_size = file_path.stat().st_size
        if actual_size != doc_metadata.file_size:
            return {
                "status": "quarantined", 
                "reason": "File size mismatch",
                "metadata": self._update_metadata_status(doc_metadata, "quarantined")
            }
        
        # Check file size limits
        size_mb = actual_size / (1024 * 1024)
        if size_mb > self.max_file_size_mb:
            return {
                "status": "quarantined",
                "reason": f"File too large: {size_mb:.1f}MB > {self.max_file_size_mb}MB",
                "metadata": self._update_metadata_status(doc_metadata, "quarantined")
            }
        
        # Verify checksum
        calculated_checksum = await self._calculate_checksum(file_path)
        if calculated_checksum != doc_metadata.checksum_sha256:
            return {
                "status": "quarantined",
                "reason": "Checksum mismatch - file may be corrupted",
                "metadata": self._update_metadata_status(doc_metadata, "quarantined")
            }
        
        # Check for duplicates
        if calculated_checksum in self.known_checksums:
            return {
                "status": "duplicate",
                "reason": f"Duplicate of {self.known_checksums[calculated_checksum]}",
                "metadata": self._update_metadata_status(doc_metadata, "duplicate")
            }
        
        # Validate MIME type using magic numbers
        detected_mime = await self._detect_mime_type(file_path)
        if detected_mime != doc_metadata.mime_type:
            self.logger.warning(
                f"MIME type mismatch for {doc_metadata.original_filename}: "
                f"expected {doc_metadata.mime_type}, detected {detected_mime}"
            )
            # Update with detected MIME type
            doc_metadata.mime_type = detected_mime
        
        # Check if format is supported
        if detected_mime not in self.supported_formats:
            return {
                "status": "quarantined",
                "reason": f"Unsupported format: {detected_mime}",
                "metadata": self._update_metadata_status(doc_metadata, "quarantined")
            }
        
        # Basic security validation (file extension check)
        if self._has_suspicious_extension(file_path):
            return {
                "status": "quarantined",
                "reason": "Suspicious file extension detected",
                "metadata": self._update_metadata_status(doc_metadata, "quarantined")
            }
        
        # Extract additional metadata
        enhanced_metadata = await self._extract_document_metadata(file_path, doc_metadata)
        
        # Mark as valid and remember checksum
        self.known_checksums[calculated_checksum] = doc_metadata.original_filename
        enhanced_metadata.validation_status = "valid"
        
        return {
            "status": "valid",
            "metadata": enhanced_metadata
        }

    async def _calculate_checksum(self, file_path: Path) -> str:
        """Calculate SHA256 checksum of file"""
        sha256_hash = hashlib.sha256()
        
        def _hash_file():
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _hash_file)

    async def _detect_mime_type(self, file_path: Path) -> str:
        """Detect MIME type using file extension and basic checks"""
        def _detect():
            # Use mimetypes module for detection
            detected, _ = mimetypes.guess_type(str(file_path))

            # Basic file signature checks for common formats
            if not detected:
                with open(file_path, 'rb') as f:
                    header = f.read(8)
                    if header.startswith(b'%PDF'):
                        detected = "application/pdf"
                    elif header.startswith(b'\xff\xd8\xff'):
                        detected = "image/jpeg"
                    elif header.startswith(b'\x89PNG'):
                        detected = "image/png"
                    elif header.startswith(b'PK\x03\x04'):
                        # Could be DOCX or other ZIP-based format
                        if str(file_path).endswith('.docx'):
                            detected = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                        else:
                            detected = "application/zip"

            return detected or "application/octet-stream"

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _detect)

    async def _extract_document_metadata(
        self, 
        file_path: Path, 
        base_metadata: DocumentMetadata
    ) -> DocumentMetadata:
        """Extract additional document metadata based on file type"""
        enhanced_metadata = base_metadata.model_copy()
        
        try:
            if base_metadata.mime_type == "application/pdf":
                # Extract PDF metadata using PyMuPDF
                try:
                    import fitz  # PyMuPDF

                    def _extract_pdf_metadata():
                        doc = fitz.open(str(file_path))
                        page_count = doc.page_count
                        metadata = doc.metadata
                        doc.close()
                        return page_count, metadata

                    loop = asyncio.get_event_loop()
                    page_count, pdf_metadata = await loop.run_in_executor(None, _extract_pdf_metadata)

                    enhanced_metadata.page_count = page_count

                    # Extract language if available
                    if pdf_metadata.get("language"):
                        enhanced_metadata.language = pdf_metadata["language"][:2].lower()

                except ImportError:
                    self.logger.warning("PyMuPDF not available, using basic PDF handling")
                    enhanced_metadata.page_count = 1  # Default fallback
                    
            elif base_metadata.mime_type.startswith("image/"):
                # Extract image metadata
                from PIL import Image
                
                def _extract_image_metadata():
                    with Image.open(file_path) as img:
                        return {
                            "width": img.width,
                            "height": img.height,
                            "format": img.format,
                            "mode": img.mode
                        }
                
                loop = asyncio.get_event_loop()
                _ = await loop.run_in_executor(None, _extract_image_metadata)

                # Treat images as single page documents
                enhanced_metadata.page_count = 1
                
            elif "word" in base_metadata.mime_type:
                # Extract Word document metadata
                from docx import Document
                
                def _extract_docx_metadata():
                    doc = Document(file_path)
                    return len(doc.paragraphs)
                
                loop = asyncio.get_event_loop()
                paragraph_count = await loop.run_in_executor(None, _extract_docx_metadata)
                
                # Estimate page count (rough approximation)
                enhanced_metadata.page_count = max(1, paragraph_count // 20)
                
        except Exception as e:
            self.logger.warning(f"Failed to extract metadata for {file_path}: {e}")
            # Set default values
            if enhanced_metadata.page_count is None:
                enhanced_metadata.page_count = 1
        
        return enhanced_metadata

    def _update_metadata_status(self, metadata: DocumentMetadata, status: str) -> DocumentMetadata:
        """Update document metadata with validation status"""
        updated_metadata = metadata.model_copy()
        updated_metadata.validation_status = status
        return updated_metadata

    def validate_inputs(self, job_manifest: JobManifest) -> bool:
        """Validate that job has documents to process"""
        if not job_manifest.documents:
            self.logger.error("No documents provided for validation")
            return False
        
        # Check if all required fields are present
        for doc in job_manifest.documents:
            if not all([doc.file_path, doc.original_filename, doc.checksum_sha256]):
                self.logger.error(f"Missing required fields for document: {doc.original_filename}")
                return False
        
        return True

    def estimate_processing_time(self, job_manifest: JobManifest) -> float:
        """Estimate processing time based on document count and sizes"""
        if not job_manifest.documents:
            return 0.0
        
        # Base time per document (validation overhead)
        base_time_per_doc = 2.0  # seconds
        
        # Additional time based on file size (larger files take longer to hash)
        size_factor = 0.1  # seconds per MB
        
        total_time = 0.0
        for doc in job_manifest.documents:
            doc_time = base_time_per_doc
            size_mb = doc.file_size / (1024 * 1024)
            doc_time += size_mb * size_factor
            total_time += doc_time
        
        return total_time

    def _has_suspicious_extension(self, file_path: Path) -> bool:
        """Check for suspicious file extensions"""
        suspicious_extensions = {'.exe', '.bat', '.cmd', '.scr', '.com', '.pif', '.vbs', '.js'}
        return file_path.suffix.lower() in suspicious_extensions
