"""
Vectorization & Indexing Agent

Handles vector database operations including embedding generation,
ChromaDB integration, and semantic search index creation.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import uuid

from ..core.base_agent import BaseAgent, AgentError
from ..models.job_manifest import JobManifest


class VectorizationIndexingAgent(BaseAgent):
    """
    Agent responsible for:
    - ChromaDB integration with collection management
    - Embedding generation with batch processing
    - Citation preservation with source document mapping
    - Similarity search optimization
    - Index persistence and incremental updates
    """
    
    def __init__(self, agent_name: str, config: Dict[str, Any]):
        super().__init__(agent_name, config)
        
        # ChromaDB configuration
        self.chroma_db_path = config.get("chroma_db_path", "data/chroma_db")
        self.collection_name_prefix = config.get("collection_name_prefix", "study_guide")
        
        # Embedding configuration
        self.embedding_model_name = config.get("embedding_model", "nomic-embed-text")
        self.embedding_batch_size = config.get("embedding_batch_size", 128)
        
        # Search configuration
        self.default_search_results = config.get("default_search_results", 10)
        self.similarity_threshold = config.get("similarity_threshold", 0.7)
        
        # Output configuration
        self.index_output_dir = Path(config.get("index_output_dir", "data/vector_index"))
        self.index_output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.chroma_client = None
        self.embedding_model = None
        self._init_chroma_client()
        self._init_embedding_model()

    def _init_chroma_client(self):
        """Initialize ChromaDB client"""
        try:
            import chromadb
            from chromadb.config import Settings
            
            # Create persistent client
            self.chroma_client = chromadb.PersistentClient(
                path=self.chroma_db_path,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            self.logger.info(f"ChromaDB client initialized at {self.chroma_db_path}")
            
        except ImportError:
            self.logger.error("ChromaDB not available")
            raise AgentError("ChromaDB package required for vectorization")
        except Exception as e:
            self.logger.error(f"Failed to initialize ChromaDB: {e}")
            raise AgentError(f"Failed to initialize ChromaDB: {e}")

    def _init_embedding_model(self):
        """Initialize embedding model"""
        try:
            from sentence_transformers import SentenceTransformer
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            self.logger.info(f"Embedding model initialized: {self.embedding_model_name}")
            
        except ImportError:
            self.logger.error("sentence-transformers not available")
            raise AgentError("sentence-transformers package required for embeddings")
        except Exception as e:
            self.logger.error(f"Failed to initialize embedding model: {e}")
            raise AgentError(f"Failed to initialize embedding model: {e}")

    async def process(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """
        Process chunks through vectorization and indexing pipeline.
        
        Args:
            job_manifest: Job containing chunking results
            
        Returns:
            Dict with vectorization results and collection info
        """
        self.update_progress(5.0, "Starting vectorization and indexing")
        
        # Load chunking results
        chunking_results = await self._load_chunking_results(job_manifest)
        
        if not chunking_results:
            raise AgentError("No chunking results found for vectorization")
        
        # Create collection for this job
        collection_name = f"{self.collection_name_prefix}_{job_manifest.job_id}"
        collection = await self._create_or_get_collection(collection_name)
        
        self.update_progress(15.0, f"Created collection: {collection_name}")
        
        # Process all chunks
        total_chunks = 0
        processed_chunks = 0
        failed_chunks = 0
        
        for doc_result in chunking_results:
            if doc_result.get("status") != "success":
                continue
                
            chunks = doc_result.get("chunks", [])
            total_chunks += len(chunks)
            
            try:
                self.update_progress(
                    20.0 + (processed_chunks / max(total_chunks, 1)) * 70.0,
                    f"Processing chunks for {doc_result.get('filename', 'unknown')}"
                )
                
                # Process chunks in batches
                batch_results = await self._process_document_chunks(
                    chunks, collection, doc_result
                )
                
                processed_chunks += batch_results["processed"]
                failed_chunks += batch_results["failed"]
                
            except Exception as e:
                self.logger.error(f"Failed to process chunks for {doc_result.get('filename')}: {e}")
                failed_chunks += len(chunks)
        
        # Generate collection statistics
        collection_stats = await self._get_collection_stats(collection)
        
        self.update_progress(95.0, "Finalizing vector index")
        
        # Save vectorization report
        vectorization_report = {
            "job_id": job_manifest.job_id,
            "collection_name": collection_name,
            "total_chunks": total_chunks,
            "processed_chunks": processed_chunks,
            "failed_chunks": failed_chunks,
            "collection_stats": collection_stats,
            "embedding_model": self.embedding_model_name,
            "search_config": {
                "default_results": self.default_search_results,
                "similarity_threshold": self.similarity_threshold
            }
        }
        
        report_path = self.index_output_dir / f"{job_manifest.job_id}_vectorization_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(vectorization_report, f, indent=2, ensure_ascii=False)
        
        self.add_output_artifact(str(report_path), "vectorization_report")
        
        self.update_progress(100.0, "Vectorization and indexing completed")
        
        return {
            "vectorization_summary": {
                "collection_name": collection_name,
                "total_chunks": total_chunks,
                "processed_chunks": processed_chunks,
                "failed_chunks": failed_chunks,
                "success_rate": processed_chunks / max(total_chunks, 1)
            },
            "output_artifacts": [str(report_path)],
            "collection_stats": collection_stats
        }

    async def _load_chunking_results(self, job_manifest: JobManifest) -> List[Dict[str, Any]]:
        """Load chunking results from previous stage"""
        chunking_stage = job_manifest.processing_stages.get("semantic_chunking")
        if not chunking_stage or not chunking_stage.output_artifacts:
            return []
        
        # Load chunking report
        for artifact_path in chunking_stage.output_artifacts:
            if "chunking_report.json" in artifact_path:
                try:
                    with open(artifact_path, 'r', encoding='utf-8') as f:
                        chunking_report = json.load(f)
                    return chunking_report.get("chunking_results", [])
                except Exception as e:
                    self.logger.error(f"Failed to load chunking report: {e}")
        
        return []

    async def _create_or_get_collection(self, collection_name: str):
        """Create or retrieve ChromaDB collection"""
        def _create_collection():
            try:
                # Try to get existing collection
                collection = self.chroma_client.get_collection(name=collection_name)
                self.logger.info(f"Retrieved existing collection: {collection_name}")
                return collection
            except Exception:
                # Create new collection
                collection = self.chroma_client.create_collection(
                    name=collection_name,
                    metadata={
                        "description": f"Study guide collection for job",
                        "embedding_model": self.embedding_model_name
                    }
                )
                self.logger.info(f"Created new collection: {collection_name}")
                return collection
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _create_collection)

    async def _process_document_chunks(
        self, 
        chunks: List[Dict[str, Any]], 
        collection, 
        doc_result: Dict[str, Any]
    ) -> Dict[str, int]:
        """Process chunks for a single document"""
        processed = 0
        failed = 0
        
        # Process in batches
        for i in range(0, len(chunks), self.embedding_batch_size):
            batch = chunks[i:i + self.embedding_batch_size]
            
            try:
                await self._process_chunk_batch(batch, collection, doc_result)
                processed += len(batch)
            except Exception as e:
                self.logger.error(f"Failed to process chunk batch: {e}")
                failed += len(batch)
        
        return {"processed": processed, "failed": failed}

    async def _process_chunk_batch(
        self, 
        chunk_batch: List[Dict[str, Any]], 
        collection, 
        doc_result: Dict[str, Any]
    ):
        """Process a batch of chunks"""
        def _process_batch():
            # Extract texts for embedding
            texts = [chunk["text"] for chunk in chunk_batch]
            
            # Generate embeddings
            embeddings = self.embedding_model.encode(
                texts,
                convert_to_numpy=True,
                show_progress_bar=False
            )
            
            # Prepare data for ChromaDB
            ids = [chunk["chunk_id"] for chunk in chunk_batch]
            metadatas = []
            
            for chunk in chunk_batch:
                metadata = {
                    "document_id": chunk["document_id"],
                    "filename": chunk["filename"],
                    "chunk_index": chunk["chunk_index"],
                    "chunk_size": chunk["chunk_size"],
                    "token_count": chunk["token_count"],
                    "source_type": doc_result.get("mime_type", "unknown")
                }
                metadatas.append(metadata)
            
            # Add to collection
            collection.add(
                embeddings=embeddings.tolist(),
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            
            return len(chunk_batch)
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _process_batch)

    async def _get_collection_stats(self, collection) -> Dict[str, Any]:
        """Get statistics about the collection"""
        def _get_stats():
            try:
                count = collection.count()
                
                # Get sample of metadata to analyze
                sample_size = min(100, count)
                if sample_size > 0:
                    results = collection.get(limit=sample_size)
                    
                    # Analyze metadata
                    document_types = {}
                    total_tokens = 0
                    chunk_sizes = []
                    
                    for metadata in results.get("metadatas", []):
                        # Count document types
                        source_type = metadata.get("source_type", "unknown")
                        document_types[source_type] = document_types.get(source_type, 0) + 1
                        
                        # Collect token counts and chunk sizes
                        total_tokens += metadata.get("token_count", 0)
                        chunk_sizes.append(metadata.get("chunk_size", 0))
                    
                    avg_chunk_size = sum(chunk_sizes) / len(chunk_sizes) if chunk_sizes else 0
                    avg_tokens = total_tokens / len(results.get("metadatas", [])) if results.get("metadatas") else 0
                    
                    return {
                        "total_chunks": count,
                        "document_types": document_types,
                        "average_chunk_size": avg_chunk_size,
                        "average_tokens_per_chunk": avg_tokens,
                        "estimated_total_tokens": (avg_tokens * count) if count > sample_size else total_tokens
                    }
                else:
                    return {
                        "total_chunks": 0,
                        "document_types": {},
                        "average_chunk_size": 0,
                        "average_tokens_per_chunk": 0,
                        "estimated_total_tokens": 0
                    }
                    
            except Exception as e:
                self.logger.error(f"Failed to get collection stats: {e}")
                return {"error": str(e)}
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _get_stats)

    async def search_similar_chunks(
        self, 
        collection_name: str, 
        query: str, 
        n_results: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar chunks in the collection"""
        n_results = n_results or self.default_search_results
        
        def _search():
            try:
                collection = self.chroma_client.get_collection(name=collection_name)
                
                # Generate query embedding
                query_embedding = self.embedding_model.encode([query])
                
                # Search
                results = collection.query(
                    query_embeddings=query_embedding.tolist(),
                    n_results=n_results,
                    include=["documents", "metadatas", "distances"]
                )
                
                # Format results
                formatted_results = []
                for i in range(len(results["ids"][0])):
                    result = {
                        "chunk_id": results["ids"][0][i],
                        "document": results["documents"][0][i],
                        "metadata": results["metadatas"][0][i],
                        "distance": results["distances"][0][i],
                        "similarity": 1 - results["distances"][0][i]  # Convert distance to similarity
                    }
                    
                    # Filter by similarity threshold
                    if result["similarity"] >= self.similarity_threshold:
                        formatted_results.append(result)
                
                return formatted_results
                
            except Exception as e:
                self.logger.error(f"Search failed: {e}")
                return []
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _search)

    def validate_inputs(self, job_manifest: JobManifest) -> bool:
        """Validate that chunking results are available"""
        chunking_stage = job_manifest.processing_stages.get("semantic_chunking")
        if not chunking_stage:
            self.logger.error("Semantic chunking stage not found")
            return False
        
        if chunking_stage.state.value != "completed":
            self.logger.error("Semantic chunking stage not completed")
            return False
        
        return True

    def estimate_processing_time(self, job_manifest: JobManifest) -> float:
        """Estimate processing time based on chunk count"""
        # Base time for setup
        base_time = 15.0  # seconds
        
        # Estimate chunk count based on document sizes
        total_size_mb = sum(doc.file_size for doc in job_manifest.documents) / (1024 * 1024)
        
        # Rough estimate: ~50 chunks per MB of text
        estimated_chunks = total_size_mb * 50
        
        # Embedding generation and indexing time
        # Estimate ~0.1 seconds per chunk for embedding + indexing
        processing_time = estimated_chunks * 0.1
        
        return base_time + processing_time
