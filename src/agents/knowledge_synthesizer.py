"""
Knowledge Synthesizer Agent

Generates comprehensive study guide content using RAG-based synthesis,
multi-query retrieval, and advanced LLM processing.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import re

from ..core.base_agent import BaseAgent, AgentError
from ..models.job_manifest import JobManifest


class KnowledgeSynthesizerAgent(BaseAgent):
    """
    Agent responsible for:
    - Section-by-section content generation using llama3:70b-instruct
    - Multi-query RAG with query expansion and result fusion
    - Context window management with sliding attention
    - Fact verification against source documents
    - Content coherence checking across sections
    """
    
    def __init__(self, agent_name: str, config: Dict[str, Any]):
        super().__init__(agent_name, config)
        
        # LLM configuration
        self.llm_model = config.get("llm_model", "llama3:70b-instruct")
        self.ollama_base_url = config.get("ollama_base_url", "http://localhost:11434")
        
        # Content generation configuration
        self.max_context_tokens = config.get("max_context_tokens", 6000)
        self.content_generation_temperature = config.get("content_generation_temperature", 0.4)
        self.queries_per_section = config.get("queries_per_section", 3)
        self.chunks_per_query = config.get("chunks_per_query", 8)
        
        # Quality control
        self.enable_fact_verification = config.get("enable_fact_verification", True)
        self.coherence_check_enabled = config.get("coherence_check_enabled", True)
        self.min_section_length = config.get("min_section_length", 500)
        self.max_section_length = config.get("max_section_length", 3000)
        
        # Output configuration
        self.content_output_dir = Path(config.get("content_output_dir", "data/synthesized_content"))
        self.content_output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.ollama_client = None
        self.vector_search = None
        self._init_ollama_client()

    def _init_ollama_client(self):
        """Initialize Ollama client for LLM inference"""
        try:
            import requests
            
            # Test connection to Ollama
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                self.ollama_client = requests.Session()
                self.logger.info(f"Ollama client initialized at {self.ollama_base_url}")
            else:
                raise AgentError(f"Ollama server not responding: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Ollama client: {e}")
            raise AgentError(f"Failed to initialize Ollama client: {e}")

    async def process(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """
        Synthesize comprehensive study guide content from outline and vector database.
        
        Args:
            job_manifest: Job containing outline and vectorization results
            
        Returns:
            Dict with content synthesis results
        """
        self.update_progress(5.0, "Starting knowledge synthesis")
        
        # Load outline and setup vector search
        outline, collection_name = await self._load_outline_and_setup_search(job_manifest)
        
        if not outline or not collection_name:
            raise AgentError("Outline or vector collection not found for synthesis")
        
        self.update_progress(15.0, "Loaded outline and vector search")
        
        # Generate content for each section
        synthesized_content = {
            "job_id": job_manifest.job_id,
            "title": outline.get("title", "Study Guide"),
            "overview": outline.get("overview", ""),
            "sections": []
        }
        
        total_sections = len(outline.get("sections", []))
        
        for i, section in enumerate(outline.get("sections", [])):
            try:
                self.update_progress(
                    20.0 + (i / total_sections) * 70.0,
                    f"Synthesizing section: {section.get('title', 'Unknown')}"
                )
                
                # Generate content for this section
                section_content = await self._synthesize_section(section, collection_name)
                synthesized_content["sections"].append(section_content)
                
            except Exception as e:
                self.logger.error(f"Failed to synthesize section {section.get('title')}: {e}")
                # Create error section
                error_section = {
                    "id": section.get("id", f"section_{i}"),
                    "title": section.get("title", "Unknown Section"),
                    "content": f"Error generating content: {str(e)}",
                    "status": "failed",
                    "subsections": []
                }
                synthesized_content["sections"].append(error_section)
        
        # Perform coherence check across sections
        if self.coherence_check_enabled:
            self.update_progress(92.0, "Performing coherence check")
            await self._check_content_coherence(synthesized_content)
        
        # Save synthesized content
        self.update_progress(95.0, "Saving synthesized content")
        
        content_path = self.content_output_dir / f"{job_manifest.job_id}_synthesized_content.json"
        with open(content_path, 'w', encoding='utf-8') as f:
            json.dump(synthesized_content, f, indent=2, ensure_ascii=False)
        
        self.add_output_artifact(str(content_path), "synthesized_content")
        
        # Generate content statistics
        content_stats = self._calculate_content_statistics(synthesized_content)
        
        self.update_progress(100.0, "Knowledge synthesis completed")
        
        return {
            "synthesis_summary": {
                "total_sections": len(synthesized_content["sections"]),
                "successful_sections": sum(1 for s in synthesized_content["sections"] if s.get("status") != "failed"),
                "total_word_count": content_stats["total_words"],
                "average_section_length": content_stats["avg_section_length"]
            },
            "output_artifacts": [str(content_path)],
            "content_statistics": content_stats,
            "synthesized_content": synthesized_content
        }

    async def _load_outline_and_setup_search(self, job_manifest: JobManifest) -> Tuple[Optional[Dict], Optional[str]]:
        """Load outline and setup vector search"""
        outline = None
        collection_name = None
        
        # Load outline from outline generation stage
        outline_stage = job_manifest.processing_stages.get("outline_generation")
        if outline_stage and outline_stage.output_artifacts:
            for artifact_path in outline_stage.output_artifacts:
                if artifact_path.endswith("_outline.json"):
                    try:
                        with open(artifact_path, 'r', encoding='utf-8') as f:
                            outline = json.load(f)
                        break
                    except Exception as e:
                        self.logger.error(f"Failed to load outline: {e}")
        
        # Setup vector search
        vectorization_stage = job_manifest.processing_stages.get("vectorization_indexing")
        if vectorization_stage and vectorization_stage.output_artifacts:
            for artifact_path in vectorization_stage.output_artifacts:
                if "vectorization_report.json" in artifact_path:
                    try:
                        with open(artifact_path, 'r', encoding='utf-8') as f:
                            vectorization_report = json.load(f)
                        
                        collection_name = vectorization_report.get("collection_name")
                        if collection_name:
                            from ..agents.vectorization_indexing import VectorizationIndexingAgent
                            self.vector_search = VectorizationIndexingAgent("vector_search", self.config)
                        break
                    except Exception as e:
                        self.logger.error(f"Failed to load vectorization report: {e}")
        
        return outline, collection_name

    async def _synthesize_section(self, section: Dict[str, Any], collection_name: str) -> Dict[str, Any]:
        """Synthesize content for a single section"""
        section_title = section.get("title", "")
        section_id = section.get("id", "")
        
        # Generate multiple queries for comprehensive retrieval
        queries = await self._generate_section_queries(section)
        
        # Retrieve relevant content using multi-query RAG
        relevant_chunks = await self._multi_query_retrieval(queries, collection_name)
        
        # Generate main section content
        main_content = await self._generate_section_content(section, relevant_chunks)
        
        # Process subsections
        subsections = []
        for subsection in section.get("subsections", []):
            subsection_content = await self._synthesize_subsection(subsection, collection_name)
            subsections.append(subsection_content)
        
        # Fact verification if enabled
        if self.enable_fact_verification:
            main_content = await self._verify_facts(main_content, relevant_chunks)
        
        return {
            "id": section_id,
            "title": section_title,
            "content": main_content,
            "word_count": len(main_content.split()),
            "subsections": subsections,
            "status": "success",
            "learning_objectives": section.get("learning_objectives", []),
            "key_points": section.get("key_points", [])
        }

    async def _generate_section_queries(self, section: Dict[str, Any]) -> List[str]:
        """Generate multiple queries for comprehensive content retrieval"""
        section_title = section.get("title", "")
        learning_objectives = section.get("learning_objectives", [])
        
        # Base queries
        queries = [
            section_title,
            f"introduction to {section_title}",
            f"{section_title} concepts and principles"
        ]
        
        # Add queries based on learning objectives
        for objective in learning_objectives[:2]:  # Limit to avoid too many queries
            queries.append(objective)
        
        # Add queries for subsections
        for subsection in section.get("subsections", [])[:2]:  # Limit subsection queries
            subsection_title = subsection.get("title", "")
            if subsection_title:
                queries.append(f"{section_title} {subsection_title}")
        
        return queries[:self.queries_per_section]

    async def _multi_query_retrieval(self, queries: List[str], collection_name: str) -> List[Dict[str, Any]]:
        """Perform multi-query retrieval and fusion"""
        all_chunks = []
        chunk_scores = {}
        
        for query in queries:
            chunks = await self.vector_search.search_similar_chunks(
                collection_name, query, n_results=self.chunks_per_query
            )
            
            for chunk in chunks:
                chunk_id = chunk["chunk_id"]
                similarity = chunk["similarity"]
                
                if chunk_id in chunk_scores:
                    # Fusion: take maximum similarity score
                    chunk_scores[chunk_id] = max(chunk_scores[chunk_id], similarity)
                else:
                    chunk_scores[chunk_id] = similarity
                    all_chunks.append(chunk)
        
        # Sort by fused scores and remove duplicates
        unique_chunks = {}
        for chunk in all_chunks:
            chunk_id = chunk["chunk_id"]
            if chunk_id not in unique_chunks:
                chunk["fused_score"] = chunk_scores[chunk_id]
                unique_chunks[chunk_id] = chunk
        
        # Sort by fused score and return top chunks
        sorted_chunks = sorted(
            unique_chunks.values(),
            key=lambda x: x["fused_score"],
            reverse=True
        )
        
        return sorted_chunks[:self.chunks_per_query * 2]  # Return more chunks for better context

    async def _generate_section_content(
        self, 
        section: Dict[str, Any], 
        relevant_chunks: List[Dict[str, Any]]
    ) -> str:
        """Generate comprehensive content for a section"""
        section_title = section.get("title", "")
        learning_objectives = section.get("learning_objectives", [])
        
        # Prepare context from relevant chunks
        context_text = self._prepare_context_text(relevant_chunks)
        
        # Create content generation prompt
        content_prompt = f"""
You are an expert educational content writer creating a comprehensive study guide section.

Section Title: {section_title}

Learning Objectives:
{chr(10).join(f"- {obj}" for obj in learning_objectives)}

Relevant Source Content:
{context_text}

Instructions:
1. Write a comprehensive, well-structured section covering the topic thoroughly
2. Include clear explanations of key concepts and principles
3. Use examples and illustrations where appropriate
4. Ensure content flows logically and is easy to understand
5. Address all learning objectives
6. Write in an educational, engaging style suitable for students
7. Aim for {self.min_section_length}-{self.max_section_length} words

Generate the section content:
"""
        
        content = await self._call_llm(content_prompt)
        
        # Post-process content
        content = self._post_process_content(content)
        
        return content

    async def _synthesize_subsection(self, subsection: Dict[str, Any], collection_name: str) -> Dict[str, Any]:
        """Synthesize content for a subsection"""
        subsection_title = subsection.get("title", "")
        subsection_id = subsection.get("id", "")
        description = subsection.get("description", "")
        
        # Search for subsection-specific content
        query = f"{subsection_title} {description}".strip()
        relevant_chunks = await self.vector_search.search_similar_chunks(
            collection_name, query, n_results=5
        )
        
        # Generate subsection content
        context_text = self._prepare_context_text(relevant_chunks)
        
        subsection_prompt = f"""
Write a focused subsection for a study guide.

Subsection Title: {subsection_title}
Description: {description}

Relevant Content:
{context_text}

Instructions:
1. Write a clear, focused explanation of the subsection topic
2. Keep it concise but informative (200-800 words)
3. Include specific details and examples from the source content
4. Ensure it complements the main section content

Generate the subsection content:
"""
        
        content = await self._call_llm(subsection_prompt)
        content = self._post_process_content(content)
        
        return {
            "id": subsection_id,
            "title": subsection_title,
            "content": content,
            "word_count": len(content.split()),
            "cross_references": subsection.get("cross_references", [])
        }

    def _prepare_context_text(self, chunks: List[Dict[str, Any]]) -> str:
        """Prepare context text from chunks, managing token limits"""
        context_parts = []
        current_length = 0
        
        for i, chunk in enumerate(chunks):
            chunk_text = f"Source {i+1}: {chunk['document']}"
            chunk_length = len(chunk_text.split()) * 1.3  # Rough token estimate
            
            if current_length + chunk_length > self.max_context_tokens:
                break
            
            context_parts.append(chunk_text)
            current_length += chunk_length
        
        return "\n\n".join(context_parts)

    async def _verify_facts(self, content: str, source_chunks: List[Dict[str, Any]]) -> str:
        """Verify facts in generated content against source material"""
        # Extract key claims from content
        claims = self._extract_key_claims(content)
        
        if not claims:
            return content
        
        # Verify each claim
        source_text = " ".join([chunk["document"] for chunk in source_chunks[:5]])
        
        verification_prompt = f"""
Verify the following claims against the provided source material. Mark each claim as VERIFIED, UNVERIFIED, or CONTRADICTED.

Claims to verify:
{chr(10).join(f"{i+1}. {claim}" for i, claim in enumerate(claims))}

Source Material:
{source_text[:2000]}...

For each claim, provide:
- Status: VERIFIED/UNVERIFIED/CONTRADICTED
- Brief explanation

Format as JSON array of objects with "claim", "status", and "explanation" fields.
"""
        
        try:
            verification_result = await self._call_llm(verification_prompt)
            verifications = json.loads(verification_result)
            
            # Log any unverified or contradicted claims
            for verification in verifications:
                if verification.get("status") in ["UNVERIFIED", "CONTRADICTED"]:
                    self.logger.warning(f"Fact check issue: {verification}")
            
        except Exception as e:
            self.logger.warning(f"Fact verification failed: {e}")
        
        return content

    def _extract_key_claims(self, content: str) -> List[str]:
        """Extract key factual claims from content"""
        # Simple extraction of sentences that might contain factual claims
        sentences = re.split(r'[.!?]+', content)
        
        # Filter for sentences that might contain facts
        claims = []
        for sentence in sentences:
            sentence = sentence.strip()
            if (len(sentence) > 20 and 
                any(word in sentence.lower() for word in ['is', 'are', 'was', 'were', 'can', 'will', 'must'])):
                claims.append(sentence)
        
        return claims[:5]  # Limit to 5 key claims

    async def _check_content_coherence(self, synthesized_content: Dict[str, Any]):
        """Check coherence across sections"""
        sections = synthesized_content.get("sections", [])
        
        if len(sections) < 2:
            return
        
        # Check for logical flow and consistency
        coherence_prompt = f"""
Review the following study guide sections for coherence, logical flow, and consistency.

Sections:
{chr(10).join(f"Section {i+1}: {section.get('title', '')}" for i, section in enumerate(sections[:5]))}

Check for:
1. Logical progression of topics
2. Consistent terminology and definitions
3. Appropriate cross-references
4. No contradictory information

Provide a brief coherence assessment and any recommendations for improvement.
"""
        
        try:
            coherence_assessment = await self._call_llm(coherence_prompt)
            self.logger.info(f"Coherence assessment: {coherence_assessment}")
        except Exception as e:
            self.logger.warning(f"Coherence check failed: {e}")

    def _post_process_content(self, content: str) -> str:
        """Post-process generated content"""
        # Remove any unwanted patterns or formatting issues
        content = re.sub(r'\n{3,}', '\n\n', content)  # Limit consecutive newlines
        content = content.strip()
        
        # Ensure minimum length
        if len(content.split()) < 50:
            content += "\n\n[Note: This section may need additional content development.]"
        
        return content

    async def _call_llm(self, prompt: str) -> str:
        """Call Ollama LLM with the given prompt"""
        def _make_request():
            payload = {
                "model": self.llm_model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": self.content_generation_temperature,
                    "top_p": 0.9,
                    "max_tokens": 4000
                }
            }
            
            response = self.ollama_client.post(
                f"{self.ollama_base_url}/api/generate",
                json=payload,
                timeout=300  # Longer timeout for content generation
            )
            
            if response.status_code == 200:
                return response.json().get("response", "")
            else:
                raise AgentError(f"LLM request failed: {response.status_code}")
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _make_request)

    def _calculate_content_statistics(self, synthesized_content: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate statistics about the synthesized content"""
        sections = synthesized_content.get("sections", [])
        
        total_words = 0
        section_lengths = []
        successful_sections = 0
        
        for section in sections:
            if section.get("status") == "success":
                successful_sections += 1
                word_count = section.get("word_count", 0)
                total_words += word_count
                section_lengths.append(word_count)
                
                # Add subsection word counts
                for subsection in section.get("subsections", []):
                    subsection_words = subsection.get("word_count", 0)
                    total_words += subsection_words
        
        avg_section_length = sum(section_lengths) / len(section_lengths) if section_lengths else 0
        
        return {
            "total_words": total_words,
            "total_sections": len(sections),
            "successful_sections": successful_sections,
            "avg_section_length": avg_section_length,
            "min_section_length": min(section_lengths) if section_lengths else 0,
            "max_section_length": max(section_lengths) if section_lengths else 0
        }

    def validate_inputs(self, job_manifest: JobManifest) -> bool:
        """Validate that outline and vectorization results are available"""
        outline_stage = job_manifest.processing_stages.get("outline_generation")
        if not outline_stage or outline_stage.state.value != "completed":
            self.logger.error("Outline generation stage not completed")
            return False
        
        vectorization_stage = job_manifest.processing_stages.get("vectorization_indexing")
        if not vectorization_stage or vectorization_stage.state.value != "completed":
            self.logger.error("Vectorization stage not completed")
            return False
        
        return True

    def estimate_processing_time(self, job_manifest: JobManifest) -> float:
        """Estimate processing time based on outline complexity"""
        # Base time for setup
        base_time = 60.0  # seconds
        
        # Estimate based on document complexity and count
        doc_count = len(job_manifest.documents)
        total_size_mb = sum(doc.file_size for doc in job_manifest.documents) / (1024 * 1024)
        
        # Content synthesis is the most time-consuming step
        # Estimate ~2 minutes per MB of content for comprehensive synthesis
        processing_time = total_size_mb * 120.0
        
        # Additional time based on document count (more documents = more sections)
        complexity_factor = doc_count * 30.0
        
        return base_time + processing_time + complexity_factor
