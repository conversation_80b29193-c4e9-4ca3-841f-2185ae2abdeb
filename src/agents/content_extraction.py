"""
Content Extraction Agent

Handles text extraction from various document formats including:
- Digital PDF text extraction with layout preservation
- OCR for scanned documents and images
- Table detection and structured data extraction
- Multi-column layout handling
"""

import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import json

from ..core.base_agent import BaseAgent, AgentError
from ..models.job_manifest import JobManifest, DocumentMetadata


class ContentExtractionAgent(BaseAgent):
    """
    Agent responsible for:
    - Digital PDF text extraction using PyMuPDF
    - OCR for scanned documents using PaddleOCR
    - Table detection and extraction
    - Image extraction and cataloging
    - Multi-column layout handling with reading order detection
    """
    
    def __init__(self, agent_name: str, config: Dict[str, Any]):
        super().__init__(agent_name, config)
        
        self.ocr_confidence_threshold = config.get("ocr_confidence_threshold", 0.7)
        self.enable_table_detection = config.get("enable_table_detection", True)
        self.preserve_layout = config.get("preserve_layout", True)
        self.extract_images = config.get("extract_images", True)
        
        # Output directories
        self.text_output_dir = Path(config.get("text_output_dir", "data/extracted_text"))
        self.image_output_dir = Path(config.get("image_output_dir", "data/extracted_images"))
        self.table_output_dir = Path(config.get("table_output_dir", "data/extracted_tables"))
        
        # Create output directories
        for dir_path in [self.text_output_dir, self.image_output_dir, self.table_output_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize OCR engine
        self.ocr_engine = None
        self._init_ocr_engine()

    def _init_ocr_engine(self):
        """Initialize PaddleOCR engine"""
        try:
            from paddleocr import PaddleOCR
            self.ocr_engine = PaddleOCR(
                use_angle_cls=True,
                lang='en',
                show_log=False,
                use_gpu=True  # Will fallback to CPU if GPU not available
            )
            self.logger.info("PaddleOCR initialized successfully")
        except ImportError:
            self.logger.warning("PaddleOCR not available, OCR functionality disabled")
        except Exception as e:
            self.logger.error(f"Failed to initialize PaddleOCR: {e}")

    async def process(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """
        Extract content from all documents in the job.
        
        Args:
            job_manifest: Job containing validated documents
            
        Returns:
            Dict with extraction results and output artifacts
        """
        self.update_progress(5.0, "Starting content extraction")
        
        extraction_results = []
        total_docs = len(job_manifest.documents)
        
        for i, doc_metadata in enumerate(job_manifest.documents):
            try:
                self.update_progress(
                    10.0 + (i / total_docs) * 80.0,
                    f"Extracting content from {doc_metadata.original_filename}"
                )
                
                # Extract content based on document type
                result = await self._extract_document_content(doc_metadata, job_manifest.job_id)
                extraction_results.append(result)
                
            except Exception as e:
                self.logger.error(f"Failed to extract content from {doc_metadata.original_filename}: {e}")
                # Create error result
                error_result = {
                    "document_id": doc_metadata.checksum_sha256,
                    "filename": doc_metadata.original_filename,
                    "status": "failed",
                    "error": str(e),
                    "extracted_text": "",
                    "page_count": 0,
                    "images": [],
                    "tables": []
                }
                extraction_results.append(error_result)
        
        # Consolidate results
        total_pages = sum(result.get("page_count", 0) for result in extraction_results)
        total_text_length = sum(len(result.get("extracted_text", "")) for result in extraction_results)
        successful_extractions = sum(1 for result in extraction_results if result.get("status") == "success")
        
        self.update_progress(95.0, "Finalizing extraction results")
        
        # Save consolidated extraction report
        extraction_report = {
            "job_id": job_manifest.job_id,
            "total_documents": total_docs,
            "successful_extractions": successful_extractions,
            "failed_extractions": total_docs - successful_extractions,
            "total_pages_processed": total_pages,
            "total_text_length": total_text_length,
            "extraction_details": extraction_results
        }
        
        report_path = self.text_output_dir / f"{job_manifest.job_id}_extraction_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(extraction_report, f, indent=2, ensure_ascii=False)
        
        self.add_output_artifact(str(report_path), "extraction_report")
        
        self.update_progress(100.0, "Content extraction completed")
        
        return {
            "extraction_summary": {
                "total_documents": total_docs,
                "successful_extractions": successful_extractions,
                "total_pages": total_pages,
                "total_text_length": total_text_length
            },
            "output_artifacts": [str(report_path)],
            "extraction_results": extraction_results
        }

    async def _extract_document_content(self, doc_metadata: DocumentMetadata, job_id: str) -> Dict[str, Any]:
        """
        Extract content from a single document.
        
        Args:
            doc_metadata: Document metadata
            job_id: Job identifier for organizing outputs
            
        Returns:
            Dict with extraction results
        """
        file_path = Path(doc_metadata.file_path)
        
        result = {
            "document_id": doc_metadata.checksum_sha256,
            "filename": doc_metadata.original_filename,
            "mime_type": doc_metadata.mime_type,
            "status": "success",
            "extracted_text": "",
            "page_count": 0,
            "images": [],
            "tables": [],
            "metadata": {}
        }
        
        try:
            if doc_metadata.mime_type == "application/pdf":
                await self._extract_pdf_content(file_path, result, job_id)
            elif doc_metadata.mime_type.startswith("image/"):
                await self._extract_image_content(file_path, result, job_id)
            elif "word" in doc_metadata.mime_type:
                await self._extract_docx_content(file_path, result, job_id)
            elif doc_metadata.mime_type == "text/plain":
                await self._extract_text_content(file_path, result)
            else:
                raise AgentError(f"Unsupported document type: {doc_metadata.mime_type}")
                
        except Exception as e:
            result["status"] = "failed"
            result["error"] = str(e)
            self.logger.error(f"Content extraction failed for {doc_metadata.original_filename}: {e}")
        
        return result

    async def _extract_pdf_content(self, file_path: Path, result: Dict[str, Any], job_id: str):
        """Extract content from PDF using PyMuPDF"""
        try:
            import fitz  # PyMuPDF
        except ImportError:
            raise AgentError("PyMuPDF not available for PDF processing")
        
        def _process_pdf():
            doc = fitz.open(str(file_path))
            extracted_text = ""
            images = []
            tables = []
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                
                # Extract text with layout preservation
                if self.preserve_layout:
                    page_text = page.get_text("dict")
                    formatted_text = self._format_pdf_text_blocks(page_text)
                else:
                    formatted_text = page.get_text()
                
                extracted_text += f"\n--- Page {page_num + 1} ---\n{formatted_text}\n"
                
                # Extract images if enabled
                if self.extract_images:
                    page_images = self._extract_pdf_images(page, page_num, job_id, file_path.stem)
                    images.extend(page_images)
                
                # Extract tables if enabled
                if self.enable_table_detection:
                    page_tables = self._extract_pdf_tables(page, page_num)
                    tables.extend(page_tables)
            
            doc.close()
            return extracted_text, images, tables, doc.page_count
        
        loop = asyncio.get_event_loop()
        extracted_text, images, tables, page_count = await loop.run_in_executor(None, _process_pdf)
        
        result.update({
            "extracted_text": extracted_text,
            "page_count": page_count,
            "images": images,
            "tables": tables
        })

    async def _extract_image_content(self, file_path: Path, result: Dict[str, Any], job_id: str):
        """Extract text from image using OCR"""
        if not self.ocr_engine:
            raise AgentError("OCR engine not available")
        
        def _process_image():
            # Run OCR
            ocr_result = self.ocr_engine.ocr(str(file_path), cls=True)
            
            extracted_text = ""
            confidence_scores = []
            
            if ocr_result and ocr_result[0]:
                for line in ocr_result[0]:
                    if len(line) >= 2:
                        text = line[1][0]
                        confidence = line[1][1]
                        
                        if confidence >= self.ocr_confidence_threshold:
                            extracted_text += text + "\n"
                            confidence_scores.append(confidence)
            
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
            
            return extracted_text, avg_confidence
        
        loop = asyncio.get_event_loop()
        extracted_text, avg_confidence = await loop.run_in_executor(None, _process_image)
        
        result.update({
            "extracted_text": extracted_text,
            "page_count": 1,
            "metadata": {
                "ocr_confidence": avg_confidence,
                "extraction_method": "paddleocr"
            }
        })

    async def _extract_docx_content(self, file_path: Path, result: Dict[str, Any], job_id: str):
        """Extract content from DOCX file"""
        try:
            from docx import Document
        except ImportError:
            raise AgentError("python-docx not available for DOCX processing")
        
        def _process_docx():
            doc = Document(file_path)
            extracted_text = ""
            tables = []
            
            # Extract paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    extracted_text += paragraph.text + "\n"
            
            # Extract tables
            for i, table in enumerate(doc.tables):
                table_data = []
                for row in table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    table_data.append(row_data)
                
                if table_data:
                    tables.append({
                        "table_id": i,
                        "data": table_data,
                        "rows": len(table_data),
                        "columns": len(table_data[0]) if table_data else 0
                    })
            
            return extracted_text, tables
        
        loop = asyncio.get_event_loop()
        extracted_text, tables = await loop.run_in_executor(None, _process_docx)
        
        result.update({
            "extracted_text": extracted_text,
            "page_count": max(1, len(extracted_text) // 2000),  # Rough estimate
            "tables": tables
        })

    async def _extract_text_content(self, file_path: Path, result: Dict[str, Any]):
        """Extract content from plain text file"""
        def _process_text():
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            return content
        
        loop = asyncio.get_event_loop()
        extracted_text = await loop.run_in_executor(None, _process_text)
        
        result.update({
            "extracted_text": extracted_text,
            "page_count": max(1, len(extracted_text) // 2000)  # Rough estimate
        })

    def _format_pdf_text_blocks(self, page_dict: Dict[str, Any]) -> str:
        """Format PDF text blocks preserving layout"""
        formatted_text = ""
        
        for block in page_dict.get("blocks", []):
            if "lines" in block:  # Text block
                for line in block["lines"]:
                    line_text = ""
                    for span in line.get("spans", []):
                        line_text += span.get("text", "")
                    if line_text.strip():
                        formatted_text += line_text + "\n"
                formatted_text += "\n"  # Add spacing between blocks
        
        return formatted_text

    def _extract_pdf_images(self, page, page_num: int, job_id: str, doc_name: str) -> List[Dict[str, Any]]:
        """Extract images from PDF page"""
        images = []
        image_list = page.get_images()
        
        for img_index, img in enumerate(image_list):
            try:
                xref = img[0]
                pix = fitz.Pixmap(page.parent, xref)
                
                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    img_filename = f"{job_id}_{doc_name}_page{page_num+1}_img{img_index+1}.png"
                    img_path = self.image_output_dir / img_filename
                    pix.save(str(img_path))
                    
                    images.append({
                        "image_id": f"page{page_num+1}_img{img_index+1}",
                        "filename": img_filename,
                        "path": str(img_path),
                        "page": page_num + 1,
                        "width": pix.width,
                        "height": pix.height
                    })
                
                pix = None
            except Exception as e:
                self.logger.warning(f"Failed to extract image {img_index} from page {page_num}: {e}")
        
        return images

    def _extract_pdf_tables(self, page, page_num: int) -> List[Dict[str, Any]]:
        """Extract tables from PDF page using simple heuristics"""
        tables = []
        
        # This is a simplified table detection - in production, you might want to use
        # more sophisticated libraries like camelot-py or tabula-py
        text_dict = page.get_text("dict")
        
        # Look for text blocks that might be tables (simplified heuristic)
        potential_tables = []
        for block in text_dict.get("blocks", []):
            if "lines" in block:
                lines = []
                for line in block["lines"]:
                    line_text = ""
                    for span in line.get("spans", []):
                        line_text += span.get("text", "")
                    if line_text.strip():
                        lines.append(line_text.strip())
                
                # Simple heuristic: if multiple lines have similar structure, might be a table
                if len(lines) >= 3:
                    potential_tables.append(lines)
        
        for i, table_lines in enumerate(potential_tables):
            tables.append({
                "table_id": f"page{page_num+1}_table{i+1}",
                "page": page_num + 1,
                "raw_text": table_lines,
                "rows": len(table_lines)
            })
        
        return tables

    def validate_inputs(self, job_manifest: JobManifest) -> bool:
        """Validate that job has valid documents for extraction"""
        if not job_manifest.documents:
            self.logger.error("No documents provided for content extraction")
            return False
        
        # Check that all documents have valid status
        for doc in job_manifest.documents:
            if doc.validation_status != "valid":
                self.logger.error(f"Document {doc.original_filename} is not validated")
                return False
        
        return True

    def estimate_processing_time(self, job_manifest: JobManifest) -> float:
        """Estimate processing time based on document types and sizes"""
        if not job_manifest.documents:
            return 0.0
        
        total_time = 0.0
        
        for doc in job_manifest.documents:
            # Base time per document
            doc_time = 5.0  # seconds
            
            # Additional time based on document type and size
            size_mb = doc.file_size / (1024 * 1024)
            
            if doc.mime_type == "application/pdf":
                # PDF processing time depends on page count and whether OCR is needed
                pages = doc.page_count or 1
                doc_time += pages * 2.0  # 2 seconds per page
                
            elif doc.mime_type.startswith("image/"):
                # OCR processing is slower
                doc_time += size_mb * 10.0  # 10 seconds per MB for OCR
                
            elif "word" in doc.mime_type:
                # DOCX processing is relatively fast
                doc_time += size_mb * 1.0  # 1 second per MB
                
            else:
                # Text files are fastest
                doc_time += size_mb * 0.1  # 0.1 seconds per MB
            
            total_time += doc_time
        
        return total_time
