"""
Visual & Analogy Agent

Creates visual aids, diagrams, and analogies to enhance study guide content
using Mermaid.js diagrams and Stable Diffusion image generation.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import re
import base64
from io import BytesIO

from ..core.base_agent import BaseAgent, AgentError
from ..models.job_manifest import JobManifest


class VisualAnalogyAgent(BaseAgent):
    """
    Agent responsible for:
    - Mermaid.js diagram generation with syntax validation
    - Analogy creation with domain-specific knowledge mapping
    - Stable Diffusion XL prompt engineering with negative prompts
    - Image generation with quality control and safety filtering
    - Visual element placement optimization within document layout
    """
    
    def __init__(self, agent_name: str, config: Dict[str, Any]):
        super().__init__(agent_name, config)
        
        # LLM configuration for analogy generation
        self.llm_model = config.get("llm_model", "llama3:8b-instruct")
        self.ollama_base_url = config.get("ollama_base_url", "http://localhost:11434")
        
        # Visual generation configuration
        self.enable_diagrams = config.get("enable_diagrams", True)
        self.enable_analogies = config.get("enable_analogies", True)
        self.enable_image_generation = config.get("enable_image_generation", True)
        
        # Diagram configuration
        self.max_diagrams_per_section = config.get("max_diagrams_per_section", 2)
        self.diagram_types = config.get("diagram_types", [
            "flowchart", "mindmap", "sequence", "class", "er", "timeline"
        ])
        
        # Analogy configuration
        self.max_analogies_per_section = config.get("max_analogies_per_section", 3)
        self.analogy_domains = config.get("analogy_domains", [
            "everyday life", "sports", "cooking", "nature", "technology", "transportation"
        ])
        
        # Image generation configuration
        self.stable_diffusion_model = config.get("stable_diffusion_model", "stabilityai/stable-diffusion-xl-base-1.0")
        self.image_width = config.get("image_width", 768)
        self.image_height = config.get("image_height", 768)
        self.inference_steps = config.get("inference_steps", 20)
        self.guidance_scale = config.get("guidance_scale", 7.5)
        
        # Output configuration
        self.visual_output_dir = Path(config.get("visual_output_dir", "data/visual_content"))
        self.diagrams_dir = self.visual_output_dir / "diagrams"
        self.images_dir = self.visual_output_dir / "images"
        
        # Create output directories
        for dir_path in [self.visual_output_dir, self.diagrams_dir, self.images_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.ollama_client = None
        self.diffusion_pipeline = None
        self._init_ollama_client()
        self._init_diffusion_pipeline()

    def _init_ollama_client(self):
        """Initialize Ollama client for LLM inference"""
        try:
            import requests
            
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                self.ollama_client = requests.Session()
                self.logger.info(f"Ollama client initialized at {self.ollama_base_url}")
            else:
                raise AgentError(f"Ollama server not responding: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Ollama client: {e}")
            raise AgentError(f"Failed to initialize Ollama client: {e}")

    def _init_diffusion_pipeline(self):
        """Initialize Stable Diffusion pipeline"""
        if not self.enable_image_generation:
            return
        
        try:
            from diffusers import StableDiffusionXLPipeline
            import torch
            
            # Check if CUDA is available
            device = "cuda" if torch.cuda.is_available() else "cpu"
            
            self.diffusion_pipeline = StableDiffusionXLPipeline.from_pretrained(
                self.stable_diffusion_model,
                torch_dtype=torch.float16 if device == "cuda" else torch.float32,
                use_safetensors=True
            )
            self.diffusion_pipeline = self.diffusion_pipeline.to(device)
            
            # Enable memory efficient attention
            if device == "cuda":
                self.diffusion_pipeline.enable_attention_slicing()
                self.diffusion_pipeline.enable_model_cpu_offload()
            
            self.logger.info(f"Stable Diffusion XL initialized on {device}")
            
        except ImportError:
            self.logger.warning("Diffusers not available, image generation disabled")
            self.enable_image_generation = False
        except Exception as e:
            self.logger.error(f"Failed to initialize Stable Diffusion: {e}")
            self.enable_image_generation = False

    async def process(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """
        Generate visual aids and analogies for synthesized content.
        
        Args:
            job_manifest: Job containing synthesized content
            
        Returns:
            Dict with visual enhancement results
        """
        self.update_progress(5.0, "Starting visual enhancement")
        
        # Load synthesized content
        synthesized_content = await self._load_synthesized_content(job_manifest)
        
        if not synthesized_content:
            raise AgentError("No synthesized content found for visual enhancement")
        
        self.update_progress(15.0, "Loaded synthesized content")
        
        # Process each section for visual enhancements
        enhanced_content = {
            "job_id": job_manifest.job_id,
            "title": synthesized_content.get("title", "Study Guide"),
            "overview": synthesized_content.get("overview", ""),
            "sections": []
        }
        
        total_sections = len(synthesized_content.get("sections", []))
        visual_artifacts = []
        
        for i, section in enumerate(synthesized_content.get("sections", [])):
            try:
                self.update_progress(
                    20.0 + (i / total_sections) * 70.0,
                    f"Enhancing section: {section.get('title', 'Unknown')}"
                )
                
                # Generate visual enhancements for this section
                enhanced_section = await self._enhance_section_visually(section, job_manifest.job_id)
                enhanced_content["sections"].append(enhanced_section)
                
                # Collect visual artifacts
                if enhanced_section.get("visual_elements"):
                    visual_artifacts.extend(enhanced_section["visual_elements"])
                
            except Exception as e:
                self.logger.error(f"Failed to enhance section {section.get('title')}: {e}")
                # Keep original section without enhancements
                enhanced_content["sections"].append(section)
        
        # Save enhanced content
        self.update_progress(95.0, "Saving enhanced content")
        
        enhanced_content_path = self.visual_output_dir / f"{job_manifest.job_id}_enhanced_content.json"
        with open(enhanced_content_path, 'w', encoding='utf-8') as f:
            json.dump(enhanced_content, f, indent=2, ensure_ascii=False)
        
        self.add_output_artifact(str(enhanced_content_path), "enhanced_content")
        
        # Generate visual enhancement report
        enhancement_stats = self._calculate_enhancement_statistics(enhanced_content)
        
        enhancement_report = {
            "job_id": job_manifest.job_id,
            "total_sections": total_sections,
            "enhanced_sections": enhancement_stats["enhanced_sections"],
            "total_diagrams": enhancement_stats["total_diagrams"],
            "total_analogies": enhancement_stats["total_analogies"],
            "total_images": enhancement_stats["total_images"],
            "visual_artifacts": visual_artifacts
        }
        
        report_path = self.visual_output_dir / f"{job_manifest.job_id}_visual_enhancement_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(enhancement_report, f, indent=2, ensure_ascii=False)
        
        self.add_output_artifact(str(report_path), "visual_enhancement_report")
        
        self.update_progress(100.0, "Visual enhancement completed")
        
        return {
            "enhancement_summary": enhancement_stats,
            "output_artifacts": [str(enhanced_content_path), str(report_path)],
            "enhanced_content": enhanced_content
        }

    async def _load_synthesized_content(self, job_manifest: JobManifest) -> Optional[Dict[str, Any]]:
        """Load synthesized content from previous stage"""
        synthesis_stage = job_manifest.processing_stages.get("knowledge_synthesizer")
        if not synthesis_stage or not synthesis_stage.output_artifacts:
            return None
        
        for artifact_path in synthesis_stage.output_artifacts:
            if "synthesized_content.json" in artifact_path:
                try:
                    with open(artifact_path, 'r', encoding='utf-8') as f:
                        return json.load(f)
                except Exception as e:
                    self.logger.error(f"Failed to load synthesized content: {e}")
        
        return None

    async def _enhance_section_visually(self, section: Dict[str, Any], job_id: str) -> Dict[str, Any]:
        """Generate visual enhancements for a single section"""
        enhanced_section = section.copy()
        visual_elements = []
        
        section_title = section.get("title", "")
        section_content = section.get("content", "")
        
        # Generate diagrams if enabled
        if self.enable_diagrams and section_content:
            diagrams = await self._generate_diagrams(section_title, section_content, job_id)
            visual_elements.extend(diagrams)
        
        # Generate analogies if enabled
        if self.enable_analogies and section_content:
            analogies = await self._generate_analogies(section_title, section_content)
            enhanced_section["analogies"] = analogies
        
        # Generate conceptual images if enabled
        if self.enable_image_generation and section_content:
            images = await self._generate_conceptual_images(section_title, section_content, job_id)
            visual_elements.extend(images)
        
        # Enhance subsections
        enhanced_subsections = []
        for subsection in section.get("subsections", []):
            enhanced_subsection = await self._enhance_subsection_visually(subsection, job_id)
            enhanced_subsections.append(enhanced_subsection)
            
            if enhanced_subsection.get("visual_elements"):
                visual_elements.extend(enhanced_subsection["visual_elements"])
        
        enhanced_section["subsections"] = enhanced_subsections
        enhanced_section["visual_elements"] = visual_elements
        
        return enhanced_section

    async def _generate_diagrams(self, title: str, content: str, job_id: str) -> List[Dict[str, Any]]:
        """Generate Mermaid.js diagrams for content"""
        diagrams = []
        
        # Analyze content to determine appropriate diagram types
        diagram_suggestions = await self._suggest_diagram_types(title, content)
        
        for suggestion in diagram_suggestions[:self.max_diagrams_per_section]:
            try:
                diagram_code = await self._generate_mermaid_diagram(
                    title, content, suggestion["type"], suggestion["focus"]
                )
                
                if diagram_code:
                    # Validate Mermaid syntax
                    if self._validate_mermaid_syntax(diagram_code):
                        diagram_filename = f"{job_id}_{title.replace(' ', '_')}_{suggestion['type']}.mmd"
                        diagram_path = self.diagrams_dir / diagram_filename
                        
                        with open(diagram_path, 'w', encoding='utf-8') as f:
                            f.write(diagram_code)
                        
                        diagrams.append({
                            "type": "diagram",
                            "diagram_type": suggestion["type"],
                            "title": f"{title} - {suggestion['focus']}",
                            "filename": diagram_filename,
                            "path": str(diagram_path),
                            "mermaid_code": diagram_code
                        })
                    else:
                        self.logger.warning(f"Invalid Mermaid syntax for {title} {suggestion['type']}")
                        
            except Exception as e:
                self.logger.error(f"Failed to generate {suggestion['type']} diagram for {title}: {e}")
        
        return diagrams

    async def _suggest_diagram_types(self, title: str, content: str) -> List[Dict[str, str]]:
        """Suggest appropriate diagram types based on content analysis"""
        suggestion_prompt = f"""
Analyze the following content and suggest 1-2 appropriate diagram types that would help visualize the concepts.

Title: {title}
Content: {content[:1000]}...

Available diagram types: {', '.join(self.diagram_types)}

For each suggestion, provide:
1. Diagram type (from the available types)
2. Focus area (what specific aspect to visualize)
3. Brief justification

Format as JSON array of objects with "type", "focus", and "justification" fields.
"""
        
        try:
            response = await self._call_llm(suggestion_prompt)
            suggestions = json.loads(response)
            
            if isinstance(suggestions, list):
                return suggestions[:2]  # Limit to 2 suggestions
                
        except Exception as e:
            self.logger.warning(f"Failed to get diagram suggestions: {e}")
        
        # Fallback suggestions based on keywords
        return self._fallback_diagram_suggestions(title, content)

    async def _generate_mermaid_diagram(
        self, 
        title: str, 
        content: str, 
        diagram_type: str, 
        focus: str
    ) -> str:
        """Generate Mermaid.js diagram code"""
        diagram_prompt = f"""
Create a Mermaid.js {diagram_type} diagram for the following content, focusing on: {focus}

Title: {title}
Content: {content[:800]}...

Requirements:
1. Use proper Mermaid.js syntax for {diagram_type}
2. Include relevant nodes/elements from the content
3. Show relationships and flow clearly
4. Keep it concise but informative
5. Use appropriate labels and descriptions

Generate only the Mermaid diagram code (starting with the diagram type declaration):
"""
        
        try:
            diagram_code = await self._call_llm(diagram_prompt)
            return self._clean_mermaid_code(diagram_code)
        except Exception as e:
            self.logger.error(f"Failed to generate Mermaid diagram: {e}")
            return ""

    def _validate_mermaid_syntax(self, mermaid_code: str) -> bool:
        """Basic validation of Mermaid.js syntax"""
        # Basic syntax checks
        lines = mermaid_code.strip().split('\n')
        
        if not lines:
            return False
        
        # Check for valid diagram type declaration
        first_line = lines[0].strip()
        valid_declarations = [
            'flowchart', 'graph', 'sequenceDiagram', 'classDiagram',
            'erDiagram', 'timeline', 'mindmap', 'gitgraph'
        ]
        
        if not any(first_line.startswith(decl) for decl in valid_declarations):
            return False
        
        # Check for basic structure
        if len(lines) < 2:
            return False
        
        return True

    def _clean_mermaid_code(self, code: str) -> str:
        """Clean and format Mermaid code"""
        # Remove markdown code blocks if present
        code = re.sub(r'```mermaid\n?', '', code)
        code = re.sub(r'```\n?', '', code)
        
        # Clean up extra whitespace
        lines = [line.strip() for line in code.split('\n') if line.strip()]
        
        return '\n'.join(lines)

    async def _generate_analogies(self, title: str, content: str) -> List[Dict[str, str]]:
        """Generate analogies to explain complex concepts"""
        analogy_prompt = f"""
Create 2-3 helpful analogies to explain the concepts in this content to students.

Title: {title}
Content: {content[:1000]}...

Use analogies from these domains: {', '.join(self.analogy_domains)}

For each analogy:
1. Identify the key concept being explained
2. Create a relatable analogy from everyday experience
3. Explain how the analogy maps to the concept
4. Keep it simple and memorable

Format as JSON array of objects with "concept", "analogy", "explanation", and "domain" fields.
"""
        
        try:
            response = await self._call_llm(analogy_prompt)
            analogies = json.loads(response)
            
            if isinstance(analogies, list):
                return analogies[:self.max_analogies_per_section]
                
        except Exception as e:
            self.logger.warning(f"Failed to generate analogies: {e}")
        
        return []

    async def _generate_conceptual_images(self, title: str, content: str, job_id: str) -> List[Dict[str, Any]]:
        """Generate conceptual images using Stable Diffusion"""
        if not self.diffusion_pipeline:
            return []
        
        images = []
        
        # Generate image prompts
        image_prompts = await self._generate_image_prompts(title, content)
        
        for i, prompt_data in enumerate(image_prompts[:2]):  # Limit to 2 images per section
            try:
                image = await self._generate_image(prompt_data["prompt"], prompt_data["negative_prompt"])
                
                if image:
                    image_filename = f"{job_id}_{title.replace(' ', '_')}_concept_{i+1}.png"
                    image_path = self.images_dir / image_filename
                    
                    image.save(image_path)
                    
                    images.append({
                        "type": "image",
                        "title": f"{title} - {prompt_data['description']}",
                        "filename": image_filename,
                        "path": str(image_path),
                        "prompt": prompt_data["prompt"],
                        "description": prompt_data["description"]
                    })
                    
            except Exception as e:
                self.logger.error(f"Failed to generate image for {title}: {e}")
        
        return images

    async def _generate_image_prompts(self, title: str, content: str) -> List[Dict[str, str]]:
        """Generate image prompts for conceptual visualization"""
        prompt_generation = f"""
Create 1-2 image generation prompts for visualizing the concepts in this content.

Title: {title}
Content: {content[:800]}...

For each prompt:
1. Create a detailed, visual description suitable for AI image generation
2. Focus on educational/conceptual visualization
3. Include style guidance (e.g., "educational diagram", "scientific illustration")
4. Provide a negative prompt to avoid unwanted elements

Format as JSON array with "prompt", "negative_prompt", and "description" fields.
"""
        
        try:
            response = await self._call_llm(prompt_generation)
            prompts = json.loads(response)
            
            if isinstance(prompts, list):
                return prompts[:2]
                
        except Exception as e:
            self.logger.warning(f"Failed to generate image prompts: {e}")
        
        # Fallback prompts
        return [{
            "prompt": f"educational illustration of {title}, clean scientific diagram style, high quality",
            "negative_prompt": "text, words, letters, blurry, low quality, distorted",
            "description": f"Conceptual illustration of {title}"
        }]

    async def _generate_image(self, prompt: str, negative_prompt: str):
        """Generate image using Stable Diffusion"""
        def _generate():
            try:
                image = self.diffusion_pipeline(
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    width=self.image_width,
                    height=self.image_height,
                    num_inference_steps=self.inference_steps,
                    guidance_scale=self.guidance_scale,
                    num_images_per_prompt=1
                ).images[0]
                
                return image
                
            except Exception as e:
                self.logger.error(f"Image generation failed: {e}")
                return None
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _generate)

    async def _enhance_subsection_visually(self, subsection: Dict[str, Any], job_id: str) -> Dict[str, Any]:
        """Generate visual enhancements for a subsection"""
        enhanced_subsection = subsection.copy()
        visual_elements = []
        
        subsection_title = subsection.get("title", "")
        subsection_content = subsection.get("content", "")
        
        # Generate simple diagrams for subsections if content is substantial
        if len(subsection_content) > 300:
            diagrams = await self._generate_diagrams(subsection_title, subsection_content, job_id)
            visual_elements.extend(diagrams)
        
        enhanced_subsection["visual_elements"] = visual_elements
        
        return enhanced_subsection

    def _fallback_diagram_suggestions(self, title: str, content: str) -> List[Dict[str, str]]:
        """Fallback diagram suggestions based on keywords"""
        suggestions = []
        
        content_lower = content.lower()
        title_lower = title.lower()
        
        # Keyword-based suggestions
        if any(word in content_lower for word in ['process', 'step', 'flow', 'procedure']):
            suggestions.append({
                "type": "flowchart",
                "focus": "process flow",
                "justification": "Content describes a process or procedure"
            })
        
        if any(word in content_lower for word in ['relationship', 'connect', 'link', 'relate']):
            suggestions.append({
                "type": "mindmap",
                "focus": "concept relationships",
                "justification": "Content discusses relationships between concepts"
            })
        
        if any(word in title_lower for word in ['timeline', 'history', 'chronology', 'sequence']):
            suggestions.append({
                "type": "timeline",
                "focus": "chronological sequence",
                "justification": "Content has temporal or sequential elements"
            })
        
        return suggestions[:2]

    async def _call_llm(self, prompt: str) -> str:
        """Call Ollama LLM with the given prompt"""
        def _make_request():
            payload = {
                "model": self.llm_model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.9,
                    "max_tokens": 2000
                }
            }
            
            response = self.ollama_client.post(
                f"{self.ollama_base_url}/api/generate",
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                return response.json().get("response", "")
            else:
                raise AgentError(f"LLM request failed: {response.status_code}")
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _make_request)

    def _calculate_enhancement_statistics(self, enhanced_content: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate statistics about visual enhancements"""
        sections = enhanced_content.get("sections", [])
        
        enhanced_sections = 0
        total_diagrams = 0
        total_analogies = 0
        total_images = 0
        
        for section in sections:
            if section.get("visual_elements") or section.get("analogies"):
                enhanced_sections += 1
            
            # Count visual elements
            for element in section.get("visual_elements", []):
                if element.get("type") == "diagram":
                    total_diagrams += 1
                elif element.get("type") == "image":
                    total_images += 1
            
            # Count analogies
            total_analogies += len(section.get("analogies", []))
            
            # Count subsection enhancements
            for subsection in section.get("subsections", []):
                if subsection.get("visual_elements"):
                    for element in subsection["visual_elements"]:
                        if element.get("type") == "diagram":
                            total_diagrams += 1
                        elif element.get("type") == "image":
                            total_images += 1
        
        return {
            "total_sections": len(sections),
            "enhanced_sections": enhanced_sections,
            "total_diagrams": total_diagrams,
            "total_analogies": total_analogies,
            "total_images": total_images,
            "enhancement_rate": enhanced_sections / len(sections) if sections else 0
        }

    def validate_inputs(self, job_manifest: JobManifest) -> bool:
        """Validate that synthesized content is available"""
        synthesis_stage = job_manifest.processing_stages.get("knowledge_synthesizer")
        if not synthesis_stage or synthesis_stage.state.value != "completed":
            self.logger.error("Knowledge synthesis stage not completed")
            return False
        
        return True

    def estimate_processing_time(self, job_manifest: JobManifest) -> float:
        """Estimate processing time based on content complexity"""
        # Base time for setup
        base_time = 45.0  # seconds
        
        # Additional time based on document count and complexity
        doc_count = len(job_manifest.documents)
        
        # Visual generation is time-consuming, especially image generation
        processing_time = doc_count * 60.0  # 1 minute per document
        
        # Additional time if image generation is enabled
        if self.enable_image_generation:
            processing_time += doc_count * 120.0  # Additional 2 minutes per document for images
        
        return base_time + processing_time
