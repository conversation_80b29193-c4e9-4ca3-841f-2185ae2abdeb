"""
Final Compiler Agent

Compiles all generated content into a professional PDF study guide
using Pandoc with custom templates and formatting.
"""

import asyncio
import json
import logging
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import shutil
import tempfile

from ..core.base_agent import BaseAgent, AgentError
from ..models.job_manifest import JobManifest


class FinalCompilerAgent(BaseAgent):
    """
    Agent responsible for:
    - Pandoc-based PDF compilation with custom templates
    - Image integration with automatic sizing and positioning
    - Cross-reference resolution and table of contents generation
    - Bibliography formatting with citation style support
    - Quality assurance with layout validation and error detection
    """
    
    def __init__(self, agent_name: str, config: Dict[str, Any]):
        super().__init__(agent_name, config)
        
        # Pandoc configuration
        self.pandoc_executable = config.get("pandoc_executable", "pandoc")
        self.latex_engine = config.get("latex_engine", "xelatex")
        self.template_dir = Path(config.get("template_dir", "templates"))
        self.template_name = config.get("template_name", "study_guide_template.tex")
        
        # PDF configuration
        self.paper_size = config.get("paper_size", "letter")
        self.font_size = config.get("font_size", "11pt")
        self.margin_size = config.get("margin_size", "1in")
        self.line_spacing = config.get("line_spacing", "1.2")
        
        # Content configuration
        self.include_toc = config.get("include_toc", True)
        self.include_page_numbers = config.get("include_page_numbers", True)
        self.include_header_footer = config.get("include_header_footer", True)
        self.max_image_width = config.get("max_image_width", "0.8\\textwidth")
        
        # Quality control
        self.enable_spell_check = config.get("enable_spell_check", False)
        self.enable_grammar_check = config.get("enable_grammar_check", False)
        self.max_compilation_attempts = config.get("max_compilation_attempts", 3)
        
        # Output configuration
        self.output_dir = Path(config.get("output_dir", "data/final_output"))
        self.temp_dir = Path(config.get("temp_dir", "data/temp"))
        
        # Create output directories
        for dir_path in [self.output_dir, self.temp_dir, self.template_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self._check_dependencies()
        self._setup_templates()

    def _check_dependencies(self):
        """Check for required dependencies"""
        # Check Pandoc
        try:
            result = subprocess.run([self.pandoc_executable, "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.logger.info(f"Pandoc found: {result.stdout.split()[1]}")
            else:
                raise AgentError("Pandoc not found or not working")
        except Exception as e:
            raise AgentError(f"Pandoc dependency check failed: {e}")
        
        # Check LaTeX engine
        try:
            result = subprocess.run([self.latex_engine, "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.logger.info(f"LaTeX engine found: {self.latex_engine}")
            else:
                self.logger.warning(f"LaTeX engine {self.latex_engine} not found, trying pdflatex")
                self.latex_engine = "pdflatex"
        except Exception as e:
            self.logger.warning(f"LaTeX engine check failed: {e}")

    def _setup_templates(self):
        """Setup LaTeX templates"""
        template_path = self.template_dir / self.template_name
        
        if not template_path.exists():
            # Create default template
            self._create_default_template(template_path)
            self.logger.info(f"Created default template: {template_path}")

    def _create_default_template(self, template_path: Path):
        """Create a default LaTeX template"""
        default_template = r"""
\documentclass[$if(fontsize)$$fontsize$,$endif$$if(lang)$$babel-lang$,$endif$$if(papersize)$$papersize$paper,$endif$$for(classoption)$$classoption$$sep$,$endfor$]{article}

% Packages
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{lmodern}
\usepackage{amssymb,amsmath}
\usepackage{ifxetex,ifluatex}
\usepackage{fixltx2e}
\usepackage[unicode=true]{hyperref}
\usepackage{graphicx}
\usepackage{grffile}
\usepackage{longtable}
\usepackage{booktabs}
\usepackage{geometry}
\usepackage{fancyhdr}
\usepackage{setspace}
\usepackage{parskip}

% Page setup
\geometry{margin=$if(margin)$$margin$$else$1in$endif$}
$if(linestretch)$
\setstretch{$linestretch$}
$endif$

% Headers and footers
$if(include_header_footer)$
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{$title$}
\fancyhead[R]{\today}
\fancyfoot[C]{\thepage}
$endif$

% Title and metadata
$if(title)$
\title{$title$}
$endif$
$if(author)$
\author{$author$}
$endif$
$if(date)$
\date{$date$}
$else$
\date{\today}
$endif$

% Hyperref setup
\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    filecolor=magenta,
    urlcolor=cyan,
    pdftitle={$title$},
    pdfauthor={$author$}
}

\begin{document}

$if(title)$
\maketitle
$endif$

$if(abstract)$
\begin{abstract}
$abstract$
\end{abstract}
$endif$

$if(toc)$
\tableofcontents
\newpage
$endif$

$body$

\end{document}
"""
        
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(default_template)

    async def process(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """
        Compile all content into final PDF study guide.
        
        Args:
            job_manifest: Job containing all generated content
            
        Returns:
            Dict with compilation results
        """
        self.update_progress(5.0, "Starting final compilation")
        
        # Load all content components
        content_components = await self._load_all_content(job_manifest)
        
        if not content_components:
            raise AgentError("No content found for compilation")
        
        self.update_progress(15.0, "Loaded all content components")
        
        # Create temporary working directory
        with tempfile.TemporaryDirectory(dir=self.temp_dir) as temp_work_dir:
            work_dir = Path(temp_work_dir)
            
            # Generate markdown source
            self.update_progress(25.0, "Generating markdown source")
            markdown_content = await self._generate_markdown_source(content_components, work_dir)
            
            # Copy and prepare assets
            self.update_progress(40.0, "Preparing assets")
            await self._prepare_assets(content_components, work_dir)
            
            # Generate PDF
            self.update_progress(60.0, "Compiling PDF")
            pdf_path = await self._compile_pdf(markdown_content, work_dir, job_manifest)
            
            # Quality assurance
            self.update_progress(85.0, "Performing quality checks")
            qa_results = await self._perform_quality_checks(pdf_path)
            
            # Move final output
            final_pdf_path = self.output_dir / f"{job_manifest.job_id}_study_guide.pdf"
            shutil.copy2(pdf_path, final_pdf_path)
            
            # Update job manifest with final output
            job_manifest.final_output_path = str(final_pdf_path)
            
            self.add_output_artifact(str(final_pdf_path), "final_study_guide")
        
        # Generate compilation report
        compilation_stats = await self._generate_compilation_stats(content_components, qa_results)
        
        report_path = self.output_dir / f"{job_manifest.job_id}_compilation_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(compilation_stats, f, indent=2, ensure_ascii=False)
        
        self.add_output_artifact(str(report_path), "compilation_report")
        
        self.update_progress(100.0, "Final compilation completed")
        
        return {
            "compilation_summary": {
                "final_pdf_path": str(final_pdf_path),
                "file_size_mb": final_pdf_path.stat().st_size / (1024 * 1024),
                "total_pages": qa_results.get("page_count", 0),
                "compilation_status": "success",
                "quality_score": qa_results.get("quality_score", 0.0)
            },
            "output_artifacts": [str(final_pdf_path), str(report_path)],
            "quality_assurance": qa_results
        }

    async def _load_all_content(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """Load all content components from previous stages"""
        content_components = {
            "enhanced_content": None,
            "qa_content": None,
            "visual_elements": [],
            "metadata": {
                "job_id": job_manifest.job_id,
                "title": "Study Guide",
                "created_at": job_manifest.created_at.isoformat()
            }
        }
        
        # Load enhanced content
        visual_stage = job_manifest.processing_stages.get("visual_analogy")
        if visual_stage and visual_stage.output_artifacts:
            for artifact_path in visual_stage.output_artifacts:
                if "enhanced_content.json" in artifact_path:
                    try:
                        with open(artifact_path, 'r', encoding='utf-8') as f:
                            content_components["enhanced_content"] = json.load(f)
                        break
                    except Exception as e:
                        self.logger.error(f"Failed to load enhanced content: {e}")
        
        # Load Q&A content
        qa_stage = job_manifest.processing_stages.get("qa_generation")
        if qa_stage and qa_stage.output_artifacts:
            for artifact_path in qa_stage.output_artifacts:
                if "qa_content.json" in artifact_path:
                    try:
                        with open(artifact_path, 'r', encoding='utf-8') as f:
                            content_components["qa_content"] = json.load(f)
                        break
                    except Exception as e:
                        self.logger.error(f"Failed to load Q&A content: {e}")
        
        # Collect visual elements
        if content_components["enhanced_content"]:
            for section in content_components["enhanced_content"].get("sections", []):
                content_components["visual_elements"].extend(section.get("visual_elements", []))
        
        return content_components

    async def _generate_markdown_source(
        self, 
        content_components: Dict[str, Any], 
        work_dir: Path
    ) -> str:
        """Generate comprehensive markdown source"""
        
        enhanced_content = content_components.get("enhanced_content", {})
        qa_content = content_components.get("qa_content", {})
        
        markdown_parts = []
        
        # Title and metadata
        title = enhanced_content.get("title", "Study Guide")
        markdown_parts.append(f"---")
        markdown_parts.append(f"title: '{title}'")
        markdown_parts.append(f"author: 'AI Study Guide Generator'")
        markdown_parts.append(f"date: '\\today'")
        markdown_parts.append(f"toc: {str(self.include_toc).lower()}")
        markdown_parts.append(f"geometry: margin={self.margin_size}")
        markdown_parts.append(f"fontsize: {self.font_size}")
        markdown_parts.append(f"linestretch: {self.line_spacing}")
        markdown_parts.append(f"papersize: {self.paper_size}")
        markdown_parts.append(f"include_header_footer: {str(self.include_header_footer).lower()}")
        markdown_parts.append(f"---")
        markdown_parts.append("")
        
        # Overview
        if enhanced_content.get("overview"):
            markdown_parts.append("# Overview")
            markdown_parts.append("")
            markdown_parts.append(enhanced_content["overview"])
            markdown_parts.append("")
            markdown_parts.append("\\newpage")
            markdown_parts.append("")
        
        # Main content sections
        for section in enhanced_content.get("sections", []):
            markdown_parts.extend(await self._format_section_markdown(section, work_dir))
        
        # Q&A sections
        if qa_content and qa_content.get("qa_sections"):
            markdown_parts.append("\\newpage")
            markdown_parts.append("")
            markdown_parts.append("# Study Questions")
            markdown_parts.append("")
            
            for qa_section in qa_content["qa_sections"]:
                if qa_section.get("status") == "success":
                    markdown_parts.extend(await self._format_qa_section_markdown(qa_section))
        
        # Comprehensive exam
        if qa_content and qa_content.get("comprehensive_exam"):
            markdown_parts.append("\\newpage")
            markdown_parts.append("")
            markdown_parts.append("# Comprehensive Exam Questions")
            markdown_parts.append("")
            
            exam = qa_content["comprehensive_exam"]
            for question in exam.get("questions", []):
                markdown_parts.extend(self._format_question_markdown(question))
        
        return "\n".join(markdown_parts)

    async def _format_section_markdown(self, section: Dict[str, Any], work_dir: Path) -> List[str]:
        """Format a section as markdown"""
        lines = []
        
        # Section title
        lines.append(f"# {section.get('title', 'Untitled Section')}")
        lines.append("")
        
        # Learning objectives
        if section.get("learning_objectives"):
            lines.append("## Learning Objectives")
            lines.append("")
            for objective in section["learning_objectives"]:
                lines.append(f"- {objective}")
            lines.append("")
        
        # Main content
        content = section.get("content", "")
        if content:
            lines.append(content)
            lines.append("")
        
        # Visual elements
        for visual_element in section.get("visual_elements", []):
            lines.extend(await self._format_visual_element_markdown(visual_element, work_dir))
        
        # Analogies
        if section.get("analogies"):
            lines.append("### Key Analogies")
            lines.append("")
            for analogy in section["analogies"]:
                lines.append(f"**{analogy.get('concept', 'Concept')}**: {analogy.get('analogy', '')}")
                lines.append("")
                if analogy.get("explanation"):
                    lines.append(f"*{analogy['explanation']}*")
                    lines.append("")
        
        # Subsections
        for subsection in section.get("subsections", []):
            lines.append(f"## {subsection.get('title', 'Untitled Subsection')}")
            lines.append("")
            
            subsection_content = subsection.get("content", "")
            if subsection_content:
                lines.append(subsection_content)
                lines.append("")
            
            # Subsection visual elements
            for visual_element in subsection.get("visual_elements", []):
                lines.extend(await self._format_visual_element_markdown(visual_element, work_dir))
        
        lines.append("\\newpage")
        lines.append("")
        
        return lines

    async def _format_visual_element_markdown(
        self, 
        visual_element: Dict[str, Any], 
        work_dir: Path
    ) -> List[str]:
        """Format visual elements as markdown"""
        lines = []
        
        element_type = visual_element.get("type", "")
        
        if element_type == "diagram":
            # Mermaid diagrams - convert to image or include as code block
            lines.append("```mermaid")
            lines.append(visual_element.get("mermaid_code", ""))
            lines.append("```")
            lines.append("")
            
        elif element_type == "image":
            # Images
            image_path = visual_element.get("path", "")
            if image_path and Path(image_path).exists():
                # Copy image to work directory
                image_filename = Path(image_path).name
                work_image_path = work_dir / image_filename
                shutil.copy2(image_path, work_image_path)
                
                title = visual_element.get("title", "Figure")
                lines.append(f"![{title}]({image_filename}){{width={self.max_image_width}}}")
                lines.append("")
        
        return lines

    async def _format_qa_section_markdown(self, qa_section: Dict[str, Any]) -> List[str]:
        """Format Q&A section as markdown"""
        lines = []
        
        section_title = qa_section.get("section_title", "Questions")
        lines.append(f"## {section_title}")
        lines.append("")
        
        for i, question in enumerate(qa_section.get("questions", []), 1):
            lines.append(f"### Question {i}")
            lines.append("")
            lines.extend(self._format_question_markdown(question))
        
        return lines

    def _format_question_markdown(self, question: Dict[str, Any]) -> List[str]:
        """Format a single question as markdown"""
        lines = []
        
        question_type = question.get("type", "")
        
        if question_type == "multiple_choice":
            lines.append(question.get("question", ""))
            lines.append("")
            
            options = question.get("options", {})
            for key, value in options.items():
                lines.append(f"{key}. {value}")
            lines.append("")
            
            correct_answer = question.get("correct_answer", "")
            explanation = question.get("explanation", "")
            lines.append(f"**Answer: {correct_answer}**")
            lines.append("")
            if explanation:
                lines.append(f"*Explanation: {explanation}*")
                lines.append("")
                
        elif question_type == "short_answer":
            lines.append(question.get("question", ""))
            lines.append("")
            
            model_answer = question.get("model_answer", "")
            if model_answer:
                lines.append("**Model Answer:**")
                lines.append(model_answer)
                lines.append("")
                
        elif question_type == "essay":
            lines.append(question.get("question", ""))
            lines.append("")
            
            guidance = question.get("guidance", "")
            if guidance:
                lines.append("**Guidance:**")
                lines.append(guidance)
                lines.append("")
                
        elif question_type == "true_false":
            lines.append(question.get("statement", ""))
            lines.append("")
            
            correct_answer = question.get("correct_answer", False)
            lines.append(f"**Answer: {'True' if correct_answer else 'False'}**")
            lines.append("")
            
            explanation = question.get("explanation", "")
            if explanation:
                lines.append(f"*Explanation: {explanation}*")
                lines.append("")
        
        lines.append("---")
        lines.append("")
        
        return lines

    async def _prepare_assets(self, content_components: Dict[str, Any], work_dir: Path):
        """Prepare and copy assets to working directory"""
        # Copy images and diagrams
        for visual_element in content_components.get("visual_elements", []):
            element_path = visual_element.get("path", "")
            if element_path and Path(element_path).exists():
                filename = Path(element_path).name
                work_path = work_dir / filename
                if not work_path.exists():
                    shutil.copy2(element_path, work_path)

    async def _compile_pdf(
        self, 
        markdown_content: str, 
        work_dir: Path, 
        job_manifest: JobManifest
    ) -> Path:
        """Compile markdown to PDF using Pandoc"""
        
        # Write markdown to file
        markdown_file = work_dir / "study_guide.md"
        with open(markdown_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        # Prepare Pandoc command
        output_pdf = work_dir / "study_guide.pdf"
        template_path = self.template_dir / self.template_name
        
        pandoc_cmd = [
            self.pandoc_executable,
            str(markdown_file),
            "-o", str(output_pdf),
            "--pdf-engine", self.latex_engine,
            "--template", str(template_path),
            "--variable", f"geometry:margin={self.margin_size}",
            "--variable", f"fontsize:{self.font_size}",
            "--variable", f"linestretch:{self.line_spacing}",
            "--variable", f"papersize:{self.paper_size}",
        ]
        
        if self.include_toc:
            pandoc_cmd.append("--toc")
        
        # Attempt compilation with retries
        for attempt in range(self.max_compilation_attempts):
            try:
                self.logger.info(f"Pandoc compilation attempt {attempt + 1}")
                
                def _run_pandoc():
                    result = subprocess.run(
                        pandoc_cmd,
                        cwd=work_dir,
                        capture_output=True,
                        text=True,
                        timeout=300  # 5 minute timeout
                    )
                    return result
                
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, _run_pandoc)
                
                if result.returncode == 0:
                    self.logger.info("PDF compilation successful")
                    return output_pdf
                else:
                    self.logger.error(f"Pandoc compilation failed: {result.stderr}")
                    if attempt == self.max_compilation_attempts - 1:
                        raise AgentError(f"PDF compilation failed after {self.max_compilation_attempts} attempts: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                self.logger.error(f"Pandoc compilation timed out on attempt {attempt + 1}")
                if attempt == self.max_compilation_attempts - 1:
                    raise AgentError("PDF compilation timed out")
            except Exception as e:
                self.logger.error(f"Pandoc compilation error on attempt {attempt + 1}: {e}")
                if attempt == self.max_compilation_attempts - 1:
                    raise AgentError(f"PDF compilation failed: {e}")
        
        raise AgentError("PDF compilation failed after all attempts")

    async def _perform_quality_checks(self, pdf_path: Path) -> Dict[str, Any]:
        """Perform quality assurance checks on the generated PDF"""
        qa_results = {
            "file_exists": pdf_path.exists(),
            "file_size_bytes": 0,
            "page_count": 0,
            "quality_score": 0.0,
            "issues": []
        }
        
        if not pdf_path.exists():
            qa_results["issues"].append("PDF file not generated")
            return qa_results
        
        # Check file size
        file_size = pdf_path.stat().st_size
        qa_results["file_size_bytes"] = file_size
        
        if file_size < 1024:  # Less than 1KB
            qa_results["issues"].append("PDF file suspiciously small")
        elif file_size > 50 * 1024 * 1024:  # More than 50MB
            qa_results["issues"].append("PDF file very large")
        
        # Try to get page count using pdfinfo if available
        try:
            def _get_page_count():
                result = subprocess.run(
                    ["pdfinfo", str(pdf_path)],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if line.startswith('Pages:'):
                            return int(line.split(':')[1].strip())
                return 0
            
            loop = asyncio.get_event_loop()
            page_count = await loop.run_in_executor(None, _get_page_count)
            qa_results["page_count"] = page_count
            
            if page_count == 0:
                qa_results["issues"].append("Could not determine page count")
            elif page_count < 2:
                qa_results["issues"].append("PDF has very few pages")
                
        except Exception as e:
            self.logger.warning(f"Could not get PDF page count: {e}")
            qa_results["issues"].append("Could not verify page count")
        
        # Calculate quality score
        quality_score = 1.0
        if qa_results["issues"]:
            quality_score -= len(qa_results["issues"]) * 0.2
        
        qa_results["quality_score"] = max(0.0, quality_score)
        
        return qa_results

    async def _generate_compilation_stats(
        self, 
        content_components: Dict[str, Any], 
        qa_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate compilation statistics"""
        
        enhanced_content = content_components.get("enhanced_content", {})
        qa_content = content_components.get("qa_content", {})
        
        # Count content elements
        total_sections = len(enhanced_content.get("sections", []))
        total_subsections = sum(len(s.get("subsections", [])) for s in enhanced_content.get("sections", []))
        total_visual_elements = len(content_components.get("visual_elements", []))
        
        total_questions = 0
        if qa_content:
            for qa_section in qa_content.get("qa_sections", []):
                total_questions += len(qa_section.get("questions", []))
            
            comprehensive_exam = qa_content.get("comprehensive_exam", {})
            total_questions += len(comprehensive_exam.get("questions", []))
        
        return {
            "compilation_timestamp": job_manifest.updated_at.isoformat(),
            "content_statistics": {
                "total_sections": total_sections,
                "total_subsections": total_subsections,
                "total_visual_elements": total_visual_elements,
                "total_questions": total_questions
            },
            "pdf_statistics": {
                "file_size_mb": qa_results["file_size_bytes"] / (1024 * 1024),
                "page_count": qa_results["page_count"],
                "quality_score": qa_results["quality_score"]
            },
            "quality_issues": qa_results["issues"],
            "compilation_settings": {
                "latex_engine": self.latex_engine,
                "paper_size": self.paper_size,
                "font_size": self.font_size,
                "include_toc": self.include_toc
            }
        }

    def validate_inputs(self, job_manifest: JobManifest) -> bool:
        """Validate that all required content is available"""
        # Check that Q&A generation is completed
        qa_stage = job_manifest.processing_stages.get("qa_generation")
        if not qa_stage or qa_stage.state.value != "completed":
            self.logger.error("Q&A generation stage not completed")
            return False
        
        return True

    def estimate_processing_time(self, job_manifest: JobManifest) -> float:
        """Estimate processing time based on content complexity"""
        # Base time for compilation setup
        base_time = 60.0  # seconds
        
        # Additional time based on document count and complexity
        doc_count = len(job_manifest.documents)
        
        # PDF compilation time depends on content volume
        # Estimate based on document count and size
        total_size_mb = sum(doc.file_size for doc in job_manifest.documents) / (1024 * 1024)
        
        # Compilation time: ~30 seconds per MB of content + base processing
        compilation_time = total_size_mb * 30.0
        
        # Additional time for quality checks
        qa_time = 15.0
        
        return base_time + compilation_time + qa_time
