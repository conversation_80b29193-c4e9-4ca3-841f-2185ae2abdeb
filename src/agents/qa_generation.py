"""
Q&A Generation Agent

Generates comprehensive question and answer sets for study guides
with multiple question types, difficulty levels, and validation.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import random
import re

from ..core.base_agent import BaseAgent, AgentError
from ..models.job_manifest import JobManifest


class QAGenerationAgent(BaseAgent):
    """
    Agent responsible for:
    - Multi-type question generation (multiple choice, short answer, essay, true/false)
    - Difficulty level calibration (beginner, intermediate, advanced)
    - RAG-powered answer validation with confidence scoring
    - Distractor generation for multiple choice questions
    - Question diversity ensuring with topic coverage analysis
    """
    
    def __init__(self, agent_name: str, config: Dict[str, Any]):
        super().__init__(agent_name, config)
        
        # LLM configuration
        self.llm_model = config.get("llm_model", "llama3:8b-instruct")
        self.ollama_base_url = config.get("ollama_base_url", "http://localhost:11434")
        
        # Question generation configuration
        self.questions_per_section = config.get("questions_per_section", 5)
        self.question_types = config.get("question_types", [
            "multiple_choice", "short_answer", "essay", "true_false", "fill_blank"
        ])
        self.difficulty_levels = config.get("difficulty_levels", [
            "beginner", "intermediate", "advanced"
        ])
        
        # Question type distribution
        self.question_distribution = config.get("question_distribution", {
            "multiple_choice": 0.4,
            "short_answer": 0.3,
            "essay": 0.15,
            "true_false": 0.1,
            "fill_blank": 0.05
        })
        
        # Quality control
        self.enable_answer_validation = config.get("enable_answer_validation", True)
        self.min_distractors = config.get("min_distractors", 3)
        self.max_distractors = config.get("max_distractors", 4)
        self.answer_confidence_threshold = config.get("answer_confidence_threshold", 0.8)
        
        # Output configuration
        self.qa_output_dir = Path(config.get("qa_output_dir", "data/qa_content"))
        self.qa_output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.ollama_client = None
        self.vector_search = None
        self._init_ollama_client()

    def _init_ollama_client(self):
        """Initialize Ollama client for LLM inference"""
        try:
            import requests
            
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                self.ollama_client = requests.Session()
                self.logger.info(f"Ollama client initialized at {self.ollama_base_url}")
            else:
                raise AgentError(f"Ollama server not responding: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Ollama client: {e}")
            raise AgentError(f"Failed to initialize Ollama client: {e}")

    async def process(self, job_manifest: JobManifest) -> Dict[str, Any]:
        """
        Generate comprehensive Q&A content for the study guide.
        
        Args:
            job_manifest: Job containing enhanced content
            
        Returns:
            Dict with Q&A generation results
        """
        self.update_progress(5.0, "Starting Q&A generation")
        
        # Load enhanced content and setup vector search
        enhanced_content, collection_name = await self._load_content_and_setup_search(job_manifest)
        
        if not enhanced_content:
            raise AgentError("No enhanced content found for Q&A generation")
        
        self.update_progress(15.0, "Loaded enhanced content")
        
        # Generate Q&A for each section
        qa_content = {
            "job_id": job_manifest.job_id,
            "title": enhanced_content.get("title", "Study Guide"),
            "qa_sections": []
        }
        
        total_sections = len(enhanced_content.get("sections", []))
        all_questions = []
        
        for i, section in enumerate(enhanced_content.get("sections", [])):
            try:
                self.update_progress(
                    20.0 + (i / total_sections) * 70.0,
                    f"Generating Q&A for: {section.get('title', 'Unknown')}"
                )
                
                # Generate questions for this section
                section_qa = await self._generate_section_qa(section, collection_name)
                qa_content["qa_sections"].append(section_qa)
                
                # Collect all questions for diversity analysis
                all_questions.extend(section_qa.get("questions", []))
                
            except Exception as e:
                self.logger.error(f"Failed to generate Q&A for section {section.get('title')}: {e}")
                # Create error section
                error_section = {
                    "section_id": section.get("id", f"section_{i}"),
                    "section_title": section.get("title", "Unknown Section"),
                    "questions": [],
                    "status": "failed",
                    "error": str(e)
                }
                qa_content["qa_sections"].append(error_section)
        
        # Analyze question diversity and coverage
        self.update_progress(92.0, "Analyzing question diversity")
        diversity_analysis = await self._analyze_question_diversity(all_questions)
        
        # Generate comprehensive exam questions
        self.update_progress(95.0, "Generating comprehensive exam questions")
        comprehensive_exam = await self._generate_comprehensive_exam(enhanced_content, collection_name)
        qa_content["comprehensive_exam"] = comprehensive_exam
        
        # Save Q&A content
        qa_path = self.qa_output_dir / f"{job_manifest.job_id}_qa_content.json"
        with open(qa_path, 'w', encoding='utf-8') as f:
            json.dump(qa_content, f, indent=2, ensure_ascii=False)
        
        self.add_output_artifact(str(qa_path), "qa_content")
        
        # Generate Q&A statistics
        qa_stats = self._calculate_qa_statistics(qa_content)
        
        # Save Q&A report
        qa_report = {
            "job_id": job_manifest.job_id,
            "total_sections": total_sections,
            "total_questions": len(all_questions),
            "question_statistics": qa_stats,
            "diversity_analysis": diversity_analysis,
            "comprehensive_exam_questions": len(comprehensive_exam.get("questions", []))
        }
        
        report_path = self.qa_output_dir / f"{job_manifest.job_id}_qa_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(qa_report, f, indent=2, ensure_ascii=False)
        
        self.add_output_artifact(str(report_path), "qa_report")
        
        self.update_progress(100.0, "Q&A generation completed")
        
        return {
            "qa_summary": {
                "total_sections": total_sections,
                "total_questions": len(all_questions),
                "question_types": qa_stats["question_type_counts"],
                "difficulty_distribution": qa_stats["difficulty_distribution"],
                "comprehensive_exam_questions": len(comprehensive_exam.get("questions", []))
            },
            "output_artifacts": [str(qa_path), str(report_path)],
            "qa_content": qa_content
        }

    async def _load_content_and_setup_search(self, job_manifest: JobManifest) -> Tuple[Optional[Dict], Optional[str]]:
        """Load enhanced content and setup vector search"""
        enhanced_content = None
        collection_name = None
        
        # Load enhanced content from visual enhancement stage
        visual_stage = job_manifest.processing_stages.get("visual_analogy")
        if visual_stage and visual_stage.output_artifacts:
            for artifact_path in visual_stage.output_artifacts:
                if "enhanced_content.json" in artifact_path:
                    try:
                        with open(artifact_path, 'r', encoding='utf-8') as f:
                            enhanced_content = json.load(f)
                        break
                    except Exception as e:
                        self.logger.error(f"Failed to load enhanced content: {e}")
        
        # Setup vector search
        vectorization_stage = job_manifest.processing_stages.get("vectorization_indexing")
        if vectorization_stage and vectorization_stage.output_artifacts:
            for artifact_path in vectorization_stage.output_artifacts:
                if "vectorization_report.json" in artifact_path:
                    try:
                        with open(artifact_path, 'r', encoding='utf-8') as f:
                            vectorization_report = json.load(f)
                        
                        collection_name = vectorization_report.get("collection_name")
                        if collection_name:
                            from ..agents.vectorization_indexing import VectorizationIndexingAgent
                            self.vector_search = VectorizationIndexingAgent("vector_search", self.config)
                        break
                    except Exception as e:
                        self.logger.error(f"Failed to load vectorization report: {e}")
        
        return enhanced_content, collection_name

    async def _generate_section_qa(self, section: Dict[str, Any], collection_name: str) -> Dict[str, Any]:
        """Generate Q&A for a single section"""
        section_title = section.get("title", "")
        section_content = section.get("content", "")
        section_id = section.get("id", "")
        
        # Determine question distribution for this section
        num_questions = self.questions_per_section
        question_plan = self._plan_question_distribution(num_questions)
        
        questions = []
        
        for question_type, count in question_plan.items():
            for i in range(count):
                try:
                    # Generate question based on type
                    question = await self._generate_question(
                        question_type, section_title, section_content, collection_name
                    )
                    
                    if question:
                        questions.append(question)
                        
                except Exception as e:
                    self.logger.error(f"Failed to generate {question_type} question: {e}")
        
        # Generate subsection questions
        for subsection in section.get("subsections", []):
            if len(subsection.get("content", "")) > 200:  # Only for substantial subsections
                try:
                    subsection_question = await self._generate_question(
                        "short_answer", 
                        subsection.get("title", ""), 
                        subsection.get("content", ""), 
                        collection_name
                    )
                    if subsection_question:
                        questions.append(subsection_question)
                except Exception as e:
                    self.logger.error(f"Failed to generate subsection question: {e}")
        
        return {
            "section_id": section_id,
            "section_title": section_title,
            "questions": questions,
            "total_questions": len(questions),
            "status": "success"
        }

    def _plan_question_distribution(self, total_questions: int) -> Dict[str, int]:
        """Plan the distribution of question types"""
        distribution = {}
        remaining = total_questions
        
        for question_type, ratio in self.question_distribution.items():
            count = max(1, int(total_questions * ratio))
            if remaining > 0:
                actual_count = min(count, remaining)
                distribution[question_type] = actual_count
                remaining -= actual_count
        
        # Distribute any remaining questions
        while remaining > 0:
            for question_type in distribution:
                if remaining > 0:
                    distribution[question_type] += 1
                    remaining -= 1
        
        return distribution

    async def _generate_question(
        self, 
        question_type: str, 
        title: str, 
        content: str, 
        collection_name: str
    ) -> Optional[Dict[str, Any]]:
        """Generate a single question of the specified type"""
        
        # Select random difficulty level
        difficulty = random.choice(self.difficulty_levels)
        
        # Get relevant context if vector search is available
        context = ""
        if self.vector_search and collection_name:
            try:
                chunks = await self.vector_search.search_similar_chunks(
                    collection_name, title, n_results=3
                )
                context = "\n".join([chunk["document"][:300] for chunk in chunks])
            except Exception as e:
                self.logger.warning(f"Failed to get context for question generation: {e}")
        
        # Generate question based on type
        if question_type == "multiple_choice":
            return await self._generate_multiple_choice(title, content, context, difficulty)
        elif question_type == "short_answer":
            return await self._generate_short_answer(title, content, context, difficulty)
        elif question_type == "essay":
            return await self._generate_essay_question(title, content, context, difficulty)
        elif question_type == "true_false":
            return await self._generate_true_false(title, content, context, difficulty)
        elif question_type == "fill_blank":
            return await self._generate_fill_blank(title, content, context, difficulty)
        
        return None

    async def _generate_multiple_choice(
        self, 
        title: str, 
        content: str, 
        context: str, 
        difficulty: str
    ) -> Dict[str, Any]:
        """Generate a multiple choice question"""
        
        mc_prompt = f"""
Generate a {difficulty} level multiple choice question about: {title}

Content: {content[:800]}...
Additional context: {context[:400]}...

Requirements:
1. Create a clear, specific question
2. Provide one correct answer
3. Generate {self.max_distractors} plausible but incorrect distractors
4. Ensure distractors are believable but clearly wrong
5. Include explanation for the correct answer

Format as JSON:
{{
  "question": "Question text",
  "options": {{
    "A": "Option A text",
    "B": "Option B text",
    "C": "Option C text",
    "D": "Option D text"
  }},
  "correct_answer": "A",
  "explanation": "Explanation of why the answer is correct",
  "difficulty": "{difficulty}",
  "type": "multiple_choice"
}}
"""
        
        try:
            response = await self._call_llm(mc_prompt)
            question_data = json.loads(response)
            
            # Validate the question
            if self._validate_multiple_choice(question_data):
                return question_data
                
        except Exception as e:
            self.logger.error(f"Failed to generate multiple choice question: {e}")
        
        return None

    async def _generate_short_answer(
        self, 
        title: str, 
        content: str, 
        context: str, 
        difficulty: str
    ) -> Dict[str, Any]:
        """Generate a short answer question"""
        
        sa_prompt = f"""
Generate a {difficulty} level short answer question about: {title}

Content: {content[:800]}...
Additional context: {context[:400]}...

Requirements:
1. Create a question that requires 2-4 sentences to answer
2. Focus on understanding, not just memorization
3. Provide a model answer
4. Include key points that should be covered

Format as JSON:
{{
  "question": "Question text",
  "model_answer": "Complete model answer",
  "key_points": ["point 1", "point 2", "point 3"],
  "difficulty": "{difficulty}",
  "type": "short_answer"
}}
"""
        
        try:
            response = await self._call_llm(sa_prompt)
            question_data = json.loads(response)
            
            if question_data.get("question") and question_data.get("model_answer"):
                return question_data
                
        except Exception as e:
            self.logger.error(f"Failed to generate short answer question: {e}")
        
        return None

    async def _generate_essay_question(
        self, 
        title: str, 
        content: str, 
        context: str, 
        difficulty: str
    ) -> Dict[str, Any]:
        """Generate an essay question"""
        
        essay_prompt = f"""
Generate a {difficulty} level essay question about: {title}

Content: {content[:800]}...
Additional context: {context[:400]}...

Requirements:
1. Create a question requiring analytical thinking
2. Should require 1-2 paragraphs to answer thoroughly
3. Include guidance on what should be covered
4. Provide key themes and concepts to address

Format as JSON:
{{
  "question": "Essay question text",
  "guidance": "What students should address in their answer",
  "key_themes": ["theme 1", "theme 2", "theme 3"],
  "suggested_length": "1-2 paragraphs",
  "difficulty": "{difficulty}",
  "type": "essay"
}}
"""
        
        try:
            response = await self._call_llm(essay_prompt)
            question_data = json.loads(response)
            
            if question_data.get("question") and question_data.get("key_themes"):
                return question_data
                
        except Exception as e:
            self.logger.error(f"Failed to generate essay question: {e}")
        
        return None

    async def _generate_true_false(
        self, 
        title: str, 
        content: str, 
        context: str, 
        difficulty: str
    ) -> Dict[str, Any]:
        """Generate a true/false question"""
        
        tf_prompt = f"""
Generate a {difficulty} level true/false question about: {title}

Content: {content[:800]}...
Additional context: {context[:400]}...

Requirements:
1. Create a clear statement that is definitively true or false
2. Avoid ambiguous or trick statements
3. Provide explanation for why the statement is true or false
4. Base on factual content from the material

Format as JSON:
{{
  "statement": "Statement to evaluate",
  "correct_answer": true,
  "explanation": "Explanation of why the statement is true/false",
  "difficulty": "{difficulty}",
  "type": "true_false"
}}
"""
        
        try:
            response = await self._call_llm(tf_prompt)
            question_data = json.loads(response)
            
            if (question_data.get("statement") and 
                question_data.get("correct_answer") is not None):
                return question_data
                
        except Exception as e:
            self.logger.error(f"Failed to generate true/false question: {e}")
        
        return None

    async def _generate_fill_blank(
        self, 
        title: str, 
        content: str, 
        context: str, 
        difficulty: str
    ) -> Dict[str, Any]:
        """Generate a fill-in-the-blank question"""
        
        fb_prompt = f"""
Generate a {difficulty} level fill-in-the-blank question about: {title}

Content: {content[:800]}...
Additional context: {context[:400]}...

Requirements:
1. Create a sentence with 1-2 key terms removed
2. The blanks should test important concepts
3. Provide the correct answers
4. Include alternative acceptable answers if applicable

Format as JSON:
{{
  "sentence": "The _____ is responsible for _____ in the system.",
  "correct_answers": ["answer1", "answer2"],
  "alternative_answers": ["alt1", "alt2"],
  "difficulty": "{difficulty}",
  "type": "fill_blank"
}}
"""
        
        try:
            response = await self._call_llm(fb_prompt)
            question_data = json.loads(response)
            
            if (question_data.get("sentence") and 
                question_data.get("correct_answers")):
                return question_data
                
        except Exception as e:
            self.logger.error(f"Failed to generate fill-in-the-blank question: {e}")
        
        return None

    def _validate_multiple_choice(self, question_data: Dict[str, Any]) -> bool:
        """Validate multiple choice question structure"""
        required_fields = ["question", "options", "correct_answer", "explanation"]
        
        for field in required_fields:
            if field not in question_data:
                return False
        
        options = question_data.get("options", {})
        if len(options) < 3:  # At least 3 options
            return False
        
        correct_answer = question_data.get("correct_answer")
        if correct_answer not in options:
            return False
        
        return True

    async def _generate_comprehensive_exam(
        self, 
        enhanced_content: Dict[str, Any], 
        collection_name: str
    ) -> Dict[str, Any]:
        """Generate comprehensive exam questions covering all sections"""
        
        exam_questions = []
        sections = enhanced_content.get("sections", [])
        
        # Generate cross-sectional questions
        if len(sections) >= 2:
            for i in range(min(3, len(sections) - 1)):  # Generate up to 3 comprehensive questions
                try:
                    section1 = sections[i]
                    section2 = sections[i + 1]
                    
                    comprehensive_question = await self._generate_cross_sectional_question(
                        section1, section2, collection_name
                    )
                    
                    if comprehensive_question:
                        exam_questions.append(comprehensive_question)
                        
                except Exception as e:
                    self.logger.error(f"Failed to generate comprehensive question: {e}")
        
        # Generate high-level synthesis questions
        synthesis_question = await self._generate_synthesis_question(enhanced_content, collection_name)
        if synthesis_question:
            exam_questions.append(synthesis_question)
        
        return {
            "title": "Comprehensive Exam",
            "description": "Questions that test understanding across multiple sections",
            "questions": exam_questions,
            "total_questions": len(exam_questions)
        }

    async def _generate_cross_sectional_question(
        self, 
        section1: Dict[str, Any], 
        section2: Dict[str, Any], 
        collection_name: str
    ) -> Optional[Dict[str, Any]]:
        """Generate a question that spans multiple sections"""
        
        title1 = section1.get("title", "")
        title2 = section2.get("title", "")
        content1 = section1.get("content", "")[:400]
        content2 = section2.get("content", "")[:400]
        
        cross_prompt = f"""
Generate an essay question that connects concepts from these two sections:

Section 1: {title1}
Content: {content1}...

Section 2: {title2}
Content: {content2}...

Requirements:
1. Create a question that requires understanding of both sections
2. Focus on relationships, comparisons, or applications
3. Should require analytical thinking
4. Provide guidance on what to address

Format as JSON:
{{
  "question": "Cross-sectional question text",
  "guidance": "What students should address",
  "sections_covered": ["{title1}", "{title2}"],
  "difficulty": "advanced",
  "type": "essay"
}}
"""
        
        try:
            response = await self._call_llm(cross_prompt)
            return json.loads(response)
        except Exception as e:
            self.logger.error(f"Failed to generate cross-sectional question: {e}")
            return None

    async def _generate_synthesis_question(
        self, 
        enhanced_content: Dict[str, Any], 
        collection_name: str
    ) -> Optional[Dict[str, Any]]:
        """Generate a high-level synthesis question"""
        
        title = enhanced_content.get("title", "Study Guide")
        overview = enhanced_content.get("overview", "")
        section_titles = [s.get("title", "") for s in enhanced_content.get("sections", [])]
        
        synthesis_prompt = f"""
Generate a comprehensive synthesis question for this study guide:

Title: {title}
Overview: {overview}
Sections: {', '.join(section_titles)}

Requirements:
1. Create a question that requires synthesis of the entire study guide
2. Should test deep understanding and application
3. Encourage critical thinking and analysis
4. Suitable as a final exam question

Format as JSON:
{{
  "question": "Synthesis question text",
  "guidance": "Comprehensive guidance for answering",
  "key_concepts": ["concept1", "concept2", "concept3"],
  "difficulty": "advanced",
  "type": "essay"
}}
"""
        
        try:
            response = await self._call_llm(synthesis_prompt)
            return json.loads(response)
        except Exception as e:
            self.logger.error(f"Failed to generate synthesis question: {e}")
            return None

    async def _analyze_question_diversity(self, questions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze diversity and coverage of generated questions"""
        
        type_counts = {}
        difficulty_counts = {}
        topics_covered = set()
        
        for question in questions:
            # Count question types
            q_type = question.get("type", "unknown")
            type_counts[q_type] = type_counts.get(q_type, 0) + 1
            
            # Count difficulty levels
            difficulty = question.get("difficulty", "unknown")
            difficulty_counts[difficulty] = difficulty_counts.get(difficulty, 0) + 1
            
            # Extract topics (simple keyword extraction)
            question_text = question.get("question", "")
            words = re.findall(r'\b[A-Z][a-z]+\b', question_text)
            topics_covered.update(words[:3])  # Add up to 3 capitalized words as topics
        
        return {
            "question_type_distribution": type_counts,
            "difficulty_distribution": difficulty_counts,
            "topics_covered": list(topics_covered),
            "total_unique_topics": len(topics_covered),
            "diversity_score": len(topics_covered) / max(len(questions), 1)
        }

    async def _call_llm(self, prompt: str) -> str:
        """Call Ollama LLM with the given prompt"""
        def _make_request():
            payload = {
                "model": self.llm_model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.4,
                    "top_p": 0.9,
                    "max_tokens": 3000
                }
            }
            
            response = self.ollama_client.post(
                f"{self.ollama_base_url}/api/generate",
                json=payload,
                timeout=180
            )
            
            if response.status_code == 200:
                return response.json().get("response", "")
            else:
                raise AgentError(f"LLM request failed: {response.status_code}")
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _make_request)

    def _calculate_qa_statistics(self, qa_content: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate statistics about generated Q&A content"""
        
        all_questions = []
        for section in qa_content.get("qa_sections", []):
            all_questions.extend(section.get("questions", []))
        
        # Add comprehensive exam questions
        comprehensive_exam = qa_content.get("comprehensive_exam", {})
        all_questions.extend(comprehensive_exam.get("questions", []))
        
        # Count by type
        type_counts = {}
        difficulty_counts = {}
        
        for question in all_questions:
            q_type = question.get("type", "unknown")
            type_counts[q_type] = type_counts.get(q_type, 0) + 1
            
            difficulty = question.get("difficulty", "unknown")
            difficulty_counts[difficulty] = difficulty_counts.get(difficulty, 0) + 1
        
        return {
            "total_questions": len(all_questions),
            "question_type_counts": type_counts,
            "difficulty_distribution": difficulty_counts,
            "sections_with_qa": len([s for s in qa_content.get("qa_sections", []) if s.get("status") == "success"]),
            "average_questions_per_section": len(all_questions) / max(len(qa_content.get("qa_sections", [])), 1)
        }

    def validate_inputs(self, job_manifest: JobManifest) -> bool:
        """Validate that enhanced content is available"""
        visual_stage = job_manifest.processing_stages.get("visual_analogy")
        if not visual_stage or visual_stage.state.value != "completed":
            self.logger.error("Visual enhancement stage not completed")
            return False
        
        return True

    def estimate_processing_time(self, job_manifest: JobManifest) -> float:
        """Estimate processing time based on content complexity"""
        # Base time for setup
        base_time = 30.0  # seconds
        
        # Additional time based on document count and complexity
        doc_count = len(job_manifest.documents)
        
        # Q&A generation is moderately time-consuming
        # Estimate based on number of sections (roughly doc_count * 2)
        estimated_sections = doc_count * 2
        processing_time = estimated_sections * 20.0  # 20 seconds per section
        
        return base_time + processing_time
