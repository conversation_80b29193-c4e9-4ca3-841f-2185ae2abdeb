Metadata-Version: 2.4
Name: studyguide-generator
Version: 1.0.0
Summary: Multi-Agent Study Guide Generator - Privacy-First Academic Document Processing
Home-page: https://github.com/studyguide-generator/studyguide-generator
Author: Study Guide Generator Team
Author-email: <EMAIL>
License: MIT
Project-URL: Documentation, https://studyguide-generator.readthedocs.io/
Project-URL: Source, https://github.com/studyguide-generator/studyguide-generator
Project-URL: Tracker, https://github.com/studyguide-generator/studyguide-generator/issues
Project-URL: Changelog, https://github.com/studyguide-generator/studyguide-generator/blob/main/CHANGELOG.md
Keywords: study-guide,document-processing,ai,machine-learning,education,pdf-generation,privacy-first,local-processing,multi-agent,academic
Platform: any
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Education
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Text Processing :: Markup :: LaTeX
Classifier: Topic :: Utilities
Requires-Python: >=3.9
Description-Content-Type: text/markdown
Requires-Dist: asyncio-mqtt>=0.11.0
Requires-Dist: aiofiles>=23.0.0
Requires-Dist: aiohttp>=3.8.0
Requires-Dist: aiohttp-cors>=0.7.0
Requires-Dist: aiosqlite>=0.19.0
Requires-Dist: click>=8.0.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: pyyaml>=6.0
Requires-Dist: toml>=0.10.0
Requires-Dist: PyMuPDF>=1.23.0
Requires-Dist: python-docx>=0.8.11
Requires-Dist: Pillow>=10.0.0
Requires-Dist: paddleocr>=2.7.0
Requires-Dist: sentence-transformers>=2.2.0
Requires-Dist: chromadb>=0.4.0
Requires-Dist: transformers>=4.30.0
Requires-Dist: torch>=2.0.0
Requires-Dist: diffusers>=0.21.0
Requires-Dist: psutil>=5.9.0
Requires-Dist: GPUtil>=1.4.0
Requires-Dist: requests>=2.31.0
Requires-Dist: httpx>=0.24.0
Requires-Dist: pandas>=2.0.0
Requires-Dist: numpy>=1.24.0
Requires-Dist: weasyprint>=59.0
Requires-Dist: colorlog>=6.7.0
Requires-Dist: tqdm>=4.65.0
Requires-Dist: python-dateutil>=2.8.0
Requires-Dist: cryptography>=41.0.0
Requires-Dist: python-magic>=0.4.27
Provides-Extra: gpu
Requires-Dist: torch[cuda]>=2.0.0; extra == "gpu"
Requires-Dist: diffusers[torch]>=0.21.0; extra == "gpu"
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Requires-Dist: pre-commit>=3.0.0; extra == "dev"
Provides-Extra: monitoring
Requires-Dist: prometheus-client>=0.17.0; extra == "monitoring"
Requires-Dist: grafana-api>=1.0.3; extra == "monitoring"
Provides-Extra: cloud
Requires-Dist: boto3>=1.28.0; extra == "cloud"
Requires-Dist: google-cloud-storage>=2.10.0; extra == "cloud"
Requires-Dist: azure-storage-blob>=12.17.0; extra == "cloud"
Provides-Extra: all
Requires-Dist: pytest>=7.0.0; extra == "all"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "all"
Requires-Dist: mypy>=1.0.0; extra == "all"
Requires-Dist: boto3>=1.28.0; extra == "all"
Requires-Dist: azure-storage-blob>=12.17.0; extra == "all"
Requires-Dist: torch[cuda]>=2.0.0; extra == "all"
Requires-Dist: prometheus-client>=0.17.0; extra == "all"
Requires-Dist: flake8>=6.0.0; extra == "all"
Requires-Dist: pre-commit>=3.0.0; extra == "all"
Requires-Dist: pytest-cov>=4.0.0; extra == "all"
Requires-Dist: diffusers[torch]>=0.21.0; extra == "all"
Requires-Dist: google-cloud-storage>=2.10.0; extra == "all"
Requires-Dist: black>=23.0.0; extra == "all"
Requires-Dist: grafana-api>=1.0.3; extra == "all"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: platform
Dynamic: project-url
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# Local Multi-Agent Study Guide Generator

A fully local, privacy-first system for generating comprehensive study guides from academic documents using a multi-agent architecture.

## 🎯 Overview

This system processes uploaded academic documents (PDFs, images, DOCX) and generates complete study guides with Q&A sections, visual aids, and structured content - all running locally with zero external API calls.

## 🏗️ Architecture

### Multi-Agent System (9 Specialized Agents)

1. **Ingestion & Triage Agent** - File validation and preprocessing
2. **Content Extraction Agent** - OCR and text extraction
3. **Semantic Chunking Agent** - Intelligent text segmentation
4. **Vectorization & Indexing Agent** - Vector database management
5. **Outline Generation Agent** - Structure and hierarchy creation
6. **Knowledge Synthesizer Agent** - Content generation with RAG
7. **Visual & Analogy Agent** - Diagrams and visual aids
8. **Q&A Generation Agent** - Question and answer creation
9. **Final Compiler Agent** - PDF compilation and formatting

### Core Components

- **Job Orchestrator** - Manages agent lifecycle and state transitions
- **Job Manifest System** - Persistent state tracking with atomic operations
- **Vector Database** - ChromaDB for semantic search and retrieval
- **Resource Manager** - Hardware-aware resource allocation
- **Monitoring System** - Comprehensive logging and metrics

## 🛠️ Technology Stack

- **Local LLM**: Ollama (llama3:8b-instruct, llama3:70b-instruct)
- **Vector DB**: ChromaDB v0.4.15+
- **OCR**: PaddleOCR v2.7.0+
- **Document Processing**: PyMuPDF, python-docx, Pillow
- **Embeddings**: sentence-transformers (bge-small-en-v1.5, nomic-embed-text)
- **Image Generation**: Stable Diffusion XL (local)
- **PDF Generation**: Pandoc + XeLaTeX
- **Database**: SQLite with WAL mode
- **API**: FastAPI for job management

## 📋 Requirements

### Hardware Requirements

**Minimum (Consumer)**:
- CPU: 8 cores, 16GB RAM
- GPU: RTX 3060 (8GB VRAM) or equivalent
- Storage: 100GB free space

**Recommended (Professional)**:
- CPU: 16+ cores, 32GB+ RAM
- GPU: RTX 4090 (24GB VRAM) or A6000
- Storage: 500GB+ NVMe SSD

### Software Requirements

- Python 3.10+
- Docker (optional)
- CUDA 11.8+ (for GPU acceleration)
- Ollama v0.1.32+

## 🚀 Quick Start

```bash
# Clone and setup
git clone <repository>
cd local-study-guide-generator
pip install -r requirements.txt

# Install models
ollama pull llama3:8b-instruct
ollama pull llama3:70b-instruct

# Initialize database
python -m src.database.init_db

# Start the system
python -m src.main --config config/default.yaml

# Process a document
python -m src.cli submit-job --file document.pdf --output study_guide.pdf
```

## 📁 Project Structure

```
local-study-guide-generator/
├── src/
│   ├── agents/           # Agent implementations
│   │   ├── ingestion_triage.py
│   │   ├── content_extraction.py
│   │   ├── semantic_chunking.py
│   │   ├── vectorization_indexing.py
│   │   ├── outline_generation.py
│   │   ├── knowledge_synthesizer.py
│   │   ├── visual_analogy.py
│   │   ├── qa_generation.py
│   │   └── final_compiler.py
│   ├── core/            # Core system components
│   │   ├── orchestrator.py
│   │   ├── resource_manager.py
│   │   └── base_agent.py
│   ├── database/        # Database models and migrations
│   │   └── models.py
│   ├── models/          # Data models and schemas
│   │   └── job_manifest.py
│   ├── config/          # Configuration management
│   │   ├── system_config.py
│   │   ├── hardware_profiles.py
│   │   └── config_manager.py
│   ├── cli/             # Command-line interface
│   │   └── main.py
│   ├── monitoring/      # Monitoring and observability
│   │   ├── metrics_collector.py
│   │   └── dashboard.py
│   └── utils/           # Utility functions
├── tests/               # Test suites
│   ├── test_job_manifest.py
│   ├── test_orchestrator.py
│   └── benchmarks/
│       └── performance_tests.py
├── config/              # Configuration files
├── docs/                # Documentation
├── scripts/             # Setup and utility scripts
├── requirements.txt     # Python dependencies
└── setup.py            # Package setup
```

## 🚀 Installation & Setup

### Prerequisites

**Hardware Requirements:**
- **Minimum**: 8GB RAM, 4-core CPU, 50GB storage
- **Recommended**: 16GB+ RAM, 8-core CPU, 100GB+ SSD storage
- **Optimal**: 32GB+ RAM, 12+ core CPU, 200GB+ NVMe SSD, dedicated GPU

**Software Dependencies:**
- Python 3.9+ (3.11+ recommended)
- Git
- Pandoc (for PDF generation)
- LaTeX distribution (TeX Live, MiKTeX, or MacTeX)
- Ollama (for local LLM serving)

### Quick Start

1. **Clone the repository:**
```bash
git clone https://github.com/studyguide-generator/studyguide-generator.git
cd studyguide-generator
```

2. **Install dependencies:**
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install package
pip install -e .

# Or install from requirements.txt
pip install -r requirements.txt
```

3. **Install system dependencies:**
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install pandoc texlive-full

# macOS
brew install pandoc
brew install --cask mactex

# Windows
# Download and install Pandoc from https://pandoc.org/installing.html
# Download and install MiKTeX from https://miktex.org/
```

4. **Install and configure Ollama:**
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull required models
ollama pull llama3:8b-instruct
ollama pull llama3:70b-instruct  # Optional, for high-quality mode
ollama pull nomic-embed-text
```

5. **Initialize the system:**
```bash
# Initialize configuration
studyguide init

# Verify installation
studyguide status
```

### Usage Examples

**Basic Usage:**
```bash
# Process documents and generate study guide
studyguide create --title "Biology Study Guide" --input docs/ --output study_guide.pdf

# Monitor job progress
studyguide jobs watch <job-id>

# View system status
studyguide status
```

**Advanced Usage:**
```bash
# High-quality mode (requires more resources)
studyguide create --title "Advanced Physics" --input physics_docs/ --quality high

# Batch processing
studyguide create --title "Multiple Subjects" --input subject1/ subject2/ subject3/

# Custom configuration
studyguide create --config custom_config.yaml --input docs/
```

## 🔧 Configuration

The system uses YAML configuration files with environment-specific overrides:

- `config/default.yaml` - Base configuration
- `config/development.yaml` - Development overrides
- `config/production.yaml` - Production settings

## 📊 Monitoring

- Real-time job progress tracking
- Resource usage monitoring
- Performance metrics collection
- Structured logging with correlation IDs
- Health check endpoints

## 🧪 Testing & Development

### Running Tests

The system includes comprehensive test suites for all components:

```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/test_job_manifest.py          # Core models
pytest tests/test_orchestrator.py          # Job orchestration
pytest tests/benchmarks/                   # Performance tests

# Run with coverage
pytest --cov=src --cov-report=html

# Run performance benchmarks
pytest -m benchmark tests/benchmarks/performance_tests.py
```

### Performance Benchmarking

Run comprehensive performance benchmarks to evaluate system performance:

```bash
# Run all benchmarks
python tests/benchmarks/performance_tests.py

# Run specific benchmark
python -c "
import asyncio
from tests.benchmarks.performance_tests import PerformanceBenchmark

async def main():
    benchmark = PerformanceBenchmark()
    await benchmark.benchmark_single_document()
    benchmark.print_summary()

asyncio.run(main())
"
```

### Development Setup

For development and contribution:

```bash
# Install development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install

# Run code formatting
black src/ tests/
flake8 src/ tests/

# Run type checking
mypy src/
```

### Monitoring & Observability

The system includes comprehensive monitoring capabilities:

```bash
# Start monitoring dashboard
studyguide dashboard --host 0.0.0.0 --port 8080

# View metrics
studyguide monitor

# Export metrics
studyguide metrics export --format json --hours 24
```

Access the web dashboard at `http://localhost:8080` for real-time monitoring.

## 📖 Documentation

- [Architecture Guide](docs/architecture.md)
- [Agent Specifications](docs/agents.md)
- [Configuration Reference](docs/configuration.md)
- [API Documentation](docs/api.md)
- [Deployment Guide](docs/deployment.md)
- [Troubleshooting](docs/troubleshooting.md)

## 🤝 Contributing

Please read our [Contributing Guide](CONTRIBUTING.md) for development setup and guidelines.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔒 Privacy & Security

- **100% Local Processing** - No data leaves your machine
- **Zero External Dependencies** - No internet required after setup
- **Secure by Design** - All processing happens in isolated containers
- **Data Encryption** - Optional encryption for sensitive documents
