asyncio-mqtt>=0.11.0
aiofiles>=23.0.0
aiohttp>=3.8.0
aiohttp-cors>=0.7.0
aiosqlite>=0.19.0
click>=8.0.0
pydantic>=2.0.0
pyyaml>=6.0
toml>=0.10.0
PyMuPDF>=1.23.0
python-docx>=0.8.11
Pillow>=10.0.0
paddleocr>=2.7.0
sentence-transformers>=2.2.0
chromadb>=0.4.0
transformers>=4.30.0
torch>=2.0.0
diffusers>=0.21.0
psutil>=5.9.0
GPUtil>=1.4.0
requests>=2.31.0
httpx>=0.24.0
pandas>=2.0.0
numpy>=1.24.0
weasyprint>=59.0
colorlog>=6.7.0
tqdm>=4.65.0
python-dateutil>=2.8.0
cryptography>=41.0.0
python-magic>=0.4.27

[all]
pytest>=7.0.0
pytest-asyncio>=0.21.0
mypy>=1.0.0
boto3>=1.28.0
azure-storage-blob>=12.17.0
torch[cuda]>=2.0.0
prometheus-client>=0.17.0
flake8>=6.0.0
pre-commit>=3.0.0
pytest-cov>=4.0.0
diffusers[torch]>=0.21.0
google-cloud-storage>=2.10.0
black>=23.0.0
grafana-api>=1.0.3

[cloud]
boto3>=1.28.0
google-cloud-storage>=2.10.0
azure-storage-blob>=12.17.0

[dev]
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0
pre-commit>=3.0.0

[gpu]
torch[cuda]>=2.0.0
diffusers[torch]>=0.21.0

[monitoring]
prometheus-client>=0.17.0
grafana-api>=1.0.3
