"""
Job Manifest Schema and Models

Defines the comprehensive job manifest structure for tracking processing state,
progress, and metadata throughout the multi-agent pipeline.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator
import uuid


class JobState(str, Enum):
    """Job processing states with atomic transitions"""
    PENDING = "pending"
    VALIDATING = "validating"
    EXTRACTING = "extracting"
    CHUNKING = "chunking"
    VECTORIZING = "vectorizing"
    OUTLINING = "outlining"
    SYNTHESIZING = "synthesizing"
    ENHANCING = "enhancing"
    GENERATING_QA = "generating_qa"
    COMPILING = "compiling"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"
    CANCELLED = "cancelled"


class AgentState(str, Enum):
    """Individual agent processing states"""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"


class Priority(str, Enum):
    """Job priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class DocumentMetadata(BaseModel):
    """Metadata for uploaded documents"""
    file_path: str
    original_filename: str
    file_size: int
    checksum_sha256: str
    mime_type: str
    page_count: Optional[int] = None
    language: str = "en"
    upload_timestamp: datetime
    validation_status: str = "pending"
    extraction_method: Optional[str] = None


class ProcessingStage(BaseModel):
    """Individual processing stage tracking"""
    agent_name: str
    state: AgentState = AgentState.NOT_STARTED
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    progress_percentage: float = 0.0
    retry_count: int = 0
    max_retries: int = 3
    error_message: Optional[str] = None
    output_artifacts: List[str] = Field(default_factory=list)
    resource_usage: Dict[str, Any] = Field(default_factory=dict)


class ErrorRecord(BaseModel):
    """Error tracking and recovery information"""
    timestamp: datetime
    agent_name: str
    error_type: str
    error_message: str
    stack_trace: Optional[str] = None
    recovery_action: Optional[str] = None
    retry_attempt: int = 0
    correlation_id: str


class ResourceMetrics(BaseModel):
    """Resource usage tracking"""
    cpu_usage_percent: float = 0.0
    memory_usage_mb: float = 0.0
    gpu_memory_usage_mb: float = 0.0
    disk_usage_mb: float = 0.0
    processing_time_seconds: float = 0.0
    tokens_processed: int = 0
    embeddings_generated: int = 0


class JobConfiguration(BaseModel):
    """Job-specific configuration overrides"""
    model_preferences: Dict[str, str] = Field(default_factory=dict)
    resource_limits: Dict[str, Union[int, float]] = Field(default_factory=dict)
    processing_options: Dict[str, Any] = Field(default_factory=dict)
    output_format: str = "pdf"
    include_visual_aids: bool = True
    include_qa_section: bool = True
    difficulty_level: str = "intermediate"
    language: str = "en"


class JobManifest(BaseModel):
    """Complete job manifest with all tracking information"""
    
    # Core identification
    job_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    correlation_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    priority: Priority = Priority.NORMAL
    user_id: Optional[str] = None
    
    # Job state
    current_state: JobState = JobState.PENDING
    previous_state: Optional[JobState] = None
    state_history: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Documents
    documents: List[DocumentMetadata] = Field(default_factory=list)
    
    # Processing stages
    processing_stages: Dict[str, ProcessingStage] = Field(default_factory=dict)
    
    # Progress tracking
    overall_progress: float = 0.0
    estimated_completion_time: Optional[datetime] = None
    
    # Error handling
    errors: List[ErrorRecord] = Field(default_factory=list)
    failure_count: int = 0
    max_failures: int = 5
    
    # Resource tracking
    resource_metrics: ResourceMetrics = Field(default_factory=ResourceMetrics)
    
    # Configuration
    configuration: JobConfiguration = Field(default_factory=JobConfiguration)
    
    # Output
    output_artifacts: List[str] = Field(default_factory=list)
    final_output_path: Optional[str] = None
    
    # Backup and recovery
    checkpoint_data: Dict[str, Any] = Field(default_factory=dict)
    backup_paths: List[str] = Field(default_factory=list)

    @validator('updated_at', pre=True, always=True)
    def set_updated_at(cls, v):
        return datetime.utcnow()

    def update_state(self, new_state: JobState, agent_name: Optional[str] = None):
        """Update job state with history tracking"""
        self.state_history.append({
            "from_state": self.current_state,
            "to_state": new_state,
            "timestamp": datetime.utcnow(),
            "agent": agent_name
        })
        self.previous_state = self.current_state
        self.current_state = new_state
        self.updated_at = datetime.utcnow()

    def add_error(self, agent_name: str, error_type: str, error_message: str, 
                  stack_trace: Optional[str] = None):
        """Add error record with correlation tracking"""
        error = ErrorRecord(
            timestamp=datetime.utcnow(),
            agent_name=agent_name,
            error_type=error_type,
            error_message=error_message,
            stack_trace=stack_trace,
            correlation_id=self.correlation_id
        )
        self.errors.append(error)
        self.failure_count += 1

    def update_progress(self):
        """Calculate overall progress from individual stages"""
        if not self.processing_stages:
            self.overall_progress = 0.0
            return
        
        total_progress = sum(stage.progress_percentage for stage in self.processing_stages.values())
        self.overall_progress = total_progress / len(self.processing_stages)

    def can_retry(self) -> bool:
        """Check if job can be retried based on failure limits"""
        return self.failure_count < self.max_failures

    def get_active_stage(self) -> Optional[ProcessingStage]:
        """Get currently active processing stage"""
        for stage in self.processing_stages.values():
            if stage.state == AgentState.RUNNING:
                return stage
        return None

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# JSON Schema for validation
JOB_MANIFEST_SCHEMA = {
    "type": "object",
    "properties": {
        "job_id": {"type": "string", "format": "uuid"},
        "correlation_id": {"type": "string", "format": "uuid"},
        "created_at": {"type": "string", "format": "date-time"},
        "updated_at": {"type": "string", "format": "date-time"},
        "priority": {"type": "string", "enum": ["low", "normal", "high", "urgent"]},
        "current_state": {"type": "string", "enum": list(JobState)},
        "documents": {
            "type": "array",
            "items": {
                "type": "object",
                "required": ["file_path", "original_filename", "file_size", "checksum_sha256"]
            }
        },
        "processing_stages": {
            "type": "object",
            "additionalProperties": {
                "type": "object",
                "properties": {
                    "agent_name": {"type": "string"},
                    "state": {"type": "string", "enum": list(AgentState)},
                    "progress_percentage": {"type": "number", "minimum": 0, "maximum": 100}
                }
            }
        }
    },
    "required": ["job_id", "correlation_id", "current_state"]
}
