"""
Metrics Collection System

Comprehensive metrics collection for system performance monitoring,
job tracking, and operational observability.
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json
import threading
from collections import defaultdict, deque

from ..core.resource_manager import get_resource_manager, ResourceMetrics
from ..database.models import JobRepository
from ..models.job_manifest import JobState


@dataclass
class JobMetrics:
    """Job-specific metrics"""
    job_id: str
    title: str
    state: str
    progress_percentage: float
    processing_time_seconds: float
    documents_count: int
    total_file_size_mb: float
    current_agent: str
    error_count: int
    created_at: datetime
    updated_at: datetime


@dataclass
class AgentMetrics:
    """Agent-specific performance metrics"""
    agent_name: str
    total_jobs_processed: int
    successful_jobs: int
    failed_jobs: int
    average_processing_time: float
    total_processing_time: float
    current_active_jobs: int
    error_rate: float
    last_activity: datetime


@dataclass
class SystemMetrics:
    """System-wide performance metrics"""
    timestamp: datetime
    uptime_seconds: float
    total_jobs_created: int
    total_jobs_completed: int
    total_jobs_failed: int
    active_jobs: int
    queued_jobs: int
    average_job_completion_time: float
    system_load_average: float
    memory_usage_percent: float
    cpu_usage_percent: float
    gpu_usage_percent: float
    disk_usage_percent: float
    error_rate_percent: float


class MetricsCollector:
    """
    Comprehensive metrics collection system for monitoring and observability.
    
    Features:
    - Real-time system resource monitoring
    - Job lifecycle tracking and performance metrics
    - Agent performance analysis
    - Historical data retention and aggregation
    - Alert generation for anomalies
    - Export capabilities for external monitoring systems
    """
    
    def __init__(self, retention_hours: int = 168):  # 1 week default
        self.logger = logging.getLogger(__name__)
        self.retention_hours = retention_hours
        
        # Metrics storage
        self.resource_metrics: deque = deque(maxlen=10080)  # 1 week at 1-minute intervals
        self.job_metrics: Dict[str, JobMetrics] = {}
        self.agent_metrics: Dict[str, AgentMetrics] = {}
        self.system_metrics: deque = deque(maxlen=1440)  # 1 day at 1-minute intervals
        
        # Performance tracking
        self.job_completion_times: deque = deque(maxlen=1000)
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.alert_thresholds = self._initialize_alert_thresholds()
        
        # Collection state
        self.collection_active = False
        self.collection_task: Optional[asyncio.Task] = None
        self.collection_interval = 60.0  # seconds
        
        # Start time for uptime calculation
        self.start_time = time.time()
        
        # Components
        self.resource_manager = get_resource_manager()
        self.job_repository = JobRepository()
        
        # Alert callbacks
        self.alert_callbacks: List[Callable[[str, Dict[str, Any]], None]] = []

    def _initialize_alert_thresholds(self) -> Dict[str, Any]:
        """Initialize alert thresholds"""
        return {
            "memory_usage_critical": 90.0,
            "memory_usage_warning": 80.0,
            "cpu_usage_critical": 95.0,
            "cpu_usage_warning": 85.0,
            "disk_usage_critical": 95.0,
            "disk_usage_warning": 90.0,
            "error_rate_critical": 10.0,
            "error_rate_warning": 5.0,
            "job_failure_rate_critical": 20.0,
            "job_failure_rate_warning": 10.0,
            "average_job_time_warning_multiplier": 2.0
        }

    async def start_collection(self, interval_seconds: float = 60.0):
        """Start metrics collection"""
        if self.collection_active:
            return
        
        self.collection_interval = interval_seconds
        self.collection_active = True
        self.collection_task = asyncio.create_task(self._collection_loop())
        
        self.logger.info(f"Metrics collection started (interval: {interval_seconds}s)")

    async def stop_collection(self):
        """Stop metrics collection"""
        self.collection_active = False
        
        if self.collection_task:
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Metrics collection stopped")

    async def _collection_loop(self):
        """Main metrics collection loop"""
        while self.collection_active:
            try:
                # Collect all metrics
                await self._collect_resource_metrics()
                await self._collect_job_metrics()
                await self._collect_agent_metrics()
                await self._collect_system_metrics()
                
                # Check for alerts
                await self._check_alerts()
                
                # Clean up old metrics
                self._cleanup_old_metrics()
                
                await asyncio.sleep(self.collection_interval)
                
            except Exception as e:
                self.logger.error(f"Error in metrics collection loop: {e}")
                await asyncio.sleep(self.collection_interval)

    async def _collect_resource_metrics(self):
        """Collect system resource metrics"""
        try:
            metrics = self.resource_manager.get_current_metrics()
            if metrics:
                self.resource_metrics.append(metrics)
        except Exception as e:
            self.logger.error(f"Failed to collect resource metrics: {e}")

    async def _collect_job_metrics(self):
        """Collect job-specific metrics"""
        try:
            # Get active jobs
            active_jobs = await self.job_repository.get_active_jobs()
            
            for job_manifest in active_jobs:
                processing_time = (datetime.now() - job_manifest.created_at).total_seconds()
                
                # Calculate total file size
                total_size_mb = sum(doc.file_size for doc in job_manifest.documents) / (1024 * 1024)
                
                # Get current agent
                current_agent = "unknown"
                for stage_name, stage in job_manifest.processing_stages.items():
                    if stage.state.value == "running":
                        current_agent = stage_name
                        break
                
                # Count errors
                error_count = len([stage for stage in job_manifest.processing_stages.values() 
                                 if stage.error_message])
                
                job_metrics = JobMetrics(
                    job_id=job_manifest.job_id,
                    title=job_manifest.title,
                    state=job_manifest.state.value,
                    progress_percentage=job_manifest.progress_percentage,
                    processing_time_seconds=processing_time,
                    documents_count=len(job_manifest.documents),
                    total_file_size_mb=total_size_mb,
                    current_agent=current_agent,
                    error_count=error_count,
                    created_at=job_manifest.created_at,
                    updated_at=job_manifest.updated_at
                )
                
                self.job_metrics[job_manifest.job_id] = job_metrics
                
                # Track completion times
                if job_manifest.state == JobState.COMPLETED:
                    self.job_completion_times.append(processing_time)
                
        except Exception as e:
            self.logger.error(f"Failed to collect job metrics: {e}")

    async def _collect_agent_metrics(self):
        """Collect agent performance metrics"""
        try:
            # Get job statistics by agent
            agent_stats = await self._calculate_agent_statistics()
            
            for agent_name, stats in agent_stats.items():
                agent_metrics = AgentMetrics(
                    agent_name=agent_name,
                    total_jobs_processed=stats["total_processed"],
                    successful_jobs=stats["successful"],
                    failed_jobs=stats["failed"],
                    average_processing_time=stats["avg_processing_time"],
                    total_processing_time=stats["total_processing_time"],
                    current_active_jobs=stats["active_jobs"],
                    error_rate=stats["error_rate"],
                    last_activity=stats["last_activity"]
                )
                
                self.agent_metrics[agent_name] = agent_metrics
                
        except Exception as e:
            self.logger.error(f"Failed to collect agent metrics: {e}")

    async def _collect_system_metrics(self):
        """Collect system-wide metrics"""
        try:
            # Get job statistics
            total_jobs = await self.job_repository.get_job_count()
            completed_jobs = await self.job_repository.get_job_count(state=JobState.COMPLETED)
            failed_jobs = await self.job_repository.get_job_count(state=JobState.FAILED)
            active_jobs = len(await self.job_repository.get_active_jobs())
            
            # Calculate averages
            avg_completion_time = (sum(self.job_completion_times) / len(self.job_completion_times) 
                                 if self.job_completion_times else 0.0)
            
            # Get resource metrics
            current_resource_metrics = self.resource_manager.get_current_metrics()
            
            # Calculate error rate
            total_errors = sum(self.error_counts.values())
            error_rate = (total_errors / max(total_jobs, 1)) * 100
            
            system_metrics = SystemMetrics(
                timestamp=datetime.now(),
                uptime_seconds=time.time() - self.start_time,
                total_jobs_created=total_jobs,
                total_jobs_completed=completed_jobs,
                total_jobs_failed=failed_jobs,
                active_jobs=active_jobs,
                queued_jobs=0,  # Would be calculated from job queue
                average_job_completion_time=avg_completion_time,
                system_load_average=0.0,  # Would be calculated from system load
                memory_usage_percent=current_resource_metrics.memory_percent if current_resource_metrics else 0.0,
                cpu_usage_percent=current_resource_metrics.cpu_percent if current_resource_metrics else 0.0,
                gpu_usage_percent=current_resource_metrics.gpu_utilization_percent if current_resource_metrics else 0.0,
                disk_usage_percent=current_resource_metrics.disk_percent if current_resource_metrics else 0.0,
                error_rate_percent=error_rate
            )
            
            self.system_metrics.append(system_metrics)
            
        except Exception as e:
            self.logger.error(f"Failed to collect system metrics: {e}")

    async def _calculate_agent_statistics(self) -> Dict[str, Dict[str, Any]]:
        """Calculate statistics for each agent"""
        agent_stats = defaultdict(lambda: {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "total_processing_time": 0.0,
            "active_jobs": 0,
            "last_activity": datetime.min
        })
        
        # Get recent jobs for analysis
        recent_jobs = await self.job_repository.get_recent_jobs(limit=1000)
        
        for job in recent_jobs:
            for stage_name, stage in job.processing_stages.items():
                stats = agent_stats[stage_name]
                
                if stage.state.value in ["completed", "failed"]:
                    stats["total_processed"] += 1
                    
                    if stage.state.value == "completed":
                        stats["successful"] += 1
                    else:
                        stats["failed"] += 1
                    
                    # Calculate processing time
                    if stage.started_at and stage.completed_at:
                        processing_time = (stage.completed_at - stage.started_at).total_seconds()
                        stats["total_processing_time"] += processing_time
                    
                    # Update last activity
                    if stage.completed_at and stage.completed_at > stats["last_activity"]:
                        stats["last_activity"] = stage.completed_at
                
                elif stage.state.value == "running":
                    stats["active_jobs"] += 1
        
        # Calculate derived metrics
        for agent_name, stats in agent_stats.items():
            if stats["total_processed"] > 0:
                stats["avg_processing_time"] = stats["total_processing_time"] / stats["total_processed"]
                stats["error_rate"] = (stats["failed"] / stats["total_processed"]) * 100
            else:
                stats["avg_processing_time"] = 0.0
                stats["error_rate"] = 0.0
        
        return dict(agent_stats)

    async def _check_alerts(self):
        """Check for alert conditions and trigger callbacks"""
        try:
            alerts = []
            
            # Check resource alerts
            current_metrics = self.resource_manager.get_current_metrics()
            if current_metrics:
                # Memory alerts
                if current_metrics.memory_percent >= self.alert_thresholds["memory_usage_critical"]:
                    alerts.append({
                        "type": "critical",
                        "category": "resource",
                        "message": f"Critical memory usage: {current_metrics.memory_percent:.1f}%",
                        "value": current_metrics.memory_percent,
                        "threshold": self.alert_thresholds["memory_usage_critical"]
                    })
                elif current_metrics.memory_percent >= self.alert_thresholds["memory_usage_warning"]:
                    alerts.append({
                        "type": "warning",
                        "category": "resource",
                        "message": f"High memory usage: {current_metrics.memory_percent:.1f}%",
                        "value": current_metrics.memory_percent,
                        "threshold": self.alert_thresholds["memory_usage_warning"]
                    })
                
                # CPU alerts
                if current_metrics.cpu_percent >= self.alert_thresholds["cpu_usage_critical"]:
                    alerts.append({
                        "type": "critical",
                        "category": "resource",
                        "message": f"Critical CPU usage: {current_metrics.cpu_percent:.1f}%",
                        "value": current_metrics.cpu_percent,
                        "threshold": self.alert_thresholds["cpu_usage_critical"]
                    })
                
                # Disk alerts
                if current_metrics.disk_percent >= self.alert_thresholds["disk_usage_critical"]:
                    alerts.append({
                        "type": "critical",
                        "category": "resource",
                        "message": f"Critical disk usage: {current_metrics.disk_percent:.1f}%",
                        "value": current_metrics.disk_percent,
                        "threshold": self.alert_thresholds["disk_usage_critical"]
                    })
            
            # Check job performance alerts
            if self.system_metrics:
                latest_system_metrics = self.system_metrics[-1]
                
                # Error rate alerts
                if latest_system_metrics.error_rate_percent >= self.alert_thresholds["error_rate_critical"]:
                    alerts.append({
                        "type": "critical",
                        "category": "performance",
                        "message": f"Critical error rate: {latest_system_metrics.error_rate_percent:.1f}%",
                        "value": latest_system_metrics.error_rate_percent,
                        "threshold": self.alert_thresholds["error_rate_critical"]
                    })
            
            # Trigger alert callbacks
            for alert in alerts:
                for callback in self.alert_callbacks:
                    try:
                        callback(alert["type"], alert)
                    except Exception as e:
                        self.logger.error(f"Alert callback failed: {e}")
                        
        except Exception as e:
            self.logger.error(f"Failed to check alerts: {e}")

    def _cleanup_old_metrics(self):
        """Clean up old metrics based on retention policy"""
        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
        
        # Clean up job metrics for completed jobs
        completed_jobs = [job_id for job_id, metrics in self.job_metrics.items() 
                         if metrics.state in ["completed", "failed", "cancelled"] 
                         and metrics.updated_at < cutoff_time]
        
        for job_id in completed_jobs:
            del self.job_metrics[job_id]

    def add_alert_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """Add an alert callback function"""
        self.alert_callbacks.append(callback)

    def get_current_metrics_summary(self) -> Dict[str, Any]:
        """Get current metrics summary"""
        current_resource_metrics = self.resource_manager.get_current_metrics()
        latest_system_metrics = self.system_metrics[-1] if self.system_metrics else None
        
        return {
            "timestamp": datetime.now().isoformat(),
            "resource_metrics": asdict(current_resource_metrics) if current_resource_metrics else None,
            "system_metrics": asdict(latest_system_metrics) if latest_system_metrics else None,
            "active_jobs": len([j for j in self.job_metrics.values() if j.state == "running"]),
            "total_agents": len(self.agent_metrics),
            "collection_active": self.collection_active,
            "uptime_hours": (time.time() - self.start_time) / 3600
        }

    def get_job_metrics(self, job_id: Optional[str] = None) -> Dict[str, Any]:
        """Get job metrics"""
        if job_id:
            return asdict(self.job_metrics.get(job_id)) if job_id in self.job_metrics else None
        else:
            return {job_id: asdict(metrics) for job_id, metrics in self.job_metrics.items()}

    def get_agent_metrics(self, agent_name: Optional[str] = None) -> Dict[str, Any]:
        """Get agent metrics"""
        if agent_name:
            return asdict(self.agent_metrics.get(agent_name)) if agent_name in self.agent_metrics else None
        else:
            return {agent: asdict(metrics) for agent, metrics in self.agent_metrics.items()}

    def get_historical_metrics(self, hours: int = 24) -> Dict[str, Any]:
        """Get historical metrics for the specified time period"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Filter resource metrics
        recent_resource_metrics = [
            asdict(m) for m in self.resource_metrics 
            if datetime.fromtimestamp(m.timestamp) >= cutoff_time
        ]
        
        # Filter system metrics
        recent_system_metrics = [
            asdict(m) for m in self.system_metrics 
            if m.timestamp >= cutoff_time
        ]
        
        return {
            "time_period_hours": hours,
            "resource_metrics": recent_resource_metrics,
            "system_metrics": recent_system_metrics,
            "metrics_count": {
                "resource": len(recent_resource_metrics),
                "system": len(recent_system_metrics)
            }
        }

    def export_metrics(self, file_path: Path, format: str = "json", hours: int = 24):
        """Export metrics to file"""
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "export_format": format,
            "time_period_hours": hours,
            "current_summary": self.get_current_metrics_summary(),
            "historical_data": self.get_historical_metrics(hours),
            "job_metrics": self.get_job_metrics(),
            "agent_metrics": self.get_agent_metrics()
        }
        
        try:
            if format.lower() == "json":
                with open(file_path, 'w') as f:
                    json.dump(export_data, f, indent=2, default=str)
            else:
                raise ValueError(f"Unsupported export format: {format}")
            
            self.logger.info(f"Metrics exported to {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to export metrics: {e}")
            raise


# Global metrics collector instance
_metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """Get global metrics collector instance"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


async def initialize_metrics_collector(retention_hours: int = 168) -> MetricsCollector:
    """Initialize and start the global metrics collector"""
    global _metrics_collector
    _metrics_collector = MetricsCollector(retention_hours)
    await _metrics_collector.start_collection()
    return _metrics_collector
