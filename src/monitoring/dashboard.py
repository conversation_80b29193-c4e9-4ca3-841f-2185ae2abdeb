"""
Observability Dashboard

Web-based dashboard for monitoring system performance, job status,
and operational metrics with real-time updates.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import aiohttp
from aiohttp import web, WSMsgType
import aiohttp_cors
import weakref

from .metrics_collector import get_metrics_collector, MetricsCollector
from ..database.models import JobRepository
from ..core.resource_manager import get_resource_manager
from ..config.system_config import get_config


class DashboardServer:
    """
    Web-based observability dashboard with real-time monitoring.
    
    Features:
    - Real-time system metrics visualization
    - Job status and progress monitoring
    - Agent performance analytics
    - Resource utilization tracking
    - Alert notifications
    - Historical data analysis
    """
    
    def __init__(self, host: str = "localhost", port: int = 8080):
        self.host = host
        self.port = port
        self.logger = logging.getLogger(__name__)
        
        # Components
        self.metrics_collector = get_metrics_collector()
        self.resource_manager = get_resource_manager()
        self.job_repository = JobRepository()
        self.config = get_config()
        
        # Web server
        self.app = web.Application()
        self.server = None
        self.site = None
        
        # WebSocket connections for real-time updates
        self.websocket_connections: weakref.WeakSet = weakref.WeakSet()
        
        # Update task
        self.update_task: Optional[asyncio.Task] = None
        self.update_interval = 5.0  # seconds
        
        # Setup routes and middleware
        self._setup_routes()
        self._setup_cors()
        self._setup_static_files()

    def _setup_routes(self):
        """Setup HTTP routes"""
        # API routes
        self.app.router.add_get('/api/status', self._handle_status)
        self.app.router.add_get('/api/metrics', self._handle_metrics)
        self.app.router.add_get('/api/jobs', self._handle_jobs)
        self.app.router.add_get('/api/jobs/{job_id}', self._handle_job_detail)
        self.app.router.add_get('/api/agents', self._handle_agents)
        self.app.router.add_get('/api/config', self._handle_config)
        self.app.router.add_get('/api/alerts', self._handle_alerts)
        
        # WebSocket route
        self.app.router.add_get('/ws', self._handle_websocket)
        
        # Dashboard route
        self.app.router.add_get('/', self._handle_dashboard)

    def _setup_cors(self):
        """Setup CORS for API access"""
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        # Add CORS to all routes
        for route in list(self.app.router.routes()):
            cors.add(route)

    def _setup_static_files(self):
        """Setup static file serving for dashboard assets"""
        # In a real implementation, you'd serve actual HTML/CSS/JS files
        # For now, we'll serve a simple HTML dashboard
        pass

    async def start(self):
        """Start the dashboard server"""
        try:
            # Create server
            runner = web.AppRunner(self.app)
            await runner.setup()
            
            self.site = web.TCPSite(runner, self.host, self.port)
            await self.site.start()
            
            # Start update task for WebSocket clients
            self.update_task = asyncio.create_task(self._update_loop())
            
            self.logger.info(f"Dashboard server started at http://{self.host}:{self.port}")
            
        except Exception as e:
            self.logger.error(f"Failed to start dashboard server: {e}")
            raise

    async def stop(self):
        """Stop the dashboard server"""
        try:
            # Stop update task
            if self.update_task:
                self.update_task.cancel()
                try:
                    await self.update_task
                except asyncio.CancelledError:
                    pass
            
            # Close WebSocket connections
            for ws in list(self.websocket_connections):
                if not ws.closed:
                    await ws.close()
            
            # Stop server
            if self.site:
                await self.site.stop()
            
            self.logger.info("Dashboard server stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping dashboard server: {e}")

    async def _update_loop(self):
        """Send periodic updates to WebSocket clients"""
        while True:
            try:
                # Get current data
                update_data = await self._get_dashboard_data()
                
                # Send to all connected WebSocket clients
                if self.websocket_connections:
                    message = json.dumps({
                        "type": "update",
                        "timestamp": datetime.now().isoformat(),
                        "data": update_data
                    })
                    
                    # Send to all connections
                    disconnected = []
                    for ws in list(self.websocket_connections):
                        try:
                            if ws.closed:
                                disconnected.append(ws)
                            else:
                                await ws.send_str(message)
                        except Exception as e:
                            self.logger.warning(f"Failed to send WebSocket update: {e}")
                            disconnected.append(ws)
                    
                    # Clean up disconnected clients
                    for ws in disconnected:
                        self.websocket_connections.discard(ws)
                
                await asyncio.sleep(self.update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in update loop: {e}")
                await asyncio.sleep(self.update_interval)

    async def _get_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive dashboard data"""
        try:
            # Get metrics summary
            metrics_summary = self.metrics_collector.get_current_metrics_summary()
            
            # Get active jobs
            active_jobs = await self.job_repository.get_active_jobs()
            active_jobs_data = [
                {
                    "job_id": job.job_id,
                    "title": job.title,
                    "state": job.state.value,
                    "progress": job.progress_percentage,
                    "created_at": job.created_at.isoformat()
                }
                for job in active_jobs[:10]  # Limit to 10 most recent
            ]
            
            # Get agent metrics
            agent_metrics = self.metrics_collector.get_agent_metrics()
            
            # Get resource status
            resource_status = self.resource_manager.get_resource_status()
            resource_status_data = {k.value: v.value for k, v in resource_status.items()}
            
            # Get recent alerts (simplified)
            alerts = []  # Would be populated from alert system
            
            return {
                "metrics_summary": metrics_summary,
                "active_jobs": active_jobs_data,
                "agent_metrics": agent_metrics,
                "resource_status": resource_status_data,
                "alerts": alerts
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get dashboard data: {e}")
            return {"error": str(e)}

    # HTTP Route Handlers

    async def _handle_dashboard(self, request):
        """Serve the main dashboard HTML"""
        html_content = self._generate_dashboard_html()
        return web.Response(text=html_content, content_type='text/html')

    async def _handle_status(self, request):
        """Handle system status API request"""
        try:
            status_data = {
                "system_info": self.config.get_system_info(),
                "hardware_profile": self.config.get_hardware_profile().value,
                "uptime_seconds": self.metrics_collector.start_time,
                "metrics_collection_active": self.metrics_collector.collection_active,
                "dashboard_clients": len(self.websocket_connections)
            }
            
            return web.json_response(status_data)
            
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def _handle_metrics(self, request):
        """Handle metrics API request"""
        try:
            hours = int(request.query.get('hours', 1))
            metrics_data = self.metrics_collector.get_historical_metrics(hours)
            
            return web.json_response(metrics_data)
            
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def _handle_jobs(self, request):
        """Handle jobs API request"""
        try:
            limit = int(request.query.get('limit', 50))
            state = request.query.get('state')
            
            if state:
                from ..models.job_manifest import JobState
                job_state = JobState(state)
                jobs = await self.job_repository.get_jobs_by_state(job_state, limit=limit)
            else:
                jobs = await self.job_repository.get_recent_jobs(limit=limit)
            
            jobs_data = [
                {
                    "job_id": job.job_id,
                    "title": job.title,
                    "state": job.state.value,
                    "progress": job.progress_percentage,
                    "created_at": job.created_at.isoformat(),
                    "updated_at": job.updated_at.isoformat(),
                    "documents_count": len(job.documents)
                }
                for job in jobs
            ]
            
            return web.json_response({"jobs": jobs_data})
            
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def _handle_job_detail(self, request):
        """Handle job detail API request"""
        try:
            job_id = request.match_info['job_id']
            job_manifest = await self.job_repository.get_job(job_id)
            
            if not job_manifest:
                return web.json_response({"error": "Job not found"}, status=404)
            
            job_data = job_manifest.to_dict()
            
            # Add metrics if available
            job_metrics = self.metrics_collector.get_job_metrics(job_id)
            if job_metrics:
                job_data["metrics"] = job_metrics
            
            return web.json_response(job_data)
            
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def _handle_agents(self, request):
        """Handle agents API request"""
        try:
            agent_metrics = self.metrics_collector.get_agent_metrics()
            return web.json_response({"agents": agent_metrics})
            
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def _handle_config(self, request):
        """Handle configuration API request"""
        try:
            from ..config.config_manager import get_config_manager
            config_manager = get_config_manager()
            config_summary = config_manager.get_configuration_summary()
            
            return web.json_response(config_summary)
            
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def _handle_alerts(self, request):
        """Handle alerts API request"""
        try:
            # In a real implementation, this would return actual alerts
            alerts = []
            return web.json_response({"alerts": alerts})
            
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def _handle_websocket(self, request):
        """Handle WebSocket connection for real-time updates"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        # Add to connections
        self.websocket_connections.add(ws)
        self.logger.info("New WebSocket client connected")
        
        try:
            # Send initial data
            initial_data = await self._get_dashboard_data()
            await ws.send_str(json.dumps({
                "type": "initial",
                "timestamp": datetime.now().isoformat(),
                "data": initial_data
            }))
            
            # Handle incoming messages
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        await self._handle_websocket_message(ws, data)
                    except json.JSONDecodeError:
                        await ws.send_str(json.dumps({"error": "Invalid JSON"}))
                elif msg.type == WSMsgType.ERROR:
                    self.logger.error(f"WebSocket error: {ws.exception()}")
                    break
                    
        except Exception as e:
            self.logger.error(f"WebSocket error: {e}")
        finally:
            self.websocket_connections.discard(ws)
            self.logger.info("WebSocket client disconnected")
        
        return ws

    async def _handle_websocket_message(self, ws, data):
        """Handle incoming WebSocket message"""
        try:
            message_type = data.get("type")
            
            if message_type == "ping":
                await ws.send_str(json.dumps({"type": "pong"}))
            elif message_type == "subscribe":
                # Handle subscription to specific data streams
                pass
            elif message_type == "unsubscribe":
                # Handle unsubscription
                pass
            else:
                await ws.send_str(json.dumps({"error": f"Unknown message type: {message_type}"}))
                
        except Exception as e:
            await ws.send_str(json.dumps({"error": str(e)}))

    def _generate_dashboard_html(self) -> str:
        """Generate the dashboard HTML page"""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Study Guide Generator - Dashboard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: #007bff;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-available { background-color: #28a745; }
        .status-limited { background-color: #ffc107; }
        .status-critical { background-color: #fd7e14; }
        .status-exhausted { background-color: #dc3545; }
        .jobs-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .jobs-table th,
        .jobs-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .jobs-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .progress-bar {
            width: 100px;
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            color: white;
            font-weight: 600;
        }
        .connected { background-color: #28a745; }
        .disconnected { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">Connecting...</div>
    
    <div class="container">
        <div class="header">
            <h1>Study Guide Generator Dashboard</h1>
            <p>Real-time monitoring and system status</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-title">System Status</div>
                <div id="systemStatus">Loading...</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Active Jobs</div>
                <div class="metric-value" id="activeJobs">-</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Memory Usage</div>
                <div class="metric-value" id="memoryUsage">-</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">CPU Usage</div>
                <div class="metric-value" id="cpuUsage">-</div>
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-title">Resource Status</div>
            <div id="resourceStatus">Loading...</div>
        </div>
        
        <div class="metric-card" style="margin-top: 20px;">
            <div class="metric-title">Active Jobs</div>
            <table class="jobs-table" id="jobsTable">
                <thead>
                    <tr>
                        <th>Job ID</th>
                        <th>Title</th>
                        <th>State</th>
                        <th>Progress</th>
                        <th>Created</th>
                    </tr>
                </thead>
                <tbody id="jobsTableBody">
                    <tr><td colspan="5">Loading...</td></tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        let ws = null;
        let reconnectInterval = null;
        
        function connect() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                console.log('WebSocket connected');
                document.getElementById('connectionStatus').textContent = 'Connected';
                document.getElementById('connectionStatus').className = 'connection-status connected';
                
                if (reconnectInterval) {
                    clearInterval(reconnectInterval);
                    reconnectInterval = null;
                }
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                if (message.type === 'initial' || message.type === 'update') {
                    updateDashboard(message.data);
                }
            };
            
            ws.onclose = function() {
                console.log('WebSocket disconnected');
                document.getElementById('connectionStatus').textContent = 'Disconnected';
                document.getElementById('connectionStatus').className = 'connection-status disconnected';
                
                // Attempt to reconnect
                if (!reconnectInterval) {
                    reconnectInterval = setInterval(connect, 5000);
                }
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
            };
        }
        
        function updateDashboard(data) {
            // Update metrics
            if (data.metrics_summary) {
                document.getElementById('activeJobs').textContent = data.metrics_summary.active_jobs || 0;
                
                if (data.metrics_summary.resource_metrics) {
                    const rm = data.metrics_summary.resource_metrics;
                    document.getElementById('memoryUsage').textContent = `${rm.memory_percent.toFixed(1)}%`;
                    document.getElementById('cpuUsage').textContent = `${rm.cpu_percent.toFixed(1)}%`;
                }
            }
            
            // Update resource status
            if (data.resource_status) {
                const statusHtml = Object.entries(data.resource_status)
                    .map(([resource, status]) => 
                        `<div><span class="status-indicator status-${status}"></span>${resource}: ${status}</div>`
                    ).join('');
                document.getElementById('resourceStatus').innerHTML = statusHtml;
            }
            
            // Update jobs table
            if (data.active_jobs) {
                const tbody = document.getElementById('jobsTableBody');
                if (data.active_jobs.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5">No active jobs</td></tr>';
                } else {
                    tbody.innerHTML = data.active_jobs.map(job => `
                        <tr>
                            <td>${job.job_id.substring(0, 8)}...</td>
                            <td>${job.title}</td>
                            <td>${job.state}</td>
                            <td>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${job.progress}%"></div>
                                </div>
                                ${job.progress.toFixed(1)}%
                            </td>
                            <td>${new Date(job.created_at).toLocaleString()}</td>
                        </tr>
                    `).join('');
                }
            }
        }
        
        // Connect on page load
        connect();
    </script>
</body>
</html>
        """


# Global dashboard server instance
_dashboard_server: Optional[DashboardServer] = None


def get_dashboard_server() -> DashboardServer:
    """Get global dashboard server instance"""
    global _dashboard_server
    if _dashboard_server is None:
        _dashboard_server = DashboardServer()
    return _dashboard_server


async def start_dashboard_server(host: str = "localhost", port: int = 8080) -> DashboardServer:
    """Start the dashboard server"""
    global _dashboard_server
    _dashboard_server = DashboardServer(host, port)
    await _dashboard_server.start()
    return _dashboard_server
