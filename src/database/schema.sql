-- SQLite Database Schema for Local Study Guide Generator
-- Optimized for concurrent access with WAL mode

-- Enable WAL mode and foreign keys
PRAGMA journal_mode = WAL;
PRAGMA foreign_keys = ON;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = memory;

-- Jobs table - Core job tracking
CREATE TABLE IF NOT EXISTS jobs (
    job_id TEXT PRIMARY KEY,
    correlation_id TEXT NOT NULL,
    user_id TEXT,
    priority TEXT NOT NULL DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    current_state TEXT NOT NULL DEFAULT 'pending' CHECK (current_state IN (
        'pending', 'validating', 'extracting', 'chunking', 'vectorizing',
        'outlining', 'synthesizing', 'enhancing', 'generating_qa', 'compiling',
        'completed', 'failed', 'paused', 'cancelled'
    )),
    previous_state TEXT,
    overall_progress REAL DEFAULT 0.0 CHECK (overall_progress >= 0.0 AND overall_progress <= 100.0),
    failure_count INTEGER DEFAULT 0,
    max_failures INTEGER DEFAULT 5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    estimated_completion_time TIMESTAMP,
    final_output_path TEXT,
    configuration_json TEXT, -- JSON blob for job configuration
    checkpoint_data_json TEXT, -- JSON blob for checkpoint data
    UNIQUE(correlation_id)
);

-- Documents table - Document metadata and tracking
CREATE TABLE IF NOT EXISTS documents (
    document_id INTEGER PRIMARY KEY AUTOINCREMENT,
    job_id TEXT NOT NULL,
    file_path TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    checksum_sha256 TEXT NOT NULL,
    mime_type TEXT NOT NULL,
    page_count INTEGER,
    language TEXT DEFAULT 'en',
    upload_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    validation_status TEXT DEFAULT 'pending' CHECK (validation_status IN ('pending', 'valid', 'invalid', 'quarantined')),
    extraction_method TEXT,
    FOREIGN KEY (job_id) REFERENCES jobs(job_id) ON DELETE CASCADE,
    UNIQUE(checksum_sha256, job_id)
);

-- Processing stages table - Individual agent progress tracking
CREATE TABLE IF NOT EXISTS processing_stages (
    stage_id INTEGER PRIMARY KEY AUTOINCREMENT,
    job_id TEXT NOT NULL,
    agent_name TEXT NOT NULL,
    state TEXT NOT NULL DEFAULT 'not_started' CHECK (state IN ('not_started', 'running', 'completed', 'failed', 'retrying')),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    progress_percentage REAL DEFAULT 0.0 CHECK (progress_percentage >= 0.0 AND progress_percentage <= 100.0),
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    error_message TEXT,
    output_artifacts_json TEXT, -- JSON array of output file paths
    resource_usage_json TEXT, -- JSON blob for resource metrics
    FOREIGN KEY (job_id) REFERENCES jobs(job_id) ON DELETE CASCADE,
    UNIQUE(job_id, agent_name)
);

-- Error logs table - Comprehensive error tracking
CREATE TABLE IF NOT EXISTS error_logs (
    error_id INTEGER PRIMARY KEY AUTOINCREMENT,
    job_id TEXT NOT NULL,
    correlation_id TEXT NOT NULL,
    agent_name TEXT NOT NULL,
    error_type TEXT NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    recovery_action TEXT,
    retry_attempt INTEGER DEFAULT 0,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (job_id) REFERENCES jobs(job_id) ON DELETE CASCADE
);

-- Resource metrics table - System resource usage tracking
CREATE TABLE IF NOT EXISTS resource_metrics (
    metric_id INTEGER PRIMARY KEY AUTOINCREMENT,
    job_id TEXT NOT NULL,
    agent_name TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cpu_usage_percent REAL DEFAULT 0.0,
    memory_usage_mb REAL DEFAULT 0.0,
    gpu_memory_usage_mb REAL DEFAULT 0.0,
    disk_usage_mb REAL DEFAULT 0.0,
    processing_time_seconds REAL DEFAULT 0.0,
    tokens_processed INTEGER DEFAULT 0,
    embeddings_generated INTEGER DEFAULT 0,
    FOREIGN KEY (job_id) REFERENCES jobs(job_id) ON DELETE CASCADE
);

-- Job state history table - State transition tracking
CREATE TABLE IF NOT EXISTS job_state_history (
    history_id INTEGER PRIMARY KEY AUTOINCREMENT,
    job_id TEXT NOT NULL,
    from_state TEXT,
    to_state TEXT NOT NULL,
    agent_name TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (job_id) REFERENCES jobs(job_id) ON DELETE CASCADE
);

-- Output artifacts table - Generated file tracking
CREATE TABLE IF NOT EXISTS output_artifacts (
    artifact_id INTEGER PRIMARY KEY AUTOINCREMENT,
    job_id TEXT NOT NULL,
    agent_name TEXT NOT NULL,
    artifact_type TEXT NOT NULL, -- 'text', 'image', 'diagram', 'pdf', etc.
    file_path TEXT NOT NULL,
    file_size INTEGER,
    checksum_sha256 TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata_json TEXT, -- JSON blob for artifact-specific metadata
    FOREIGN KEY (job_id) REFERENCES jobs(job_id) ON DELETE CASCADE
);

-- System configuration table - Runtime configuration storage
CREATE TABLE IF NOT EXISTS system_config (
    config_key TEXT PRIMARY KEY,
    config_value TEXT NOT NULL,
    config_type TEXT NOT NULL DEFAULT 'string' CHECK (config_type IN ('string', 'integer', 'float', 'boolean', 'json')),
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Performance benchmarks table - System performance tracking
CREATE TABLE IF NOT EXISTS performance_benchmarks (
    benchmark_id INTEGER PRIMARY KEY AUTOINCREMENT,
    job_id TEXT,
    benchmark_type TEXT NOT NULL, -- 'ocr', 'embedding', 'llm_inference', etc.
    document_size_mb REAL,
    processing_time_seconds REAL NOT NULL,
    throughput_metric REAL, -- pages/sec, tokens/sec, etc.
    hardware_profile TEXT, -- 'consumer_gpu', 'professional_gpu', 'cpu_only'
    model_used TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (job_id) REFERENCES jobs(job_id) ON DELETE SET NULL
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_jobs_state ON jobs(current_state);
CREATE INDEX IF NOT EXISTS idx_jobs_priority ON jobs(priority);
CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON jobs(created_at);
CREATE INDEX IF NOT EXISTS idx_jobs_correlation_id ON jobs(correlation_id);

CREATE INDEX IF NOT EXISTS idx_documents_job_id ON documents(job_id);
CREATE INDEX IF NOT EXISTS idx_documents_checksum ON documents(checksum_sha256);

CREATE INDEX IF NOT EXISTS idx_processing_stages_job_id ON processing_stages(job_id);
CREATE INDEX IF NOT EXISTS idx_processing_stages_agent ON processing_stages(agent_name);
CREATE INDEX IF NOT EXISTS idx_processing_stages_state ON processing_stages(state);

CREATE INDEX IF NOT EXISTS idx_error_logs_job_id ON error_logs(job_id);
CREATE INDEX IF NOT EXISTS idx_error_logs_correlation_id ON error_logs(correlation_id);
CREATE INDEX IF NOT EXISTS idx_error_logs_timestamp ON error_logs(timestamp);

CREATE INDEX IF NOT EXISTS idx_resource_metrics_job_id ON resource_metrics(job_id);
CREATE INDEX IF NOT EXISTS idx_resource_metrics_timestamp ON resource_metrics(timestamp);

CREATE INDEX IF NOT EXISTS idx_job_state_history_job_id ON job_state_history(job_id);
CREATE INDEX IF NOT EXISTS idx_job_state_history_timestamp ON job_state_history(timestamp);

CREATE INDEX IF NOT EXISTS idx_output_artifacts_job_id ON output_artifacts(job_id);
CREATE INDEX IF NOT EXISTS idx_output_artifacts_type ON output_artifacts(artifact_type);

CREATE INDEX IF NOT EXISTS idx_performance_benchmarks_type ON performance_benchmarks(benchmark_type);
CREATE INDEX IF NOT EXISTS idx_performance_benchmarks_timestamp ON performance_benchmarks(timestamp);

-- Triggers for automatic timestamp updates
CREATE TRIGGER IF NOT EXISTS update_jobs_timestamp 
    AFTER UPDATE ON jobs
    BEGIN
        UPDATE jobs SET updated_at = CURRENT_TIMESTAMP WHERE job_id = NEW.job_id;
    END;

CREATE TRIGGER IF NOT EXISTS update_system_config_timestamp 
    AFTER UPDATE ON system_config
    BEGIN
        UPDATE system_config SET updated_at = CURRENT_TIMESTAMP WHERE config_key = NEW.config_key;
    END;

-- Views for common queries
CREATE VIEW IF NOT EXISTS active_jobs AS
SELECT 
    j.*,
    COUNT(d.document_id) as document_count,
    AVG(ps.progress_percentage) as avg_stage_progress
FROM jobs j
LEFT JOIN documents d ON j.job_id = d.job_id
LEFT JOIN processing_stages ps ON j.job_id = ps.job_id
WHERE j.current_state NOT IN ('completed', 'failed', 'cancelled')
GROUP BY j.job_id;

CREATE VIEW IF NOT EXISTS job_summary AS
SELECT 
    j.job_id,
    j.correlation_id,
    j.current_state,
    j.priority,
    j.overall_progress,
    j.created_at,
    j.updated_at,
    COUNT(d.document_id) as document_count,
    COUNT(CASE WHEN ps.state = 'completed' THEN 1 END) as completed_stages,
    COUNT(ps.stage_id) as total_stages,
    COUNT(e.error_id) as error_count
FROM jobs j
LEFT JOIN documents d ON j.job_id = d.job_id
LEFT JOIN processing_stages ps ON j.job_id = ps.job_id
LEFT JOIN error_logs e ON j.job_id = e.job_id AND e.resolved = FALSE
GROUP BY j.job_id;

-- Insert default system configuration
INSERT OR IGNORE INTO system_config (config_key, config_value, config_type, description) VALUES
('max_concurrent_jobs', '3', 'integer', 'Maximum number of jobs that can run concurrently'),
('default_model_llm', 'llama3:8b-instruct', 'string', 'Default LLM model for text generation'),
('default_model_embedding', 'bge-small-en-v1.5', 'string', 'Default embedding model'),
('max_document_size_mb', '100', 'integer', 'Maximum document size in MB'),
('cleanup_completed_jobs_days', '30', 'integer', 'Days to keep completed job data'),
('enable_gpu_acceleration', 'true', 'boolean', 'Enable GPU acceleration when available'),
('log_level', 'INFO', 'string', 'System logging level'),
('backup_retention_days', '7', 'integer', 'Days to retain job backups');
