"""
Database Models and Repository Classes

Provides data access layer for job management, state persistence,
and metrics tracking using SQLite with async support.
"""

import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

import aiosqlite
from ..models.job_manifest import JobManifest, JobState, DocumentMetadata, ProcessingStage, ErrorRecord


class DatabaseError(Exception):
    """Database operation errors"""
    pass


class JobRepository:
    """Repository for job manifest persistence and querying"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.db_dir = Path(db_path).parent
        self.db_dir.mkdir(parents=True, exist_ok=True)

    async def initialize(self):
        """Initialize database with schema"""
        async with aiosqlite.connect(self.db_path) as db:
            # Enable WAL mode and foreign keys
            await db.execute("PRAGMA journal_mode = WAL")
            await db.execute("PRAGMA foreign_keys = ON")
            await db.execute("PRAGMA synchronous = NORMAL")
            
            # Read and execute schema
            schema_path = Path(__file__).parent / "schema.sql"
            with open(schema_path, 'r') as f:
                schema_sql = f.read()
            
            await db.executescript(schema_sql)
            await db.commit()

    async def create_job(self, job_manifest: JobManifest) -> None:
        """Create a new job record"""
        async with aiosqlite.connect(self.db_path) as db:
            # Insert main job record
            await db.execute("""
                INSERT INTO jobs (
                    job_id, correlation_id, user_id, priority, current_state,
                    overall_progress, failure_count, max_failures, created_at,
                    updated_at, configuration_json, checkpoint_data_json
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                job_manifest.job_id,
                job_manifest.correlation_id,
                job_manifest.user_id,
                job_manifest.priority.value,
                job_manifest.current_state.value,
                job_manifest.overall_progress,
                job_manifest.failure_count,
                job_manifest.max_failures,
                job_manifest.created_at,
                job_manifest.updated_at,
                json.dumps(job_manifest.configuration.dict()),
                json.dumps(job_manifest.checkpoint_data)
            ))
            
            # Insert documents
            for doc in job_manifest.documents:
                await db.execute("""
                    INSERT INTO documents (
                        job_id, file_path, original_filename, file_size,
                        checksum_sha256, mime_type, page_count, language,
                        upload_timestamp, validation_status, extraction_method
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    job_manifest.job_id,
                    doc.file_path,
                    doc.original_filename,
                    doc.file_size,
                    doc.checksum_sha256,
                    doc.mime_type,
                    doc.page_count,
                    doc.language,
                    doc.upload_timestamp,
                    doc.validation_status,
                    doc.extraction_method
                ))
            
            # Insert processing stages
            for stage_name, stage in job_manifest.processing_stages.items():
                await db.execute("""
                    INSERT INTO processing_stages (
                        job_id, agent_name, state, start_time, end_time,
                        progress_percentage, retry_count, max_retries,
                        error_message, output_artifacts_json, resource_usage_json
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    job_manifest.job_id,
                    stage.agent_name,
                    stage.state.value,
                    stage.start_time,
                    stage.end_time,
                    stage.progress_percentage,
                    stage.retry_count,
                    stage.max_retries,
                    stage.error_message,
                    json.dumps(stage.output_artifacts),
                    json.dumps(stage.resource_usage)
                ))
            
            # Insert errors
            for error in job_manifest.errors:
                await db.execute("""
                    INSERT INTO error_logs (
                        job_id, correlation_id, agent_name, error_type,
                        error_message, stack_trace, recovery_action,
                        retry_attempt, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    job_manifest.job_id,
                    error.correlation_id,
                    error.agent_name,
                    error.error_type,
                    error.error_message,
                    error.stack_trace,
                    error.recovery_action,
                    error.retry_attempt,
                    error.timestamp
                ))
            
            await db.commit()

    async def get_job(self, job_id: str) -> Optional[JobManifest]:
        """Retrieve a job by ID"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            
            # Get main job record
            cursor = await db.execute("""
                SELECT * FROM jobs WHERE job_id = ?
            """, (job_id,))
            job_row = await cursor.fetchone()
            
            if not job_row:
                return None
            
            # Get documents
            cursor = await db.execute("""
                SELECT * FROM documents WHERE job_id = ?
            """, (job_id,))
            doc_rows = await cursor.fetchall()
            
            # Get processing stages
            cursor = await db.execute("""
                SELECT * FROM processing_stages WHERE job_id = ?
            """, (job_id,))
            stage_rows = await cursor.fetchall()
            
            # Get errors
            cursor = await db.execute("""
                SELECT * FROM error_logs WHERE job_id = ? ORDER BY timestamp
            """, (job_id,))
            error_rows = await cursor.fetchall()
            
            # Reconstruct job manifest
            return self._build_job_manifest(job_row, doc_rows, stage_rows, error_rows)

    async def update_job(self, job_manifest: JobManifest) -> None:
        """Update an existing job"""
        async with aiosqlite.connect(self.db_path) as db:
            # Update main job record
            await db.execute("""
                UPDATE jobs SET
                    current_state = ?, previous_state = ?, overall_progress = ?,
                    failure_count = ?, updated_at = ?, estimated_completion_time = ?,
                    final_output_path = ?, configuration_json = ?, checkpoint_data_json = ?
                WHERE job_id = ?
            """, (
                job_manifest.current_state.value,
                job_manifest.previous_state.value if job_manifest.previous_state else None,
                job_manifest.overall_progress,
                job_manifest.failure_count,
                job_manifest.updated_at,
                job_manifest.estimated_completion_time,
                job_manifest.final_output_path,
                json.dumps(job_manifest.configuration.dict()),
                json.dumps(job_manifest.checkpoint_data),
                job_manifest.job_id
            ))
            
            # Update processing stages
            for stage_name, stage in job_manifest.processing_stages.items():
                await db.execute("""
                    INSERT OR REPLACE INTO processing_stages (
                        job_id, agent_name, state, start_time, end_time,
                        progress_percentage, retry_count, max_retries,
                        error_message, output_artifacts_json, resource_usage_json
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    job_manifest.job_id,
                    stage.agent_name,
                    stage.state.value,
                    stage.start_time,
                    stage.end_time,
                    stage.progress_percentage,
                    stage.retry_count,
                    stage.max_retries,
                    stage.error_message,
                    json.dumps(stage.output_artifacts),
                    json.dumps(stage.resource_usage)
                ))
            
            # Add new errors (don't update existing ones)
            existing_error_count = await db.execute_fetchall("""
                SELECT COUNT(*) as count FROM error_logs WHERE job_id = ?
            """, (job_manifest.job_id,))
            
            existing_count = existing_error_count[0][0] if existing_error_count else 0
            new_errors = job_manifest.errors[existing_count:]
            
            for error in new_errors:
                await db.execute("""
                    INSERT INTO error_logs (
                        job_id, correlation_id, agent_name, error_type,
                        error_message, stack_trace, recovery_action,
                        retry_attempt, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    job_manifest.job_id,
                    error.correlation_id,
                    error.agent_name,
                    error.error_type,
                    error.error_message,
                    error.stack_trace,
                    error.recovery_action,
                    error.retry_attempt,
                    error.timestamp
                ))
            
            await db.commit()

    async def get_jobs_by_state(
        self, 
        states: Union[JobState, List[JobState]], 
        before_date: Optional[datetime] = None
    ) -> List[JobManifest]:
        """Get jobs by state(s) with optional date filter"""
        if isinstance(states, JobState):
            states = [states]
        
        state_values = [state.value for state in states]
        placeholders = ','.join(['?' for _ in state_values])
        
        query = f"SELECT job_id FROM jobs WHERE current_state IN ({placeholders})"
        params = state_values
        
        if before_date:
            query += " AND updated_at < ?"
            params.append(before_date)
        
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(query, params)
            job_ids = [row[0] for row in await cursor.fetchall()]
        
        # Fetch full job manifests
        jobs = []
        for job_id in job_ids:
            job = await self.get_job(job_id)
            if job:
                jobs.append(job)
        
        return jobs

    async def delete_job(self, job_id: str) -> bool:
        """Delete a job and all related records"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("DELETE FROM jobs WHERE job_id = ?", (job_id,))
            await db.commit()
            return cursor.rowcount > 0

    async def get_job_statistics(self) -> Dict[str, Any]:
        """Get job processing statistics"""
        async with aiosqlite.connect(self.db_path) as db:
            # Job counts by state
            cursor = await db.execute("""
                SELECT current_state, COUNT(*) as count 
                FROM jobs 
                GROUP BY current_state
            """)
            state_counts = {row[0]: row[1] for row in await cursor.fetchall()}
            
            # Average processing times
            cursor = await db.execute("""
                SELECT 
                    AVG(JULIANDAY(updated_at) - JULIANDAY(created_at)) * 24 * 3600 as avg_duration_seconds
                FROM jobs 
                WHERE current_state = 'completed'
            """)
            avg_duration = await cursor.fetchone()
            
            # Error rates
            cursor = await db.execute("""
                SELECT 
                    COUNT(DISTINCT j.job_id) as total_jobs,
                    COUNT(DISTINCT e.job_id) as jobs_with_errors
                FROM jobs j
                LEFT JOIN error_logs e ON j.job_id = e.job_id
            """)
            error_stats = await cursor.fetchone()
            
            return {
                "job_counts_by_state": state_counts,
                "average_processing_time_seconds": avg_duration[0] if avg_duration[0] else 0,
                "error_rate": error_stats[1] / error_stats[0] if error_stats[0] > 0 else 0,
                "total_jobs": error_stats[0]
            }

    def _build_job_manifest(self, job_row, doc_rows, stage_rows, error_rows) -> JobManifest:
        """Build JobManifest from database rows"""
        # Parse JSON fields
        configuration = json.loads(job_row['configuration_json']) if job_row['configuration_json'] else {}
        checkpoint_data = json.loads(job_row['checkpoint_data_json']) if job_row['checkpoint_data_json'] else {}
        
        # Build documents
        documents = []
        for doc_row in doc_rows:
            doc = DocumentMetadata(
                file_path=doc_row['file_path'],
                original_filename=doc_row['original_filename'],
                file_size=doc_row['file_size'],
                checksum_sha256=doc_row['checksum_sha256'],
                mime_type=doc_row['mime_type'],
                page_count=doc_row['page_count'],
                language=doc_row['language'],
                upload_timestamp=datetime.fromisoformat(doc_row['upload_timestamp']),
                validation_status=doc_row['validation_status'],
                extraction_method=doc_row['extraction_method']
            )
            documents.append(doc)
        
        # Build processing stages
        processing_stages = {}
        for stage_row in stage_rows:
            stage = ProcessingStage(
                agent_name=stage_row['agent_name'],
                state=stage_row['state'],
                start_time=datetime.fromisoformat(stage_row['start_time']) if stage_row['start_time'] else None,
                end_time=datetime.fromisoformat(stage_row['end_time']) if stage_row['end_time'] else None,
                progress_percentage=stage_row['progress_percentage'],
                retry_count=stage_row['retry_count'],
                max_retries=stage_row['max_retries'],
                error_message=stage_row['error_message'],
                output_artifacts=json.loads(stage_row['output_artifacts_json']) if stage_row['output_artifacts_json'] else [],
                resource_usage=json.loads(stage_row['resource_usage_json']) if stage_row['resource_usage_json'] else {}
            )
            processing_stages[stage_row['agent_name']] = stage
        
        # Build errors
        errors = []
        for error_row in error_rows:
            error = ErrorRecord(
                timestamp=datetime.fromisoformat(error_row['timestamp']),
                agent_name=error_row['agent_name'],
                error_type=error_row['error_type'],
                error_message=error_row['error_message'],
                stack_trace=error_row['stack_trace'],
                recovery_action=error_row['recovery_action'],
                retry_attempt=error_row['retry_attempt'],
                correlation_id=error_row['correlation_id']
            )
            errors.append(error)
        
        # Create job manifest
        job_manifest = JobManifest(
            job_id=job_row['job_id'],
            correlation_id=job_row['correlation_id'],
            user_id=job_row['user_id'],
            priority=job_row['priority'],
            current_state=job_row['current_state'],
            previous_state=job_row['previous_state'],
            overall_progress=job_row['overall_progress'],
            failure_count=job_row['failure_count'],
            max_failures=job_row['max_failures'],
            created_at=datetime.fromisoformat(job_row['created_at']),
            updated_at=datetime.fromisoformat(job_row['updated_at']),
            estimated_completion_time=datetime.fromisoformat(job_row['estimated_completion_time']) if job_row['estimated_completion_time'] else None,
            final_output_path=job_row['final_output_path'],
            documents=documents,
            processing_stages=processing_stages,
            errors=errors,
            checkpoint_data=checkpoint_data
        )
        
        return job_manifest
