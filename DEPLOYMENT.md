# Deployment Guide

This guide provides comprehensive instructions for deploying the Study Guide Generator system in various environments, from development to production.

## Table of Contents

1. [Environment Setup](#environment-setup)
2. [Hardware Configurations](#hardware-configurations)
3. [Installation Methods](#installation-methods)
4. [Configuration Management](#configuration-management)
5. [Production Deployment](#production-deployment)
6. [Docker Deployment](#docker-deployment)
7. [Monitoring & Maintenance](#monitoring--maintenance)
8. [Troubleshooting](#troubleshooting)
9. [Performance Optimization](#performance-optimization)

## Environment Setup

### Development Environment

**Minimum Requirements:**
- 8GB RAM
- 4-core CPU
- 50GB available storage
- Python 3.9+

**Setup Steps:**
```bash
# Clone repository
git clone https://github.com/studyguide-generator/studyguide-generator.git
cd studyguide-generator

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install in development mode
pip install -e ".[dev]"

# Initialize configuration
studyguide init --profile development

# Verify installation
studyguide status
```

### Production Environment

**Recommended Requirements:**
- 16GB+ RAM
- 8+ core CPU
- 100GB+ SSD storage
- Dedicated GPU (optional, for enhanced performance)

**Setup Steps:**
```bash
# Install system dependencies
sudo apt-get update
sudo apt-get install -y python3.11 python3.11-venv git pandoc texlive-full

# Create production user
sudo useradd -m -s /bin/bash studyguide
sudo su - studyguide

# Clone and install
git clone https://github.com/studyguide-generator/studyguide-generator.git
cd studyguide-generator
python3.11 -m venv venv
source venv/bin/activate
pip install -e .

# Initialize with production profile
studyguide init --profile production
```

## Hardware Configurations

### Minimal Configuration (8GB RAM, 4-core CPU)
```yaml
# Automatically detected and configured
hardware_profile: MINIMAL
processing_strategy: conservative
quality_settings: basic
concurrent_jobs: 1
agent_timeouts: extended
```

### Standard Configuration (16GB RAM, 8-core CPU)
```yaml
hardware_profile: STANDARD
processing_strategy: balanced
quality_settings: standard
concurrent_jobs: 2
agent_timeouts: standard
```

### Performance Configuration (32GB+ RAM, 12+ core CPU)
```yaml
hardware_profile: PERFORMANCE
processing_strategy: aggressive
quality_settings: high
concurrent_jobs: 4
agent_timeouts: optimized
```

### Enterprise Configuration (64GB+ RAM, 16+ core CPU, GPU)
```yaml
hardware_profile: ENTERPRISE
processing_strategy: maximum
quality_settings: premium
concurrent_jobs: 8
gpu_acceleration: enabled
```

## Installation Methods

### Method 1: Package Installation (Recommended)

```bash
# Install from PyPI (when available)
pip install studyguide-generator

# Or install from source
pip install git+https://github.com/studyguide-generator/studyguide-generator.git
```

### Method 2: Source Installation

```bash
git clone https://github.com/studyguide-generator/studyguide-generator.git
cd studyguide-generator
pip install -e .
```

### Method 3: Docker Installation

```bash
# Pull pre-built image
docker pull studyguide-generator:latest

# Or build from source
docker build -t studyguide-generator .
```

## Configuration Management

### Configuration Files

The system uses a hierarchical configuration system:

1. **Default Configuration** (`src/config/default.yaml`)
2. **Hardware Profile** (automatically selected)
3. **Environment Configuration** (`config/production.yaml`, `config/development.yaml`)
4. **User Configuration** (`~/.studyguide/config.yaml`)
5. **Environment Variables** (highest priority)

### Environment Variables

Key environment variables for deployment:

```bash
# Database configuration
export STUDYGUIDE_DB_PATH="/var/lib/studyguide/jobs.db"

# Data directory
export STUDYGUIDE_DATA_DIR="/var/lib/studyguide/data"

# Logging configuration
export STUDYGUIDE_LOG_LEVEL="INFO"
export STUDYGUIDE_LOG_FILE="/var/log/studyguide/app.log"

# Resource limits
export STUDYGUIDE_MAX_MEMORY_GB="8"
export STUDYGUIDE_MAX_CPU_CORES="4"

# Ollama configuration
export OLLAMA_HOST="http://localhost:11434"
export OLLAMA_MODELS_PATH="/var/lib/ollama/models"
```

### Configuration Validation

```bash
# Validate current configuration
studyguide config validate

# Show effective configuration
studyguide config show --effective

# Test system with current configuration
studyguide config test
```

## Production Deployment

### System Service Setup

Create a systemd service for production deployment:

```bash
# Create service file
sudo tee /etc/systemd/system/studyguide.service << EOF
[Unit]
Description=Study Guide Generator Service
After=network.target

[Service]
Type=simple
User=studyguide
Group=studyguide
WorkingDirectory=/home/<USER>/studyguide-generator
Environment=PATH=/home/<USER>/studyguide-generator/venv/bin
ExecStart=/home/<USER>/studyguide-generator/venv/bin/studyguide server --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/lib/studyguide /var/log/studyguide

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable studyguide
sudo systemctl start studyguide
```

### Reverse Proxy Setup (Nginx)

```nginx
# /etc/nginx/sites-available/studyguide
server {
    listen 80;
    server_name studyguide.example.com;
    
    # Redirect to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name studyguide.example.com;
    
    # SSL configuration
    ssl_certificate /etc/ssl/certs/studyguide.crt;
    ssl_certificate_key /etc/ssl/private/studyguide.key;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Main application
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts for long-running requests
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;
    }
    
    # Static files
    location /static/ {
        alias /home/<USER>/studyguide-generator/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # File uploads
    client_max_body_size 100M;
}
```

### Database Setup

```bash
# Create database directory
sudo mkdir -p /var/lib/studyguide
sudo chown studyguide:studyguide /var/lib/studyguide

# Initialize database
sudo -u studyguide studyguide db init

# Set up database backups
sudo tee /etc/cron.d/studyguide-backup << EOF
# Backup database daily at 2 AM
0 2 * * * studyguide /home/<USER>/studyguide-generator/scripts/backup-db.sh
EOF
```

### Log Management

```bash
# Create log directory
sudo mkdir -p /var/log/studyguide
sudo chown studyguide:studyguide /var/log/studyguide

# Configure log rotation
sudo tee /etc/logrotate.d/studyguide << EOF
/var/log/studyguide/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 studyguide studyguide
    postrotate
        systemctl reload studyguide
    endscript
}
EOF
```

## Docker Deployment

### Dockerfile

```dockerfile
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pandoc \
    texlive-latex-base \
    texlive-latex-extra \
    texlive-fonts-recommended \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create application user
RUN useradd -m -s /bin/bash studyguide

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ src/
COPY setup.py .
RUN pip install -e .

# Create data directories
RUN mkdir -p /data /logs && chown -R studyguide:studyguide /data /logs

# Switch to application user
USER studyguide

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD studyguide status || exit 1

# Start application
CMD ["studyguide", "server", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  studyguide:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - studyguide_data:/data
      - studyguide_logs:/logs
      - ./config:/app/config:ro
    environment:
      - STUDYGUIDE_DATA_DIR=/data
      - STUDYGUIDE_LOG_FILE=/logs/app.log
      - STUDYGUIDE_DB_PATH=/data/jobs.db
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "studyguide", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl:ro
    depends_on:
      - studyguide
    restart: unless-stopped

volumes:
  studyguide_data:
  studyguide_logs:
  ollama_data:
```

### Container Orchestration (Kubernetes)

```yaml
# studyguide-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: studyguide
  labels:
    app: studyguide
spec:
  replicas: 2
  selector:
    matchLabels:
      app: studyguide
  template:
    metadata:
      labels:
        app: studyguide
    spec:
      containers:
      - name: studyguide
        image: studyguide-generator:latest
        ports:
        - containerPort: 8000
        env:
        - name: STUDYGUIDE_DATA_DIR
          value: "/data"
        - name: STUDYGUIDE_DB_PATH
          value: "/data/jobs.db"
        volumeMounts:
        - name: data-volume
          mountPath: /data
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "8Gi"
            cpu: "2000m"
        livenessProbe:
          exec:
            command:
            - studyguide
            - status
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: studyguide-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: studyguide-service
spec:
  selector:
    app: studyguide
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
```

## Monitoring & Maintenance

### Health Monitoring

```bash
# Check system status
studyguide status

# Monitor resource usage
studyguide monitor --interval 5

# View recent logs
studyguide logs --tail 100

# Check job queue
studyguide jobs list --state pending
```

### Performance Monitoring

```bash
# Run performance benchmarks
studyguide benchmark --output /var/log/studyguide/benchmark.json

# Monitor metrics
studyguide metrics export --format prometheus

# Start monitoring dashboard
studyguide dashboard --host 0.0.0.0 --port 8080
```

### Maintenance Tasks

```bash
# Database maintenance
studyguide db vacuum
studyguide db backup --output /backup/studyguide-$(date +%Y%m%d).db

# Clean up old jobs
studyguide jobs cleanup --older-than 30d

# Update models
ollama pull llama3:8b-instruct
studyguide config reload
```

## Troubleshooting

### Common Issues

1. **Out of Memory Errors**
   ```bash
   # Check memory usage
   studyguide status --verbose
   
   # Reduce concurrent jobs
   studyguide config set processing.max_concurrent_jobs 1
   ```

2. **Model Loading Failures**
   ```bash
   # Check Ollama status
   ollama list
   
   # Restart Ollama service
   sudo systemctl restart ollama
   ```

3. **PDF Generation Issues**
   ```bash
   # Check Pandoc installation
   pandoc --version
   
   # Test LaTeX installation
   pdflatex --version
   ```

### Log Analysis

```bash
# View error logs
grep ERROR /var/log/studyguide/app.log

# Monitor real-time logs
tail -f /var/log/studyguide/app.log

# Analyze performance logs
studyguide logs analyze --performance
```

## Performance Optimization

### Hardware Optimization

1. **SSD Storage**: Use NVMe SSD for database and temporary files
2. **Memory**: Allocate sufficient RAM for model loading and document processing
3. **CPU**: Multi-core processors improve parallel processing
4. **GPU**: Optional but significantly improves image generation and some ML tasks

### Configuration Tuning

```yaml
# High-performance configuration
processing:
  max_concurrent_jobs: 4
  agent_timeout_seconds: 300
  chunk_size: 1000
  overlap_size: 200

models:
  llm_model: "llama3:70b-instruct"  # Higher quality but slower
  embedding_model: "nomic-embed-text"
  
resources:
  max_memory_gb: 16
  max_cpu_cores: 8
  gpu_enabled: true

quality:
  processing_quality: "high"
  image_generation_steps: 50
  pdf_quality: "high"
```

### Scaling Considerations

1. **Horizontal Scaling**: Deploy multiple instances behind a load balancer
2. **Database Scaling**: Use PostgreSQL for multi-instance deployments
3. **Model Caching**: Share model cache across instances
4. **Queue Management**: Implement Redis-based job queue for distributed processing

This deployment guide provides comprehensive instructions for setting up the Study Guide Generator system in various environments. Choose the deployment method that best fits your requirements and infrastructure.
