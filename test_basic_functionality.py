#!/usr/bin/env python3
"""
Basic functionality test for Study Guide Generator

This script tests the core components without complex dependencies
to verify the basic system is working.
"""

import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """Test basic imports"""
    print("🧪 Testing Basic Imports...")
    
    try:
        from models.job_manifest import JobManifest, DocumentMetadata, JobState
        print("✅ JobManifest imports successful")
    except Exception as e:
        print(f"❌ JobManifest imports failed: {e}")
        return False
    
    try:
        from config.system_config import SystemConfiguration, HardwareProfile
        print("✅ SystemConfiguration imports successful")
    except Exception as e:
        print(f"❌ SystemConfiguration imports failed: {e}")
        return False
    
    return True

def test_job_manifest():
    """Test JobManifest functionality"""
    print("\n🧪 Testing JobManifest...")
    
    try:
        from models.job_manifest import JobManifest, DocumentMetadata, JobState
        
        # Create test document
        from datetime import datetime
        doc = DocumentMetadata(
            file_path="/tmp/test.pdf",
            original_filename="test.pdf",
            file_size=1024,
            checksum_sha256="abc123def456",
            mime_type="application/pdf",
            upload_timestamp=datetime.now()
        )
        
        # Create job manifest
        job = JobManifest(
            documents=[doc]
        )
        
        print(f"✅ Created job: {job.job_id}")
        print(f"✅ Job state: {job.current_state.value}")
        print(f"✅ Documents count: {len(job.documents)}")
        print(f"✅ Processing stages: {len(job.processing_stages)}")

        # Test serialization
        job_dict = job.model_dump()
        job_restored = JobManifest.model_validate(job_dict)
        
        if job_restored.job_id == job.job_id:
            print("✅ Serialization/deserialization successful")
        else:
            print("❌ Serialization/deserialization failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ JobManifest test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_config():
    """Test SystemConfig functionality"""
    print("\n🧪 Testing SystemConfig...")
    
    try:
        from config.system_config import SystemConfiguration, HardwareProfile

        # Create system config
        config = SystemConfiguration()
        
        print(f"✅ Hardware profile: {config.hardware_profile.value}")
        print(f"✅ System info available: {bool(config.system_info)}")
        print(f"✅ Config loaded: {bool(config.config)}")

        # Test hardware detection
        system_info = config.system_info
        print(f"✅ System info: CPU cores={system_info.get('cpu_cores')}, Memory GB={system_info.get('memory_gb')}")
        
        return True
        
    except Exception as e:
        print(f"❌ SystemConfig test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_models():
    """Test database models"""
    print("\n🧪 Testing Database Models...")
    
    try:
        from database.models import JobRepository
        import tempfile
        import asyncio
        
        async def test_db():
            # Create temporary database
            with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
                db_path = tmp.name
            
            try:
                # Initialize repository
                repo = JobRepository(db_path)
                await repo.initialize()
                
                # Create test job
                from models.job_manifest import JobManifest, DocumentMetadata
                
                doc = DocumentMetadata(
                    filename="test.pdf",
                    file_path="/tmp/test.pdf",
                    file_size=1024,
                    mime_type="application/pdf",
                    checksum="abc123"
                )
                
                job = JobManifest.create_new(
                    documents=[doc],
                    title="Database Test Job"
                )
                
                # Save job
                await repo.save_job(job)
                print("✅ Job saved to database")
                
                # Retrieve job
                retrieved_job = await repo.get_job(job.job_id)
                if retrieved_job and retrieved_job.job_id == job.job_id:
                    print("✅ Job retrieved from database")
                else:
                    print("❌ Job retrieval failed")
                    return False
                
                # List jobs
                jobs = await repo.get_recent_jobs(limit=10)
                if len(jobs) >= 1:
                    print(f"✅ Listed {len(jobs)} jobs from database")
                else:
                    print("❌ Job listing failed")
                    return False
                
                return True
                
            finally:
                # Clean up
                if os.path.exists(db_path):
                    os.unlink(db_path)
        
        # Run async test
        result = asyncio.run(test_db())
        return result
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_resource_manager():
    """Test resource manager"""
    print("\n🧪 Testing Resource Manager...")
    
    try:
        from core.resource_manager import ResourceManager
        
        # Create resource manager
        manager = ResourceManager()
        
        # Get current metrics
        metrics = manager.get_current_metrics()
        if metrics:
            print(f"✅ Current CPU: {metrics.cpu_percent:.1f}%")
            print(f"✅ Current Memory: {metrics.memory_percent:.1f}%")
            print(f"✅ Available Memory: {metrics.available_memory_gb:.1f} GB")
        else:
            print("❌ Failed to get resource metrics")
            return False
        
        # Get resource status
        status = manager.get_resource_status()
        print(f"✅ Resource status: {len(status)} resources monitored")
        
        return True
        
    except Exception as e:
        print(f"❌ Resource manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Study Guide Generator - Basic Functionality Test")
    print("=" * 60)
    
    tests = [
        ("Basic Imports", test_imports),
        ("JobManifest", test_job_manifest),
        ("SystemConfig", test_system_config),
        ("Database Models", test_database_models),
        ("Resource Manager", test_resource_manager),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Basic functionality is working.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
