#!/usr/bin/env python3
"""
System Status Dashboard for Study Guide Generator

This script provides a comprehensive status check of all system components
and generates a detailed report of the system's health and capabilities.
"""

import sys
import os
import json
import subprocess
from pathlib import Path
from datetime import datetime
import psutil
import requests

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def check_python_environment():
    """Check Python environment and virtual environment status"""
    status = {
        "python_version": sys.version,
        "python_executable": sys.executable,
        "virtual_env": os.environ.get('VIRTUAL_ENV'),
        "python_path": sys.path[:3]  # First 3 entries
    }
    
    # Check if we're in a virtual environment
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    status["in_virtual_env"] = in_venv
    
    return status

def check_system_dependencies():
    """Check system-level dependencies"""
    dependencies = {}
    
    # Check Pandoc
    try:
        result = subprocess.run(['pandoc', '--version'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            dependencies['pandoc'] = {
                'status': 'available',
                'version': version_line,
                'path': subprocess.run(['which', 'pandoc'], capture_output=True, text=True).stdout.strip()
            }
        else:
            dependencies['pandoc'] = {'status': 'error', 'error': result.stderr}
    except Exception as e:
        dependencies['pandoc'] = {'status': 'not_found', 'error': str(e)}
    
    # Check LaTeX
    try:
        result = subprocess.run(['pdflatex', '--version'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            dependencies['latex'] = {
                'status': 'available',
                'version': version_line,
                'path': subprocess.run(['which', 'pdflatex'], capture_output=True, text=True).stdout.strip()
            }
        else:
            dependencies['latex'] = {'status': 'error', 'error': result.stderr}
    except Exception as e:
        dependencies['latex'] = {'status': 'not_found', 'error': str(e)}
    
    # Check Ollama
    try:
        result = subprocess.run(['ollama', '--version'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            dependencies['ollama'] = {
                'status': 'available',
                'version': result.stdout.strip(),
                'path': subprocess.run(['which', 'ollama'], capture_output=True, text=True).stdout.strip()
            }
        else:
            dependencies['ollama'] = {'status': 'error', 'error': result.stderr}
    except Exception as e:
        dependencies['ollama'] = {'status': 'not_found', 'error': str(e)}
    
    return dependencies

def check_ollama_models():
    """Check Ollama models and service status"""
    ollama_status = {}
    
    try:
        # Check if Ollama service is running
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models_data = response.json()
            models = models_data.get('models', [])
            
            ollama_status['service'] = 'running'
            ollama_status['models_count'] = len(models)
            ollama_status['models'] = []
            
            required_models = ['llama3:8b', 'nomic-embed-text']
            
            for model in models:
                model_info = {
                    'name': model['name'],
                    'size': model.get('size', 0),
                    'modified': model.get('modified_at', ''),
                    'required': model['name'] in required_models or any(req in model['name'] for req in required_models)
                }
                ollama_status['models'].append(model_info)
            
            # Check for required models
            available_model_names = [m['name'] for m in models]
            ollama_status['required_models_available'] = {
                'llama3:8b': any('llama3:8b' in name for name in available_model_names),
                'nomic-embed-text': any('nomic-embed-text' in name for name in available_model_names)
            }
            
        else:
            ollama_status['service'] = 'error'
            ollama_status['error'] = f"HTTP {response.status_code}"
            
    except requests.exceptions.ConnectionError:
        ollama_status['service'] = 'not_running'
        ollama_status['error'] = 'Connection refused - service not running'
    except Exception as e:
        ollama_status['service'] = 'error'
        ollama_status['error'] = str(e)
    
    return ollama_status

def check_python_packages():
    """Check Python package dependencies"""
    packages = {}
    
    # Core packages
    core_packages = [
        'pydantic', 'click', 'psutil', 'requests', 'weasyprint',
        'aiosqlite', 'asyncio', 'pathlib', 'datetime'
    ]
    
    # ML packages (optional)
    ml_packages = [
        'torch', 'transformers', 'sentence-transformers', 'chromadb',
        'numpy', 'pandas', 'scikit-learn'
    ]
    
    for package in core_packages + ml_packages:
        try:
            if package in ['asyncio', 'pathlib', 'datetime']:
                # Built-in modules
                __import__(package)
                packages[package] = {'status': 'available', 'version': 'built-in', 'required': package in core_packages}
            else:
                module = __import__(package)
                version = getattr(module, '__version__', 'unknown')
                packages[package] = {'status': 'available', 'version': version, 'required': package in core_packages}
        except ImportError:
            packages[package] = {'status': 'not_installed', 'required': package in core_packages}
        except Exception as e:
            packages[package] = {'status': 'error', 'error': str(e), 'required': package in core_packages}
    
    return packages

def check_system_resources():
    """Check system resource availability"""
    resources = {}
    
    # CPU information
    resources['cpu'] = {
        'logical_cores': psutil.cpu_count(logical=True),
        'physical_cores': psutil.cpu_count(logical=False),
        'current_usage': psutil.cpu_percent(interval=1)
    }
    
    # Memory information
    memory = psutil.virtual_memory()
    resources['memory'] = {
        'total_gb': round(memory.total / (1024**3), 2),
        'available_gb': round(memory.available / (1024**3), 2),
        'used_percent': memory.percent
    }
    
    # Disk information
    disk = psutil.disk_usage('/')
    resources['disk'] = {
        'total_gb': round(disk.total / (1024**3), 2),
        'free_gb': round(disk.free / (1024**3), 2),
        'used_percent': round((disk.used / disk.total) * 100, 1)
    }
    
    return resources

def check_core_modules():
    """Check core application modules"""
    modules = {}
    
    core_modules = [
        'models.job_manifest',
        'config.system_config',
        'core.orchestrator',
        'agents.base_agent'
    ]
    
    for module_name in core_modules:
        try:
            module = __import__(module_name, fromlist=[''])
            modules[module_name] = {'status': 'available', 'path': getattr(module, '__file__', 'unknown')}
        except ImportError as e:
            modules[module_name] = {'status': 'import_error', 'error': str(e)}
        except Exception as e:
            modules[module_name] = {'status': 'error', 'error': str(e)}
    
    return modules

def generate_status_report():
    """Generate comprehensive status report"""
    print("🔍 Study Guide Generator - System Status Report")
    print("=" * 70)
    print(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Python Environment
    print("🐍 Python Environment")
    print("-" * 30)
    py_env = check_python_environment()
    print(f"Version: {py_env['python_version'].split()[0]}")
    print(f"Virtual Environment: {'✅ Active' if py_env['in_virtual_env'] else '❌ Not Active'}")
    if py_env['virtual_env']:
        print(f"Virtual Env Path: {py_env['virtual_env']}")
    print()
    
    # System Dependencies
    print("🔧 System Dependencies")
    print("-" * 30)
    deps = check_system_dependencies()
    for dep_name, dep_info in deps.items():
        if dep_info['status'] == 'available':
            print(f"{dep_name}: ✅ {dep_info['version']}")
        elif dep_info['status'] == 'not_found':
            print(f"{dep_name}: ❌ Not installed")
        else:
            print(f"{dep_name}: ⚠️  Error - {dep_info.get('error', 'Unknown error')}")
    print()
    
    # Ollama Status
    print("🤖 Ollama Service & Models")
    print("-" * 30)
    ollama = check_ollama_models()
    if ollama.get('service') == 'running':
        print(f"Service: ✅ Running ({ollama['models_count']} models)")
        required = ollama.get('required_models_available', {})
        print(f"LLaMA 3 8B: {'✅' if required.get('llama3:8b') else '❌'}")
        print(f"Nomic Embed: {'✅' if required.get('nomic-embed-text') else '❌'}")
    else:
        print(f"Service: ❌ {ollama.get('error', 'Not running')}")
    print()
    
    # Python Packages
    print("📦 Python Packages")
    print("-" * 30)
    packages = check_python_packages()
    
    # Core packages
    core_available = sum(1 for p, info in packages.items() if info.get('required') and info['status'] == 'available')
    core_total = sum(1 for p, info in packages.items() if info.get('required'))
    print(f"Core Packages: {core_available}/{core_total} available")
    
    # ML packages
    ml_available = sum(1 for p, info in packages.items() if not info.get('required') and info['status'] == 'available')
    ml_total = sum(1 for p, info in packages.items() if not info.get('required'))
    print(f"ML Packages: {ml_available}/{ml_total} available")
    
    # Show missing required packages
    missing_required = [p for p, info in packages.items() if info.get('required') and info['status'] != 'available']
    if missing_required:
        print(f"Missing Required: {', '.join(missing_required)}")
    print()
    
    # System Resources
    print("💻 System Resources")
    print("-" * 30)
    resources = check_system_resources()
    print(f"CPU: {resources['cpu']['logical_cores']} cores ({resources['cpu']['current_usage']:.1f}% used)")
    print(f"Memory: {resources['memory']['available_gb']:.1f}GB available / {resources['memory']['total_gb']:.1f}GB total")
    print(f"Disk: {resources['disk']['free_gb']:.1f}GB free / {resources['disk']['total_gb']:.1f}GB total")
    print()
    
    # Core Modules
    print("🏗️  Core Application Modules")
    print("-" * 30)
    modules = check_core_modules()
    for module_name, module_info in modules.items():
        status_icon = "✅" if module_info['status'] == 'available' else "❌"
        print(f"{module_name}: {status_icon} {module_info['status']}")
    print()
    
    # Overall Assessment
    print("📊 Overall System Assessment")
    print("-" * 30)
    
    # Calculate readiness score
    scores = {
        'python_env': 100 if py_env['in_virtual_env'] else 50,
        'system_deps': sum(100 if info['status'] == 'available' else 0 for info in deps.values()) / len(deps),
        'ollama': 100 if ollama.get('service') == 'running' else 0,
        'core_packages': (core_available / core_total) * 100 if core_total > 0 else 0,
        'core_modules': sum(100 if info['status'] == 'available' else 0 for info in modules.values()) / len(modules)
    }
    
    overall_score = sum(scores.values()) / len(scores)
    
    print(f"System Readiness: {overall_score:.1f}%")
    
    if overall_score >= 80:
        print("🎉 System is ready for production use!")
    elif overall_score >= 60:
        print("⚠️  System is partially ready - some features may be limited")
    else:
        print("❌ System needs attention before use")
    
    print()
    print("🚀 Ready to process documents and generate study guides!")
    
    return overall_score

if __name__ == "__main__":
    generate_status_report()
