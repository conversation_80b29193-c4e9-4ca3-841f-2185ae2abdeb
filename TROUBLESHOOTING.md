# Troubleshooting Guide

This guide helps diagnose and resolve common issues with the Study Guide Generator system.

## Table of Contents

1. [Quick Diagnostics](#quick-diagnostics)
2. [Installation Issues](#installation-issues)
3. [Runtime Errors](#runtime-errors)
4. [Performance Issues](#performance-issues)
5. [Configuration Problems](#configuration-problems)
6. [Model and AI Issues](#model-and-ai-issues)
7. [System Resource Issues](#system-resource-issues)
8. [Logging and Debugging](#logging-and-debugging)

## Quick Diagnostics

### System Health Check

```bash
# Run comprehensive system check
studyguide status --verbose

# Check configuration validity
studyguide config validate

# Test system components
studyguide test --all
```

### Common Status Indicators

- ✅ **AVAILABLE**: Component is working normally
- ⚠️ **LIMITED**: Component has reduced functionality
- 🔶 **CRITICAL**: Component has significant issues
- ❌ **EXHAUSTED**: Component is unavailable

## Installation Issues

### Python Environment Issues

**Problem**: `ModuleNotFoundError` or import errors
```bash
# Solution: Verify Python environment
python --version  # Should be 3.9+
pip list | grep studyguide

# Reinstall if necessary
pip uninstall studyguide-generator
pip install -e .
```

**Problem**: Permission errors during installation
```bash
# Solution: Use virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -e .
```

### System Dependencies

**Problem**: Pandoc not found
```bash
# Ubuntu/Debian
sudo apt-get install pandoc

# macOS
brew install pandoc

# Windows: Download from https://pandoc.org/installing.html
```

**Problem**: LaTeX errors during PDF generation
```bash
# Install full LaTeX distribution
# Ubuntu/Debian
sudo apt-get install texlive-full

# macOS
brew install --cask mactex

# Test LaTeX installation
pdflatex --version
```

### Ollama Setup Issues

**Problem**: Ollama service not running
```bash
# Check Ollama status
ollama list

# Start Ollama service
ollama serve

# Pull required models
ollama pull llama3:8b-instruct
ollama pull nomic-embed-text
```

**Problem**: Model download failures
```bash
# Check disk space
df -h

# Check network connectivity
curl -I https://ollama.ai

# Manual model download
ollama pull llama3:8b-instruct --verbose
```

## Runtime Errors

### Job Processing Failures

**Problem**: Jobs stuck in "PENDING" state
```bash
# Check system resources
studyguide status

# Check job queue
studyguide jobs list --state pending

# Restart job processing
studyguide jobs restart <job-id>
```

**Problem**: Agent timeout errors
```bash
# Increase timeout in configuration
studyguide config set agents.timeout_seconds 600

# Check resource availability
studyguide monitor --interval 1
```

**Problem**: Database connection errors
```bash
# Check database file permissions
ls -la ~/.studyguide/jobs.db

# Reinitialize database if corrupted
studyguide db backup
studyguide db init --force
```

### Document Processing Errors

**Problem**: OCR failures on scanned documents
```bash
# Check PaddleOCR installation
python -c "import paddleocr; print('OCR available')"

# Test with different image formats
studyguide test ocr --input test_image.png
```

**Problem**: PDF extraction errors
```bash
# Check PyMuPDF installation
python -c "import fitz; print('PyMuPDF available')"

# Test PDF processing
studyguide test pdf --input test_document.pdf
```

## Performance Issues

### Slow Processing

**Problem**: Very slow document processing
```bash
# Check hardware profile
studyguide config show | grep hardware_profile

# Optimize for your hardware
studyguide config optimize --hardware auto

# Monitor resource usage
studyguide monitor --show-bottlenecks
```

**Problem**: High memory usage
```bash
# Check memory consumption
studyguide status --memory

# Reduce concurrent jobs
studyguide config set processing.max_concurrent_jobs 1

# Enable memory optimization
studyguide config set optimization.memory_efficient true
```

**Problem**: CPU bottlenecks
```bash
# Check CPU usage
studyguide monitor --cpu

# Adjust processing strategy
studyguide config set processing.strategy conservative

# Reduce model complexity
studyguide config set models.llm_model "llama3:8b-instruct"
```

### GPU Issues

**Problem**: GPU not detected
```bash
# Check GPU availability
nvidia-smi  # For NVIDIA GPUs

# Verify PyTorch GPU support
python -c "import torch; print(torch.cuda.is_available())"

# Enable GPU in configuration
studyguide config set hardware.gpu_enabled true
```

**Problem**: GPU memory errors
```bash
# Check GPU memory usage
nvidia-smi

# Reduce GPU batch size
studyguide config set models.gpu_batch_size 1

# Enable GPU memory optimization
studyguide config set optimization.gpu_memory_efficient true
```

## Configuration Problems

### Invalid Configuration

**Problem**: Configuration validation errors
```bash
# Show configuration issues
studyguide config validate --verbose

# Reset to defaults
studyguide config reset

# Import known good configuration
studyguide config import backup_config.yaml
```

**Problem**: Environment variable conflicts
```bash
# Show effective configuration
studyguide config show --effective

# Clear environment variables
unset STUDYGUIDE_*

# Restart with clean environment
studyguide status
```

### Hardware Profile Issues

**Problem**: Incorrect hardware detection
```bash
# Show detected hardware
studyguide config show --hardware

# Manually set hardware profile
studyguide config set hardware.profile PERFORMANCE

# Test with new profile
studyguide test --hardware
```

## Model and AI Issues

### LLM Connection Problems

**Problem**: Cannot connect to Ollama
```bash
# Check Ollama service
curl http://localhost:11434/api/tags

# Restart Ollama
sudo systemctl restart ollama

# Check firewall settings
sudo ufw status
```

**Problem**: Model loading failures
```bash
# Check available models
ollama list

# Verify model integrity
ollama show llama3:8b-instruct

# Re-download if corrupted
ollama rm llama3:8b-instruct
ollama pull llama3:8b-instruct
```

### Embedding Issues

**Problem**: Vector database errors
```bash
# Check ChromaDB installation
python -c "import chromadb; print('ChromaDB available')"

# Clear vector database
rm -rf ~/.studyguide/vectordb/

# Reinitialize embeddings
studyguide db init-vectors
```

**Problem**: Embedding model failures
```bash
# Test embedding model
studyguide test embeddings --text "test sentence"

# Switch to alternative model
studyguide config set models.embedding_model "all-MiniLM-L6-v2"
```

## System Resource Issues

### Disk Space Problems

**Problem**: Insufficient disk space
```bash
# Check disk usage
df -h
du -sh ~/.studyguide/

# Clean up old jobs
studyguide jobs cleanup --older-than 7d

# Clean up temporary files
studyguide cleanup --temp-files
```

**Problem**: Database size issues
```bash
# Check database size
ls -lh ~/.studyguide/jobs.db

# Vacuum database
studyguide db vacuum

# Archive old jobs
studyguide db archive --older-than 30d
```

### Memory Issues

**Problem**: Out of memory errors
```bash
# Check memory usage
free -h
studyguide status --memory

# Enable swap if needed
sudo swapon --show
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# Optimize memory usage
studyguide config set optimization.low_memory_mode true
```

### Network Issues

**Problem**: Model download timeouts
```bash
# Check network connectivity
ping ollama.ai

# Use proxy if needed
export HTTP_PROXY=http://proxy.example.com:8080
export HTTPS_PROXY=http://proxy.example.com:8080

# Increase timeout
studyguide config set network.timeout_seconds 300
```

## Logging and Debugging

### Enable Debug Logging

```bash
# Enable verbose logging
studyguide config set logging.level DEBUG

# Enable component-specific logging
studyguide config set logging.agents.debug true
studyguide config set logging.database.debug true

# View logs in real-time
studyguide logs --follow
```

### Log Analysis

```bash
# Search for errors
studyguide logs --grep ERROR --last 24h

# Analyze performance
studyguide logs --performance --output performance.json

# Export logs for support
studyguide logs export --format json --output debug_logs.json
```

### Debug Mode

```bash
# Run in debug mode
studyguide --debug create --title "Debug Test" --input test.pdf

# Enable profiling
studyguide --profile create --title "Profile Test" --input test.pdf

# Generate debug report
studyguide debug-report --output debug_report.zip
```

## Getting Help

### Collect System Information

```bash
# Generate comprehensive system report
studyguide debug-report --include-logs --include-config --output system_report.zip
```

### Community Support

1. **GitHub Issues**: https://github.com/studyguide-generator/studyguide-generator/issues
2. **Documentation**: https://studyguide-generator.readthedocs.io/
3. **Community Forum**: https://community.studyguide-generator.com/

### Professional Support

For enterprise deployments and professional support:
- Email: <EMAIL>
- Documentation: https://docs.studyguide-generator.com/enterprise/

### Reporting Bugs

When reporting issues, please include:

1. **System Information**:
   ```bash
   studyguide --version
   python --version
   uname -a  # Linux/macOS
   ```

2. **Configuration**:
   ```bash
   studyguide config show --sanitized
   ```

3. **Error Logs**:
   ```bash
   studyguide logs --grep ERROR --last 1h
   ```

4. **Steps to Reproduce**: Detailed steps that led to the issue

5. **Expected vs Actual Behavior**: What you expected to happen vs what actually happened

This troubleshooting guide covers the most common issues. If you encounter problems not covered here, please check the documentation or reach out to the community for support.
